package com.jusha.auth.common.aspectj;

import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.ServletUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Set;

/**
 * 权限处理切面处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class HasPermissionsAspect {
    private static final Logger log = LoggerFactory.getLogger(HasPermissionsAspect.class);

    @Autowired
    private TokenService tokenService;

    @Value(value = "${server.servlet.context-path}")
    private String contextPath;

    @Before("@annotation(hasPermissions)")
    public void doBefore(JoinPoint point, HasPermissions hasPermissions) throws Throwable {
        HttpServletRequest request = ServletUtils.getRequest();
        LoginUser loginUser = tokenService.getLoginUser();
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions())) {
            throw new ServiceException(MessageUtils.message("set.permission.first"));
        }
        String uri = request.getRequestURI();
        Set<String> permissions = loginUser.getPermissions();
        log.info("当前请求接口路径是{}", uri);
        if (!permissions.contains(Constants.ALL_PERMISSION) && !permissions.contains(StringUtils.trim(uri).replace(contextPath,""))) {
            throw new ServiceException(MessageUtils.message("interface.permission.set"));
        }
    }

}
