package com.jusha.auth.system.controller;

import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.service.SysPasswordService;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.system.domain.PasswordReset;
import com.jusha.auth.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController {
    @Autowired
    private ISysUserService iSysuserService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysPasswordService sysPasswordService;

    /**
     * 个人信息
     */
    @GetMapping
    public ResultBean profile() {
        LoginUser loginUser = getLoginUser();
        SysUser user = loginUser.getSysUser();
        ResultBean resultBean = ResultBean.success(user);
        resultBean.put("roleGroup", iSysuserService.selectUserRoleGroup(loginUser.getUsername()));
        return resultBean;
    }

    /**
     * 修改用户
     */
    @PostMapping(value = "/edit")
    public ResultBean updateProfile(@Validated @RequestBody SysUser user) {
        LoginUser loginUser = getLoginUser();
        SysUser currentUser = loginUser.getSysUser();
        currentUser.setNickName(user.getNickName());
        currentUser.setPhoneNumber(user.getPhoneNumber());
        if (StringUtils.isNotEmpty(user.getPhoneNumber()) && !iSysuserService.checkPhoneUnique(currentUser)) {
            return error(MessageUtils.message("user.phone.already.exist.edit"));
        }
        if (iSysuserService.updateUserProfile(currentUser)) {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error(MessageUtils.message("user.edit.exception"));
    }

    /**
     * 重置密码
     */
    @PostMapping(value = "/updatePwd")
    public ResultBean updatePwd(@RequestParam String newPassword,@RequestParam String oldPassword) throws Exception {
        PasswordReset passwordReset = new PasswordReset();
        passwordReset.setNewPassword(newPassword);
        passwordReset.setOldPassword(oldPassword);
        return iSysuserService.resetUserPwd(passwordReset);
    }
}
