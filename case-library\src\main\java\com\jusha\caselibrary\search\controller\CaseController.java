package com.jusha.caselibrary.search.controller;

import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.mybatisplus.entity.DepCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.search.service.CaseManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName CaseController
 * @Description 病例管理控制器
 * <AUTHOR>
 * @Date 2025/7/7 17:09
 **/
@Slf4j
@RestController
@Api(tags = "病例控制器")
@RequestMapping("/forbid/cases")
@RequiredArgsConstructor
public class CaseController {

    private final CaseManagementService caseManagementService;

    /**
     * 创建科室病例
     * 当调用此接口时，会自动触发@ESSync注解，将数据同步到ES
     */
    @PostMapping("/department/create")
    @ApiOperation(value = "创建科室病例")
    @ESSync(type = ESSync.SyncType.CREATE, indexType  = Constant.DEP_CASE_INDEX_NAME)
    public ResultBean<Long> createDepartmentCase(@RequestBody DepCase depCase) {
        try {
            Long caseId = caseManagementService.createDepartmentCase(depCase);
            if (caseId != null) {
                return ResultBean.success(caseId);
            } else {
                return ResultBean.error("科室病例创建失败");
            }
        } catch (Exception e) {
            log.error("创建科室病例异常", e);
            return ResultBean.error("创建科室病例异常：" + e.getMessage());
        }
    }

    /**
     * 更新科室病例
     * 当调用此接口时，会自动触发@ESSync注解，将更新同步到ES
     */
    @PostMapping("/department/update")
    @ApiOperation(value = "更新科室病例")
    public ResultBean<Long> updateDepartmentCase(@RequestBody DepCase depCase) {
        try {
            Long caseId = caseManagementService.updateDepartmentCase(depCase);
            if (caseId != null) {
                return ResultBean.success(caseId);
            } else {
                return ResultBean.error("科室病例更新失败");
            }
        } catch (Exception e) {
            log.error("更新科室病例异常", e);
            return ResultBean.error("更新科室病例异常：" + e.getMessage());
        }
    }

    /**
     * 删除科室病例
     * 当调用此接口时，会自动触发@ESSync注解，将删除同步到ES
     */
    @DeleteMapping("/department/delete")
    @ApiOperation(value = "删除科室病例")
    public ResponseEntity<String> deleteDepartmentCase(@RequestParam("caseId") Long caseId) {
        try {
            boolean success = caseManagementService.deleteDepartmentCase(caseId);
            if (success) {
                return ResponseEntity.ok("科室病例删除成功，已自动同步到ES");
            } else {
                return ResponseEntity.badRequest().body("科室病例删除失败");
            }
        } catch (Exception e) {
            log.error("删除科室病例异常", e);
            return ResponseEntity.ok().body("删除科室病例异常：" + e.getMessage());
        }
    }

    /**
     * 创建个人病例
     * 当调用此接口时，会自动触发@ESSync注解，将数据同步到ES
     */
    @PostMapping("/personal")
    @ApiOperation(value = "创建个人病例")
    public ResponseEntity<String> createPersonalCase(@RequestBody UserCase userCase) {
        try {
            boolean success = caseManagementService.createPersonalCase(userCase);
            if (success) {
                return ResponseEntity.ok("个人病例创建成功，已自动同步到ES");
            } else {
                return ResponseEntity.badRequest().body("个人病例创建失败");
            }
        } catch (Exception e) {
            log.error("创建个人病例异常", e);
            return ResponseEntity.ok().body("创建个人病例异常：" + e.getMessage());
        }
    }

    /**
     * 更新个人病例
     * 当调用此接口时，会自动触发@ESSync注解，将更新同步到ES
     */
    @PutMapping("/personal/{id}")
    @ApiOperation(value = "更新个人病例")
    public ResponseEntity<String> updatePersonalCase(@PathVariable Long id, @RequestBody UserCase userCase) {
        try {
            userCase.setCaseId(id);
            boolean success = caseManagementService.updatePersonalCase(userCase);
            if (success) {
                return ResponseEntity.ok("个人病例更新成功，已自动同步到ES");
            } else {
                return ResponseEntity.badRequest().body("个人病例更新失败");
            }
        } catch (Exception e) {
            log.error("更新个人病例异常", e);
            return ResponseEntity.ok().body("更新个人病例异常：" + e.getMessage());
        }
    }

    /**
     * 删除个人病例
     * 当调用此接口时，会自动触发@ESSync注解，将删除同步到ES
     */
    @DeleteMapping("/personal/{id}")
    @ApiOperation(value = "删除个人病例")
    public ResponseEntity<String> deletePersonalCase(@PathVariable Long id) {
        try {
            boolean success = caseManagementService.deletePersonalCase(id);
            if (success) {
                return ResponseEntity.ok("个人病例删除成功，已自动同步到ES");
            } else {
                return ResponseEntity.badRequest().body("个人病例删除失败");
            }
        } catch (Exception e) {
            log.error("删除个人病例异常", e);
            return ResponseEntity.ok().body("删除个人病例异常：" + e.getMessage());
        }
    }

    /**
     * 批量导入科室病例
     * 当调用此接口时，会自动触发@ESSync注解，异步批量同步到ES
     */
    @PostMapping("/department/batch")
    @ApiOperation(value = "批量导入科室病例")
    public ResponseEntity<String> batchImportDepartmentCases(@RequestBody List<DepCase> depCases) {
        try {
            boolean success = caseManagementService.batchImportDepartmentCases(depCases);
            if (success) {
                return ResponseEntity.ok("科室病例批量导入成功，已异步同步到ES，数量：" + depCases.size());
            } else {
                return ResponseEntity.badRequest().body("科室病例批量导入失败");
            }
        } catch (Exception e) {
            log.error("批量导入科室病例异常", e);
            return ResponseEntity.ok().body("批量导入科室病例异常：" + e.getMessage());
        }
    }

    /**
     * 批量导入个人病例
     * 当调用此接口时，会自动触发@ESSync注解，异步批量同步到ES
     */
    @PostMapping("/personal/batch")
    @ApiOperation(value = "批量导入个人病例")
    public ResponseEntity<String> batchImportPersonalCases(@RequestBody List<UserCase> userCases) {
        try {
            boolean success = caseManagementService.batchImportPersonalCases(userCases);
            if (success) {
                return ResponseEntity.ok("个人病例批量导入成功，已异步同步到ES，数量：" + userCases.size());
            } else {
                return ResponseEntity.badRequest().body("个人病例批量导入失败");
            }
        } catch (Exception e) {
            log.error("批量导入个人病例异常", e);
            return ResponseEntity.ok().body("批量导入个人病例异常：" + e.getMessage());
        }
    }
}
