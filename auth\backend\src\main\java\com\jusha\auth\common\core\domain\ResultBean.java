package com.jusha.auth.common.core.domain;

import com.jusha.auth.common.exception.HttpStatus;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;

import java.util.HashMap;
import java.util.Objects;

/**
 * 操作消息提醒
 * 
 * <AUTHOR>
 */
public class ResultBean extends HashMap<String, Object> {
    private static final long serialVersionUID = 1L;

    public static final String STATE_TAG = "state";

    /** 状态码 */
    public static final String CODE_TAG = "errorCode";

    /** 返回内容 */
    public static final String MSG_TAG = "message";

    /** 数据对象 */
    public static final String DATA_TAG = "data";

    /**
     * 初始化一个新创建的 ResultBean 对象，使其表示一个空消息。
     */
    public ResultBean()
    {
    }

    /**
     * 初始化一个新创建的 ResultBean 对象
     * 
     * @param code 状态码
     * @param msg 返回内容
     */
    public ResultBean(Boolean state, int code, String msg) {
        super.put(STATE_TAG, state);
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
    }

    /**
     * 初始化一个新创建的 ResultBean 对象
     * 
     * @param code 状态码
     * @param msg 返回内容
     * @param data 数据对象
     */
    public ResultBean(Boolean state, int code, String msg, Object data) {
        super.put(STATE_TAG, state);
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
        if (StringUtils.isNotNull(data)) {
            super.put(DATA_TAG, data);
        }
    }

    /**
     * 返回成功消息
     * 
     * @return 成功消息
     */
    public static ResultBean success() {
        return ResultBean.success(MessageUtils.message("operate.success"));
    }

    /**
     * 返回成功数据
     * 
     * @return 成功消息
     */
    public static ResultBean success(Object data) {
        return ResultBean.success(MessageUtils.message("operate.success"), data);
    }

    /**
     * 返回成功消息
     * 
     * @param msg 返回内容
     * @return 成功消息
     */
    public static ResultBean success(String msg) {
        return ResultBean.success(msg, null);
    }

    /**
     * 返回成功消息
     * 
     * @param msg 返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static ResultBean success(String msg, Object data) {
        return new ResultBean(true,HttpStatus.SUCCESS, msg, data);
    }

    /**
     * 返回错误消息
     * 
     * @return 错误消息
     */
    public static ResultBean error() {
        return ResultBean.error(MessageUtils.message("operate.fail"));
    }

    /**
     * 返回错误消息
     * 
     * @param msg 返回内容
     * @return 错误消息
     */
    public static ResultBean error(String msg) {
        return ResultBean.error(msg, null);
    }

    /**
     * 返回错误消息
     * 
     * @param msg 返回内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static ResultBean error(String msg, Object data) {
        return new ResultBean(false,HttpStatus.ERROR, msg, data);
    }

    /**
     * 返回错误消息
     * 
     * @param code 状态码
     * @param msg 返回内容
     * @return 错误消息
     */
    public static ResultBean error(int code, String msg) {
        return new ResultBean(false,code, msg, null);
    }

    /**
     * 是否为成功消息
     *
     * @return 结果
     */
    public boolean isSuccess() {
        return Objects.equals(HttpStatus.SUCCESS, this.get(CODE_TAG));
    }

    /**
     * 是否为错误消息
     *
     * @return 结果
     */
    public boolean isError()
    {
        return Objects.equals(HttpStatus.ERROR, this.get(CODE_TAG));
    }

    /**
     * 方便链式调用
     *
     * @param key 键
     * @param value 值
     * @return 数据对象
     */
    @Override
    public ResultBean put(String key, Object value) {
        super.put(key, value);
        return this;
    }
}
