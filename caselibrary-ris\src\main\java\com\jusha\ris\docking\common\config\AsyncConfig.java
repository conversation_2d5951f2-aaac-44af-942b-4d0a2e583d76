package com.jusha.ris.docking.common.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池配置
 **/
@Configuration
public class AsyncConfig {

    /**
     * 示例 线程池
     * @return
     */
    @Bean(name = "reportThreadPool")
    public ExecutorService demoThreadPool() {
        return Executors.newFixedThreadPool(10, new SelfDefFactory("reportThreadPool"));
    }


    
    /**
     * 自定义线程工厂
     */
    private class SelfDefFactory implements ThreadFactory {
        private final ThreadGroup group;
        private final String namePrefix;
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        SelfDefFactory(String threadPoolName) {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
            namePrefix = StringUtils.join(threadPoolName, "-thread-");
        }

        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r, namePrefix + threadNumber.getAndIncrement(), 0);
            if (t.isDaemon())
                t.setDaemon(false);
            if (t.getPriority() != Thread.NORM_PRIORITY)
                t.setPriority(Thread.NORM_PRIORITY);
            return t;
        }
    }

}