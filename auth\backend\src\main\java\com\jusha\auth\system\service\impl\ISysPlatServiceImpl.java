package com.jusha.auth.system.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.SysGroup;
import com.jusha.auth.mybatisplus.entity.SysPlat;
import com.jusha.auth.mybatisplus.service.SysPlatService;
import com.jusha.auth.system.service.ISysGroupService;
import com.jusha.auth.system.service.ISysMenuService;
import com.jusha.auth.system.service.ISysPlatService;
import com.jusha.auth.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 业务平台信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
@Service
public class ISysPlatServiceImpl implements ISysPlatService {

    @Autowired
    private SysPlatService sysPlatService;

    @Autowired
    private ISysGroupService iSysGroupService;

    @Autowired
    private ISysMenuService iSysMenuService;

    @Autowired
    private ISysRoleService iSysRoleService;

    @Autowired
    private TokenService tokenService;

    /**
     * 新增业务平台信息
     *
     * @param sysPlat 业务平台信息
     * @return 结果
     */
    @Override
    public ResultBean insertSysPlat(SysPlat sysPlat) {
        if (!checkPlatNameUnique(sysPlat)) {
            return ResultBean.error(MessageUtils.message("plat.already.exist.add"));
        }
        if (!checkPlatTagUnique(sysPlat)) {
            return ResultBean.error(MessageUtils.message("platTag.already.exist.add"));
        }
        //雪花生成id
        long platId = YitIdHelper.nextId();
        sysPlat.setPlatId(platId);
        //当前用户id作为创建者
        sysPlat.setCreateBy(tokenService.getUserId());
        //当前时间作为创建时间
        sysPlat.setCreateTime(DateUtils.getNowDate());

        boolean savePlat = sysPlatService.save(sysPlat);
        if (savePlat) {
            SysGroup group = new SysGroup();
            group.setGroupName(sysPlat.getPlatName());
            group.setPlatId(platId);
            return ResultBean.success(iSysGroupService.insertRootGroup(group));
        }
        return ResultBean.error();
    }

    /**
     * 查询业务平台信息列表
     *
     * @param sysPlat 业务平台信息
     * @return 业务平台信息
     */
    @Override
    public List<SysPlat> selectSysPlatList(SysPlat sysPlat) {
        LambdaQueryChainWrapper<SysPlat> wrapper = sysPlatService.lambdaQuery();
        if (sysPlat.getPlatName() != null) {
            wrapper.like(SysPlat::getPlatName, sysPlat.getPlatName());
        }
        if (sysPlat.getVisible() != null) {
            wrapper.eq(SysPlat::getVisible, sysPlat.getVisible());
        }
        if (sysPlat.getPlatTag() != null) {
            wrapper.eq(SysPlat::getPlatTag, sysPlat.getPlatTag());
        }
        wrapper.ne(SysPlat::getPlatId, 0L);
        return wrapper.orderByDesc(SysPlat::getCreateTime).list();
    }

    /**
     * 查询业务平台信息
     *
     * @param platId 业务平台信息主键
     * @return 业务平台信息
     */
    @Override
    public SysPlat selectSysPlatByPlatId(Long platId) {
        return sysPlatService.getById(platId);
    }

    /**
     * 修改业务平台信息
     *
     * @param sysPlat 业务平台信息
     * @return 结果
     */
    @Override
    public ResultBean updateSysPlat(SysPlat sysPlat) {
        if (!checkPlatNameUnique(sysPlat)) {
            return ResultBean.error(MessageUtils.message("plat.already.exist.edit"));
        }
        if (!checkPlatTagUnique(sysPlat)) {
            return ResultBean.error(MessageUtils.message("platTag.already.exist.edit"));
        }
        sysPlat.setUpdateTime(DateUtils.getNowDate());
        sysPlat.setUpdateBy(tokenService.getUserId());
        return ResultBean.success(sysPlatService.updateById(sysPlat));
    }


    /**
     * 批量删除业务平台信息
     *
     * @param platId 需要删除的业务平台信息主键
     * @return 结果
     */
    @Override
    public ResultBean deleteSysPlatByPlatId(Long platId) {
        //平台下有分组不能删除，平台下有菜单不能删除，平台下有角色不能删除
        if (platId == 0L) {
            return ResultBean.error(MessageUtils.message("plat.myself.delete"));
        }
        if (iSysGroupService.hasChildByPlatId(platId)) {
            return ResultBean.error(MessageUtils.message("plat.group.child.delete"));
        }
        if (iSysMenuService.hasChildByPlatId(platId)) {
            return ResultBean.error(MessageUtils.message("plat.menu.child.delete"));
        }
        if (iSysRoleService.hasChildByPlatId(platId)) {
            return ResultBean.error(MessageUtils.message("plat.role.child.delete"));
        }
        iSysGroupService.deleteGroupByPlatId(platId);
        return ResultBean.success(sysPlatService.lambdaUpdate()
                .set(SysPlat::getUpdateBy, tokenService.getUserId())
                .set(SysPlat::getUpdateTime, DateUtils.getNowDate())
                .set(SysPlat::getDelFlag, Constants.DELETE_FLAG)
                .eq(SysPlat::getPlatId, platId).update());
    }

    /**
     * 校验平台名称是否唯一
     *
     * @param sysPlat 平台信息
     * @return 结果
     */
    @Override
    public boolean checkPlatNameUnique(SysPlat sysPlat) {
        Long platId = StringUtils.isNull(sysPlat.getPlatId()) ? Constants.ALL_MINUS1L : sysPlat.getPlatId();
        List<SysPlat> playInfos = sysPlatService.lambdaQuery()
                .eq(SysPlat::getPlatName, sysPlat.getPlatName()).list();
        if (!playInfos.isEmpty() && playInfos.get(0).getPlatId().longValue() != platId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    public boolean checkPlatTagUnique(SysPlat sysPlat) {
        Long platId = StringUtils.isNull(sysPlat.getPlatId()) ? Constants.ALL_MINUS1L : sysPlat.getPlatId();
        List<SysPlat> playInfos = sysPlatService.lambdaQuery()
                .eq(SysPlat::getPlatTag, sysPlat.getPlatTag()).list();
        if (!playInfos.isEmpty() && playInfos.get(0).getPlatId().longValue() != platId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }
}
