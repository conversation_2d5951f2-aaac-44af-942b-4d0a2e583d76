package com.jusha.auth.common.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用成功标识
     */
    public static final Long ALL_MINUS1L = -1L;

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 通用删除标识
     */
    public static final String DELETE_FLAG = "1";


    public static final String SEPARATOR = ",";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS_MSG = "登录成功";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 所有权限标识
     */
    public static final String ALL_PERMISSION = "/*/*/*";

    /**
     * 管理员角色权限标识
     */
    public static final String SUPER_ADMIN = "admin";

    /**
     * 角色权限分隔符
     */
    public static final String EMPTY_STRING = "";

    public static final Long ZERO_LONG = 0L;

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 网络环境 redis key
     */
    public static final String NETWORK_ENVIRONMENT = "network_environment:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 参数管理 MAG_CODE
     */
    public static final String VERIFY_CODE = "verifyCode";

    /**
     * 参数管理 MAG_CODE
     */
    public static final String VERIFY_CODE_TIME = "verifyCodeTime";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * RSA
     */
    public static final String SYS_RSA_KEY = "sys_rsa:";

    /**
     * RSA
     */
    public static final String RSA_KEY_PUBLIC = "rsa_key_public";

    /**
     * RSA
     */
    public static final String RSA_KEY_PRIVATE = "rsa_key_private";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 账号对应的菜单，跟用户id绑定
     */

    public static final String MENU_ROUTER = "menu_router:";

    /**
     * 自动识别json对象白名单配置（仅允许解析的包名，范围越小越安全）
     */
    public static final String[] JSON_WHITELIST_STR = {"org.springframework", "com.jusha"};

    public static final String PATH_DELIMITER = "/";

    public static final String PATH_EXCEL = "excelTemp/";

    public static final String USER_EXCEL_NAME = "sysUserTemp.xlsx";
    public static final String USER_EXCEL_TITLE_NAME = "用户信息表";
    public static final String USER_EXCEL_SHEET_NAME = "用户信息表";
    public static final String USER_EXCEL_FILE_NAME = "用户信息表";

    public static final String BEGIN_TIME = "beginTime";

    public static final String END_TIME = "endTime";

    public static final String FAIL_SYSLOGIN_INFOR = "failSysLogininfor";

    public static final String TODAY_LOGIN_COUNT = "todayLoginCount";

    public static final String YSETODAY_LOGIN_COUNT = "yestodayLoginCount";

    public static final String RECENT7_USER_LIST = "recent7UserList";

    public static final String MYSQL_LIMIT_1 = "LIMIT 1";

    public static final int SEVEN_DAYS = 7;

    public static final String TOTAL_USER_COUNT = "totalUserCount";

    public static final String TODAY_USER_COUNT = "todayUserCount";

    public static final String TOTAL_COUNT = "totalCount";
    public static final String ERROR_COUNT = "errorCount";
    public static final String SUCCESS_COUNT = "successCount";

    public static final String MSG = "msg";

    public static final String DETAIL = "detail";

    public static final String PLAT_ID = "platId";

    public static final String PLAT_TAG = "platTag";

    public static final String OLD_PASSWORD = "oldPassword";

    public static final String NEW_PASSWORD = "newPassword";

    /** 正常状态 */
    public static final String NORMAL = "0";

    /** 分组正常状态 */
    public static final String DEPT_NORMAL = "0";

    /** 根目录 */
    public static final String DEPT_ROOT = "0";

    /** 分组停用状态 */
    public static final String DEPT_DISABLE = "1";

    /** 是否为系统默认（是） */
    public static final String YES = "Y";

    /** 菜单类型（目录） */
    public static final String TYPE_DIR = "M";

    /** 菜单类型（菜单） */
    public static final String TYPE_MENU = "C";

    /** 菜单类型（按钮） */
    public static final String TYPE_BUTTON = "F";

    /** Layout组件标识 */
    public final static String LAYOUT = "Layout";

    /** 校验是否唯一的返回标识 */
    public final static boolean UNIQUE = true;
    public final static boolean NOT_UNIQUE = false;

    public final static Long ADMIN_ROLE_ID = 1L;

    /**
     * 用户名长度限制
     */
    public static final int USERNAME_MIN_LENGTH = 2;
    public static final int USERNAME_MAX_LENGTH = 20;

    /**
     * 密码长度限制
     */
    public static final int PASSWORD_MIN_LENGTH = 6;
    public static final int PASSWORD_MAX_LENGTH = 20;

    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "user_id";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";

    /**
     * 发送短信-手机号码的REDIS的key
     */
    public static final String SMS_MEG_PHONE = "sms_phone:";

    /**
     * 发送短信-手机号码最近10分钟的发送次数的REDIS的key
     */
    public static final String COUNT_10_MIN = ":count10";

    /**
     * 发送短信-手机号码最近1分钟的发送次数的REDIS的key
     */
    public static final String COUNT_1_MIN = ":count1";

    /**
     * 发送短信-手机号码最近1天的发送次数的REDIS的key
     */
    public static final String COUNT_1_DAY = ":day";

    /**
     * 发送短信-手机号码最近接收到的REDIS的key
     */
    public static final String CODE = ":code";

    /**
     * 发送短信类型-登录
     */
    public static final String SMS_MEG_TYPE_LOGIN = "login";

    /**
     * 发送短信类型-修改密码
     */
    public static final String SMS_MEG_TYPE_PASSWORD = "password";

    /**
     * 发送短信类型-修改手机号
     */
    public static final String SMS_MEG_TYPE_PHONE = "phone";

    /**
     * 教学-角色名称-学生
     */
    public static final String ROLE_NAME_STUDENT = "学员";

    /**
     * 教学-角色名称-教师
     */
    public static final String ROLE_NAME_TEACHER = "老师";

    /**
     * 教学-角色名称-教秘
     */
    public static final String ROLE_NAME_MANAGER = "管理员";


    /**
     * plato-角色名称-管理员
     */
    public static final String PLATO_ROLE_NAME_1 = "管理员";

    /**
     * plato-角色名称-普通用户
     */
    public static final String PLATO_ROLE_NAME_2 = "普通用户";

    /**
     * plato-角色名称-匿名用户
     */
    public static final String PLATO_ROLE_NAME_3 = "匿名用户";


    /**
     * 角色名称-RIS平台管理员
     */
    public static final String RIS_ROLE_NAME_PLAT_ADMIN = "平台管理员";

    /**
     * 角色名称-RIS分组组长
     */
    public static final String RIS_ROLE_NAME_GROUP_LEADER = "分组组长";

    /**
     * 角色名称-RIS医院管理员
     */
    public static final String RIS_ROLE_NAME_HOSPITAL_ADMIN = "医院管理员";

    /**
     * 角色名称-RIS医生
     */
    public static final String RIS_ROLE_NAME_DOCTOR = "医生";
}
