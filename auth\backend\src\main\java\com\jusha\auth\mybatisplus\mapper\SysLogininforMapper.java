package com.jusha.auth.mybatisplus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jusha.auth.monitor.domain.LoginTimeStatistics;
import com.jusha.auth.mybatisplus.entity.SysLogininfor;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 系统访问记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
public interface SysLogininforMapper extends BaseMapper<SysLogininfor> {

    @Update("truncate table sys_login_infor")
    public void clearAll();

    @Select("SELECT user_name userName,COUNT(*) as count FROM sys_login_infor where login_time >= #{days7Ago} and login_time<=#{nowDate} GROUP BY user_name  ORDER BY count DESC limit 10")
    public List<LoginTimeStatistics> queryLoginTime(@Param("days7Ago") Date days7Ago, @Param("nowDate")Date nowDate);

}
