package com.jusha.ris.docking.study.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 检查请求类
 * @date 2025/7/12
 */

@ApiModel
@Data
public class StudyReq {

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("申请单号")
    private String applyNo;

    @NotNull
    @ApiModelProperty("检查开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime studyStartTime;

    @NotNull
    @ApiModelProperty("检查结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime studyEndTime;

    @ApiModelProperty("门诊/住院/体检号")
    private String outInPatientNo;

    @ApiModelProperty("检查类型s")
    private List<String> studyTypes;

}
