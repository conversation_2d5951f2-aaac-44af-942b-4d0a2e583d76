package com.jusha.auth.common.core.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.page.*;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.exception.HttpStatus;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.ServletUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.utils.sql.SqlUtil;
import com.jusha.auth.mybatisplus.entity.SysPlat;
import com.jusha.auth.system.service.ISysPlatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import javax.servlet.http.HttpServletRequest;
import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysPlatService iSysPlatService;

    /**
     * 获取请求头中的platId
     *
     * @return String
     */
    public String getPlatId() {
        HttpServletRequest request = ServletUtils.getRequest();
        String platId =  request.getHeader(Constants.PLAT_ID);
        String platTag =  request.getHeader(Constants.PLAT_TAG);
        if (platId == null && platTag ==null) {
            throw new ServiceException(MessageUtils.message("take.plate.parameters"));
        }
        SysPlat sysPlat = null;
        if(platId != null){
            sysPlat = iSysPlatService.selectSysPlatByPlatId(Long.parseLong(platId));
        }
        if(platTag != null){
            SysPlat sysPlatForQ = new SysPlat();
            sysPlatForQ.setPlatTag(platTag);
            List<SysPlat> sysPlatList = iSysPlatService.selectSysPlatList(sysPlatForQ);
            if(!sysPlatList.isEmpty()){
                sysPlat = sysPlatList.get(0);
            }
        }
        if (sysPlat == null) {
            throw new ServiceException(MessageUtils.message("this.plate.not.exist"));
        }
        return sysPlat.getPlatId()+"";
    }
    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageUtils.startPage();
    }

    /**
     * 设置请求排序数据
     */
    protected void startOrderBy() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotEmpty(pageDomain.getOrderBy())) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.orderBy(orderBy);
        }
    }

    /**
     * 清理分页的线程变量
     */
    protected void clearPage() {
        PageUtils.clearPage();
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataNoCode getDataTableNoCode(List<?> list) {
        TableDataNoCode rspData = new TableDataNoCode();
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 返回成功
     */
    public ResultBean success() {
        return ResultBean.success();
    }

    /**
     * 返回失败消息
     */
    public ResultBean error() {
        return ResultBean.error();
    }

    /**
     * 返回成功消息
     */
    public ResultBean success(String message) {
        return ResultBean.success(message);
    }

    /**
     * 返回成功消息
     */
    public ResultBean success(Object data) {
        return ResultBean.success(data);
    }

    /**
     * 返回失败消息
     */
    public ResultBean error(String message) {
        return ResultBean.error(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected ResultBean resultBean(int rows) {
        return rows > 0 ? ResultBean.success() : ResultBean.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected ResultBean resultBean(boolean result) {
        return result ? success() : error();
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }

    /**
     * 获取用户缓存信息
     */
    public LoginUser getLoginUser() {
        return tokenService.getLoginUser();
    }

    public LoginUser getLoginUserCanNull() {
        return tokenService.getLoginUserCanNull();
    }


    /**
     * 获取登录用户id
     */
    public Long getUserId() {
        return getLoginUser().getUserId();
    }

    /**
     * 获取登录用户名
     */
    public String getUsername() {
        return getLoginUser().getUsername();
    }
}
