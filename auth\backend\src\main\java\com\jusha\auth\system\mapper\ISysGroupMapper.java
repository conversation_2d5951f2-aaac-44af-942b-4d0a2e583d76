package com.jusha.auth.system.mapper;

import com.jusha.auth.mybatisplus.entity.SysGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分组管理 数据层
 *
 * <AUTHOR>
 */
public interface ISysGroupMapper {

    /**
     * 根据角色ID查询分组树信息
     *
     * @param roleId 角色ID
     * @return 选中分组列表
     */
    public List<Long> selectGroupListByRoleId(@Param("roleId") Long roleId,@Param("platId") Long platId);

    /**
     * 根据ID查询所有子分组
     *
     * @param groupId 分组ID
     * @return 分组列表
     */
    public List<SysGroup> selectChildrenGroupById(@Param("groupId") Long groupId);

    /**
     * 根据ID查询所有子分组（正常状态）
     *
     * @param groupId 分组ID
     * @return 子分组数
     */
    public int selectNormalChildrenGroupById(@Param("groupId") Long groupId);

    /**
     * 修改子元素关系
     *
     * @param groups 子元素
     * @return 结果
     */
    public int updateGroupChildren(@Param("groups") List<SysGroup> groups);
}
