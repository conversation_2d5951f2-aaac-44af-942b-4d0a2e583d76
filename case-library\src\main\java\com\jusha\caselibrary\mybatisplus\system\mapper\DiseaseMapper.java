package com.jusha.caselibrary.mybatisplus.system.mapper;

import com.jusha.caselibrary.mybatisplus.system.entity.Disease;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 疾病分类表 Mapper 接口
 * <AUTHOR>
 */
public interface DiseaseMapper extends BaseMapper<Disease> {

    /**
     * 根据ID查询所有下级疾病
     * @param diseaseId 疾病ID
     * @return 疾病列表
     */
    public List<Disease> selectChildrenDiseaseById(@Param("diseaseId") Long diseaseId);


    /**
     * 修改子元素关系
     * @param diseases 子元素
     * @return 结果
     */
    public int updateDiseaseChildren(@Param("diseases") List<Disease> diseases);
}
