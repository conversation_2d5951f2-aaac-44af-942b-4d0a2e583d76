package com.jusha.caselibrary.system.service;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.system.dto.req.CaseLibTypeSaveReq;
import com.jusha.caselibrary.system.dto.req.CaseLibTypeUpdateReq;
import com.jusha.caselibrary.system.dto.resp.CaseLibTypeResp;

import java.util.List;

/**
 * @InterfaceName CaseLibTypeService
 * @Description 病例库类型管理服务接口
 * <AUTHOR>
 * @Date 2025/7/3 16:27
 **/
public interface CaseLibTypeService {

    /**
     * @description 病例库类型列表
     * <AUTHOR>
     * @date 2025/7/3 16:37
     * @return List<CaseLibTypeResp>
     **/
    List<CaseLibTypeResp> getCaseLibTypeList();

    /**
     * @description 病例库类型分页列表
     * <AUTHOR>
     * @date 2025/7/3 16:51
     * @param pageNum
     * @param pageSize
     * @return PageInfo<CaseLibTypeResp>
     **/
    PageInfo<CaseLibTypeResp> getCaseLibTypePage(Integer pageNum, Integer pageSize);

    /**
     * @description 新增病例库类型
     * <AUTHOR>
     * @date 2025/7/3 17:05
     * @param req
     * @return void
     **/
    void addCaseLibType(CaseLibTypeSaveReq req);

    /**
     * @description 删除病例库类型
     * <AUTHOR>
     * @date 2025/7/7 10:25
     * @param caseTypeId
     * @return void
     **/
    void deleteCaseLibType(Long caseTypeId);

    /**
     * @description 修改病例库类型
     * <AUTHOR>
     * @date 2025/7/7 10:39
     * @param req
     * @return void
     **/
    void updateCaseLibType(CaseLibTypeUpdateReq req);

    /**
     * @description 获取病例库类型详情
     * <AUTHOR>
     * @date 2025/7/7 10:58
     * @param caseTypeId
     * @return CaseLibTypeResp
     **/
    CaseLibTypeResp getCaseLibTypeDetail(Long caseTypeId);
}
