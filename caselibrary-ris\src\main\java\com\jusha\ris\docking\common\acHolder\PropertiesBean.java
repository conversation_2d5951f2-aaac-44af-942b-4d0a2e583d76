package com.jusha.ris.docking.common.acHolder;


import com.jusha.ris.docking.common.constant.Constant;
import com.jusha.ris.docking.common.util.LoginUtil;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * application.yml中配置的持有对象
 */
@RefreshScope
@Getter
@Component
public class PropertiesBean {

    @Value("${spring.application.name}")
    private String applicationName;    //服务名

    @Value("${ENVIRONMENT}")
    private String environment;    //环境(dev、test、prod)

    @Value("${TMP-LOCATIONS}")
    private String tmpLocation;

    @Value("${MINIO_ENDPOINT:}")
    private String minioPath;

//    @Value("${JWT_SECRET}")
//    private String jwtSecret;            //JWT secret

    public String getMinioPath(){
        return Constant.NETWORK_LAN.equals(LoginUtil.getNetwork())?minioPath:"";
    }

}