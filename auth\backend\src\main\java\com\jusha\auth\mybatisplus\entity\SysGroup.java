package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 分组表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Getter
@Setter
@TableName("sys_group")
@ApiModel(value = "SysGroup对象", description = "分组表")
public class SysGroup {

    public SysGroup(Long platId) {
        this.platId = platId;
    }

    public SysGroup() {
    }

    @ApiModelProperty(value = "分组id")
    @TableId("group_id")
    private Long groupId;

    @ApiModelProperty(value = "所属平台id")
    @TableField("plat_id")
    private Long platId;

    @ApiModelProperty(value = "父分组id")
    @TableField("parent_id")
    private Long parentId;

    @ApiModelProperty(value = "祖级列表")
    @TableField("ancestors")
    private String ancestors;

    @ApiModelProperty(value = "分组名称")
    @TableField("group_name")
    @Size(max = 50, message = "分组名称长度不合法")
    private String groupName;

    @ApiModelProperty(value = "显示顺序")
    @TableField("order_num")
    private Integer orderNum;

    @ApiModelProperty(value = "负责人")
    @TableField("leader")
    private String leader;

    @ApiModelProperty(value = "联系电话")
    @TableField("phone")
    private String phone;

    @ApiModelProperty(value = "分组状态（0正常 1停用）")
    @TableField("status")
    private String status;

    @ApiModelProperty(value = "是否中心分组（0是 1不是）")
    @TableField("center")
    private String center;

    @ApiModelProperty(value = "删除标志（0代表存在 1代表删除）")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;

    @ApiModelProperty(value = "创建者")
    @TableField("create_by")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField("update_by")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @TableField(exist = false)
    private List<SysGroup> children = new ArrayList<SysGroup>();
}
