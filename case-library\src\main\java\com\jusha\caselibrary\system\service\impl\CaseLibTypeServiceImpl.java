package com.jusha.caselibrary.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.feign.api.AuthServerApi;
import com.jusha.caselibrary.feign.dto.req.SysMenu;
import com.jusha.caselibrary.mybatisplus.entity.DepCaseType;
import com.jusha.caselibrary.mybatisplus.service.DepCaseTypeService;
import com.jusha.caselibrary.system.dto.SysRole;
import com.jusha.caselibrary.system.dto.req.CaseLibTypeSaveReq;
import com.jusha.caselibrary.system.dto.req.CaseLibTypeUpdateReq;
import com.jusha.caselibrary.system.dto.resp.CaseLibTypeResp;
import com.jusha.caselibrary.system.service.CaseLibTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName CaseLibTypeServiceImpl
 * @Description 病例库类型管理服务实现类
 * <AUTHOR>
 * @Date 2025/7/3 16:28
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseLibTypeServiceImpl implements CaseLibTypeService {

    private final DepCaseTypeService depCaseTypeService;
    private final AuthServerApi authServerApi;

    @Override
    public List<CaseLibTypeResp> getCaseLibTypeList() {
        List<DepCaseType> depCaseTypeList = depCaseTypeService.list();
        if (CollectionUtils.isNotEmpty(depCaseTypeList)) {
            // 转换为CaseLibTypeResp列表
            CaseLibTypeResp caseLibTypeResp = new CaseLibTypeResp();
            return depCaseTypeList.stream()
                    .map(depCaseType -> {
                        BeanUtils.copyProperties(depCaseType, caseLibTypeResp);
                        return caseLibTypeResp;
                    }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public PageInfo<CaseLibTypeResp> getCaseLibTypePage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<DepCaseType> depCaseTypeList = depCaseTypeService.list();
        PageInfo<DepCaseType> orgPpageInfo = new PageInfo<>(depCaseTypeList);
        if (CollectionUtils.isNotEmpty(depCaseTypeList)) {
            // 转换为CaseLibTypeResp列表
            CaseLibTypeResp caseLibTypeResp = new CaseLibTypeResp();
            List<CaseLibTypeResp> caseLibTypeRespList = depCaseTypeList.stream()
                    .map(depCaseType -> {
                        BeanUtils.copyProperties(depCaseType, caseLibTypeResp);
                        return caseLibTypeResp;
                    }).collect(Collectors.toList());
            PageInfo<CaseLibTypeResp> pageInfo = new PageInfo<>(caseLibTypeRespList);
            BeanUtils.copyProperties(orgPpageInfo, pageInfo, "list");
            return pageInfo;
        }
        return new PageInfo<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCaseLibType(CaseLibTypeSaveReq req) {
        Long loginUserId = LoginUtil.getLoginUserId();
        //新增病例库类型
        DepCaseType depCaseType = new DepCaseType();
        depCaseType.setCaseTypeId(YitIdHelper.nextId());
        depCaseType.setCaseTypeName(req.getCaseTypeName());
        depCaseType.setAudit(req.getAudit());
        depCaseType.setCreateTime(new Date());
        depCaseType.setCreateBy(loginUserId);
        //调用权限系
        //1.获取科室病例库目录菜单
        List<SysMenu> sysMenuList = authServerApi.getMenuListByName(Constant.CASE_LIB_MENU_NAME).getData();
        if (CollectionUtils.isEmpty(sysMenuList)) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.menu.not.exist"));
        }
        SysMenu parentMenu = sysMenuList.stream().filter(s -> s.getParentId().equals(Constant.ROOT_MENU_ID)).findFirst()
                .orElseThrow(() -> new BusinessException(LocaleUtil.getLocale("case.lib.menu.not.exist")));
        //1.创建病例库分类子菜单
        SysMenu sysMenu = new SysMenu();
        sysMenu.setPlatId(ContextHolder.propertiesBean().getPlatId());
        sysMenu.setParentId(parentMenu.getMenuId());
        sysMenu.setMenuType(Constant.TYPE_MENU);
        sysMenu.setMenuName(req.getCaseTypeName());
        sysMenu.setOrderNum(0);
        //组装路径
        String path = parentMenu.getPath() + "/" + chineseToPinyinWithCapitalization(req.getCaseTypeName());
        depCaseType.setAddress(path);
        depCaseType.setMenuId(parentMenu.getMenuId());
        sysMenu.setPath(path);
        sysMenu.setComponent(path);
        sysMenu.setVisible(Constant.NORMAL);
        depCaseTypeService.save(depCaseType);
        SysMenu resultMenu = authServerApi.addMenu(sysMenu).getData();
        //2.为病例库角色授权菜单
        List<SysRole> sysRoleList = authServerApi.roleList().getData();
        if (CollectionUtils.isNotEmpty(sysRoleList)) {
            for (SysRole sysRole : sysRoleList) {
                // 获取角色的菜单
                List<Long> menuIds = authServerApi.menuIdsByRoleId(sysRole.getRoleId()).getData();
                menuIds.add(resultMenu.getMenuId());
                sysRole.setMenuIds(menuIds.toArray(new Long[0]));
                authServerApi.editRole(sysRole);
            }
        }
    }


    /**
     * 将中文字符串转换为拼音，每个拼音单词首字母大写
     *
     * @param chinese 中文字符串
     * @return 拼音字符串，单词间用空格分隔，首字母大写
     */
    private String chineseToPinyinWithCapitalization(String chinese) {
        if (chinese == null || chinese.trim().isEmpty()) {
            return "";
        }
        try {
            HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
            format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
            format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
            format.setVCharType(HanyuPinyinVCharType.WITH_V);
            StringBuilder result = new StringBuilder();
            char[] chars = chinese.toCharArray();
            for (char c : chars) {
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    // 中文字符
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        String pinyin = pinyinArray[0];
                        // 首字母大写
                        pinyin = pinyin.substring(0, 1).toUpperCase() + pinyin.substring(1);
                        result.append(pinyin);
                    }
                } else if (Character.isLetter(c)) {
                    // 英文字符直接添加
                    result.append(c);
                }
                // 其他字符忽略
            }
            return result.toString();
        } catch (Exception e) {
            log.error("中文转拼音失败: {}", chinese, e);
            return chinese;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCaseLibType(Long caseTypeId) {
        //删除病例库类型
        DepCaseType depCaseType = depCaseTypeService.getById(caseTypeId);
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        depCaseTypeService.removeById(caseTypeId);
        //删除权限系统下的菜单
        authServerApi.removeMenu(depCaseType.getMenuId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCaseLibType(CaseLibTypeUpdateReq req) {
        DepCaseType depCaseType = depCaseTypeService.getById(req.getCaseTypeId());
        // 修改病例库类型
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        if (req.getCaseTypeName() != null) {
            depCaseType.setCaseTypeName(req.getCaseTypeName());
        }
        if (req.getAudit() != null) {
            depCaseType.setAudit(req.getAudit());
        }
        depCaseTypeService.updateById(depCaseType);
        //修改权限系统下的菜单
        if (req.getCaseTypeName() != null) {
            SysMenu sysMenu = new SysMenu();
            sysMenu.setMenuId(depCaseType.getMenuId());
            sysMenu.setMenuName(req.getCaseTypeName());
            authServerApi.editMenu(sysMenu);
        }
    }

    @Override
    public CaseLibTypeResp getCaseLibTypeDetail(Long caseTypeId) {
        DepCaseType depCaseType = depCaseTypeService.getById(caseTypeId);
        if (depCaseType == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.lib.type.not.exist"));
        }
        CaseLibTypeResp caseLibTypeResp = new CaseLibTypeResp();
        caseLibTypeResp.setCaseTypeId(depCaseType.getCaseTypeId());
        caseLibTypeResp.setCaseTypeName(depCaseType.getCaseTypeName());
        caseLibTypeResp.setAudit(depCaseType.getAudit());
        caseLibTypeResp.setAddress(depCaseType.getAddress());
        caseLibTypeResp.setUpdateTime(depCaseType.getUpdateTime().toString());
        return caseLibTypeResp;
    }
}
