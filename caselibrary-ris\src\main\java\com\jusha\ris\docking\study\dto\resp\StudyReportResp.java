package com.jusha.ris.docking.study.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title StudyReportResp
 * @description
 * @date 2025/2/26
 */

@ApiModel
@Data
public class StudyReportResp {

    @ApiModelProperty("检查id")
    private Long studyId;

    @ApiModelProperty("检查状态")
    private Integer studyStatus;

    @ApiModelProperty("检查号")
    private String studyNo;

    @ApiModelProperty("影像Uid")
    private String studyUid;

    @ApiModelProperty("影像号")
    private String accessNumber;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("患者性别")
    private Integer patientSex;

    @ApiModelProperty("患者号")
    private String patientNo;

    @ApiModelProperty("患者年龄")
    private String patientAge;

    @ApiModelProperty("患者类型")
    private String patientType;

    @ApiModelProperty("检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String studyTime;

    @ApiModelProperty("检查设备")
    private String studyDevice;

    @ApiModelProperty("检查类型")
    private String studyType;

    @ApiModelProperty("检查项目")
    private String studyItems;

    @ApiModelProperty("检查部位")
    private String studyParts;

    @ApiModelProperty("申请科室")
    private String applyDepartment;

    @ApiModelProperty("检查目的")
    private String studyPurpose;

    @ApiModelProperty("体征")
    private String physicalSign;

    @ApiModelProperty("患者主诉")
    private String selfComplaints;

    @ApiModelProperty("病史")
    private String historyDisease;

    @ApiModelProperty("临床诊断")
    private String clinicalDiagnosis;

    @ApiModelProperty("报告医生")
    private String reportDoctor;

    @ApiModelProperty("报告时间")
    private String reportTime;

    @ApiModelProperty("审核医生")
    private String checkDoctor;

    @ApiModelProperty("审核时间")
    private String checkTime;

    @ApiModelProperty("报告id")
    private Long reportId;

    @ApiModelProperty("影像所见")
    private String reportDescribe;

    @ApiModelProperty("诊断意见")
    private String reportDiagnosis;

    @ApiModelProperty("是否阳性 0-未知（默认） 1-阴性 2-阳性 3-重大阳性")
    private Integer positiveStatus;

    @ApiModelProperty("影像评级（0-未知（默认） 1-废片 2-甲片 3-乙片 4-丙片）")
    private Integer imageRating;



}
