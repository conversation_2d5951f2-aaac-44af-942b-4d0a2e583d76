package com.jusha.caselibrary.file.dto.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MinioMsgRsp {

    @ApiModelProperty("环境: LAN-内网，WAN-外网")
    private String network;

    @ApiModelProperty("Minio的endpoint")
    private String minioPath;

    @ApiModelProperty("Minio的accessKey")
    private String minioAccessKey;

    @ApiModelProperty("Minio的secretKey")
    private String minioSecretKey;

    @ApiModelProperty("Minio的bucketName")
    private String minioBucketName;

}