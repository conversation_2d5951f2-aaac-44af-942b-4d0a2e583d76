package com.jusha.auth.common.acHolder;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * application.yml中配置的持有对象
 */
@RefreshScope
@Getter
@Component
public class PropertiesBean {

    @Value("${spring.application.name:}")
    private String applicationName;    //服务名

}