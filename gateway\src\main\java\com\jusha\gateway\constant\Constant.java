package com.jusha.gateway.constant;

/**
 * 常量
 */
public class Constant {

    /**
     * traceId
     */
    public static final String TRACEID = "traceId";


    /**
     * token key（JWT）
     */
    public static final String TOKEN_KEY = "Authorization";


    /**
     * 错误码
     */
    public static final int COMMON_ERROR = 1;       //通用错误码
    public static final int UNAUTHORIZED = 401;     //登录失效
    public static final int FORBIDDEN = 403;        //无访问权限

    /**
     * 网络环境 redis key
     */
    public static final String NETWORK_ENVIRONMENT = "network_environment:";

    /**
     * 当前用户网络环境: LAN 内网, WAN 外网
     */
    public static final String NETWORK_LAN = "LAN";
    public static final String NETWORK_WAN = "WAN";
}
