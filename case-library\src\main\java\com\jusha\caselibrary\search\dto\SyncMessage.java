package com.jusha.caselibrary.search.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName SyncMessage
 * @Description 同步消息DTO
 * <AUTHOR>
 * @Date 2025/7/7 16:17
 **/
@Data
public class SyncMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 索引ID（单个操作使用）
     */
    private Long indexId;

    /**
     * 索引ID集合（批量操作使用）
     */
    private List<Long> indexIdList;

    /**
     * 病例类型（department: 科室病例, personal: 个人病例）
     */
    private String indexType;

    /**
     * 操作类型（CREATE, UPDATE, DELETE, BATCH_CREATE, BATCH_UPDATE, BATCH_DELETE）
     */
    private String operation;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 延迟执行时间（毫秒）
     */
    private Long delayTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 是否需要重试
     */
    public boolean needRetry() {
        return retryCount < maxRetryCount;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetry() {
        this.retryCount++;
    }

    /**
     * 构造方法
     */
    public SyncMessage() {
        this.createTime = LocalDateTime.now();
        this.messageId = generateMessageId();
    }

    /**
     * 构造方法（单个操作）
     */
    public SyncMessage(Long indexId, String indexType, String operation) {
        this();
        this.indexId = indexId;
        this.indexType = indexType;
        this.operation = operation;
    }

    /**
     * 构造方法（批量操作）
     */
    public SyncMessage(List<Long> indexIdList, String indexType, String operation) {
        this();
        this.indexIdList = indexIdList;
        this.indexType = indexType;
        this.operation = operation;
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "sync_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 判断是否为批量操作
     */
    public boolean isBatchOperation() {
        return indexIdList != null && !indexIdList.isEmpty();
    }

    /**
     * 获取有效的ID集合（兼容单个和批量操作）
     */
    public List<Long> getEffectiveCaseIdList() {
        if (isBatchOperation()) {
            return indexIdList;
        } else if (indexId != null) {
            return java.util.Collections.singletonList(indexId);
        } else {
            return java.util.Collections.emptyList();
        }
    }

    @Override
    public String toString() {
        return "SyncMessage{" +
                "messageId='" + messageId + '\'' +
                ", indexId=" + indexId +
                ", indexIdList=" + indexIdList +
                ", indexType='" + indexType + '\'' +
                ", operation='" + operation + '\'' +
                ", retryCount=" + retryCount +
                ", createTime=" + createTime +
                '}';
    }
}