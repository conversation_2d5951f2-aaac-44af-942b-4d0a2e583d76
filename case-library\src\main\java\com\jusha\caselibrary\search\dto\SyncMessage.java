package com.jusha.caselibrary.search.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName SyncMessage
 * @Description 同步消息DTO
 * <AUTHOR>
 * @Date 2025/7/7 16:17
 **/
@Data
public class SyncMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 病例ID
     */
    private Long caseId;

    /**
     * 目录ID（个人病例库特有）
     */
    private Long catalogId;

    /**
     * 病例类型（department: 科室病例, personal: 个人病例）
     */
    private String caseType;

    /**
     * 操作类型（CREATE, UPDATE, DELETE）
     */
    private String operation;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 延迟执行时间（毫秒）
     */
    private Long delayTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 是否需要重试
     */
    public boolean needRetry() {
        return retryCount < maxRetryCount;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetry() {
        this.retryCount++;
    }

    /**
     * 构造方法
     */
    public SyncMessage() {
        this.createTime = LocalDateTime.now();
        this.messageId = generateMessageId();
    }

    /**
     * 构造方法
     */
    public SyncMessage(Long caseId, String caseType, String operation) {
        this();
        this.caseId = caseId;
        this.caseType = caseType;
        this.operation = operation;
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "sync_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    @Override
    public String toString() {
        return "SyncMessage{" +
                "messageId='" + messageId + '\'' +
                ", caseId=" + caseId +
                ", caseType='" + caseType + '\'' +
                ", operation='" + operation + '\'' +
                ", retryCount=" + retryCount +
                ", createTime=" + createTime +
                '}';
    }
}