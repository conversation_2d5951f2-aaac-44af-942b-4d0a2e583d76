package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.AuditRecord;
import com.jusha.caselibrary.mybatisplus.mapper.AuditRecordMapper;
import com.jusha.caselibrary.mybatisplus.service.AuditRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 审核表 服务实现类
 * <AUTHOR>
 */
@Service
public class AuditRecordServiceImpl extends ServiceImpl<AuditRecordMapper, AuditRecord> implements AuditRecordService {

}
