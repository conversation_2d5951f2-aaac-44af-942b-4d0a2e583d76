package com.jusha.auth.monitor.domain.server;

import com.jusha.auth.common.utils.Arith;
import lombok.Getter;
import lombok.Setter;

/**
 * 內存相关信息
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class Mem {
    /**
     * 内存总量
     */
    private double total;

    /**
     * 已用内存
     */
    private double used;

    /**
     * 剩余内存
     */
    private double free;

    public double getUsage()
    {
        return Arith.mul(Arith.div(used, total, 4), 100);
    }
}
