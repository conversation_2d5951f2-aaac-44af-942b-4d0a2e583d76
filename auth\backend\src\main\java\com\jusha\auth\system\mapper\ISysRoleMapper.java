package com.jusha.auth.system.mapper;

import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色表 数据层
 *
 * <AUTHOR>
 */
public interface ISysRoleMapper {
    /**
     * 根据用户ID查询角色
     *
     * @param user 用户ID
     * @return 角色列表
     */
    public List<SysRole> selectRolePermissionByUser(SysUser user);

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    public List<Long> selectRoleListByUserId(@Param("userId") Long userId);


    /**
     * 根据用户ID查询角色
     *
     * @param userName 用户名
     * @return 角色列表
     */
    public List<SysRole> selectRolesByUserName(@Param("userName") String userName);

    /**
     * 根据用户ID查询角色
     *
     * @param user 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRoleByUser(SysUser user);

    /**
     * 删除用户再某个plat的关联角色
     * @param userIdList
     * @param platId
     */
    void removeUserPlatRole(@Param("userIdList") List<Long> userIdList, @Param("platId") Long platId);

}
