package com.jusha.auth.monitor.controller;

import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.monitor.domain.resp.SysDictDataResp;
import com.jusha.auth.monitor.service.ISysDictDataService;
import com.jusha.auth.monitor.service.ISysDictTypeService;
import com.jusha.auth.mybatisplus.entity.SysDictData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController {
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @HasPermissions
    @GetMapping(value = "/list")
    public TableDataInfo list(SysDictData dictData) {
        startPage();
        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
        return getDataTable(list);
    }

    /**
     * 查询字典数据详细
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long dictCode) {
        return success(dictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/type/dictType")
    public ResultBean dictType(@RequestParam String dictType) {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(data)) {
            data = new ArrayList<>();
        }
        return success(data);
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/getDicType")
    public ResultBean getDicType(@RequestParam String dictType) {
        List<SysDictData> sysDictDataList = dictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(sysDictDataList)) {
            return success(new ArrayList<>());
        }else {
            List<SysDictDataResp> newList = sysDictDataList.stream()
                    .map(data -> new SysDictDataResp(data.getDictLabel(), data.getDictValue(),data.getDictType())).collect(Collectors.toList());
            return success(newList);
        }
    }

    /**
     * 新增字典类型
     */
    @HasPermissions
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysDictData dict) {
        return resultBean(dictDataService.insertDictData(dict));
    }

    /**
     * 修改保存字典类型
     */
    @HasPermissions
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysDictData dict) {
        return resultBean(dictDataService.updateDictData(dict));
    }

    /**
     * 删除字典类型
     */
    @HasPermissions
    @PostMapping("/dictCodes/remove")
    public ResultBean remove(@RequestParam Long[] dictCodes) {
        dictDataService.deleteDictDataByIds(dictCodes);
        return success();
    }
}
