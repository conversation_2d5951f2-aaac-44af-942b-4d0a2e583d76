package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 操作日志表
 * <AUTHOR>
 */
@TableName("t_operate_log")
@Data
public class OperateLog {

    @ApiModelProperty(value = "日志ID")
    @TableId("log_id")
    private Long logId;    

    @ApiModelProperty(value = "日志产生时间")
    @TableField("log_time")
    private Date logTime;    

    @ApiModelProperty(value = "操作类型")
    @TableField("log_type")
    private String logType;    

    @ApiModelProperty(value = "操作模块")
    @TableField("log_module")
    private String logModule;    

    @ApiModelProperty(value = "操作用户")
    @TableField("log_user")
    private Long logUser;    

    @ApiModelProperty(value = "操作IP")
    @TableField("log_IP")
    private String logIp;    

    @ApiModelProperty(value = "操作结果")
    @TableField("log_resut")
    private String logResut;    

    @ApiModelProperty(value = "操作报错记录")
    @TableField("log_error")
    private String logError;    

    @ApiModelProperty(value = "操作参数")
    @TableField("log_param")
    private String logParam;    

    @ApiModelProperty(value = "操作详情")
    @TableField("log_content")
    private String logContent;    

}
