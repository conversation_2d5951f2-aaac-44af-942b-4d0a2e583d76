package com.jusha.ris.docking.common.util;


import com.jusha.ris.docking.common.acHolder.ContextHolder;
import com.jusha.ris.docking.common.acHolder.PropertiesBean;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * redis工具类
 */
public class RedisUtil {

    /**
     * 通用加锁的key (application名:lockKey:Class名.method名_参数)
     */
    public static String lockKey(String className, String methodName, List<String> params){
        String applicationName = ContextHolder.getBean(PropertiesBean.class).getApplicationName();

        StringBuilder sb = new StringBuilder();
        sb.append(applicationName).append(":lockKey:").append(className).append(".").append(methodName);
        if(CollectionUtils.isNotEmpty(params)){
            for(String param : params){
                sb.append("_").append(param);
            }
        }
        return sb.toString();
    }


    /**
     * 加锁
     * @param lockKey
     * @param lockVal
     * @param outSeconds：单位秒
     * @return: true-获取锁成功，false-获取锁失败
     */
    public static boolean lock(String lockKey, String lockVal, int outSeconds){
        StringRedisTemplate redisTemplate = ContextHolder.stringRedisTemplate();
        return redisTemplate.opsForValue().setIfAbsent(lockKey, lockVal, outSeconds, TimeUnit.SECONDS);
    }


    /**
     * 解锁
     * @param lockKey
     * @param lockVal
     */
    public static boolean unlock(String lockKey, String lockVal){
        if (StringUtils.isBlank(lockKey) || StringUtils.isBlank(lockVal))
            return false;

        boolean unlockResult = false;
        StringRedisTemplate redisTemplate = ContextHolder.stringRedisTemplate();
        if (lockVal.equals(redisTemplate.opsForValue().get(lockKey))) {
            unlockResult = redisTemplate.opsForValue().getOperations().delete(lockKey);
        }
        return unlockResult;
    }

}