package com.jusha.auth.system.mapper;

import com.jusha.auth.mybatisplus.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 数据层
 * 
 * <AUTHOR>
 */
public interface ISysUserMapper {

    /**
     * 根据条件分页查询已配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(@Param("userName")String userName);

    /**
     * 通过电话号码查询用户
     *
     * @param phoneNumber 电话号码
     * @return 用户对象信息
     */
    public SysUser selectUserByPhoneNumber(@Param("phoneNumber")String phoneNumber);

    /**
     * 通过工号查询用户
     *
     * @param workNumber 工号
     * @return 用户对象信息
     */
    public SysUser selectUserByWorkNumber(@Param("workNumber")String workNumber);

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(@Param("userId")Long userId);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public List<SysUser> selectUserByUserNamePhoneWork(@Param("userName")String userName);

}
