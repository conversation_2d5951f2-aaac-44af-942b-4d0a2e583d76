package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 审核表
 * <AUTHOR>
 */
@TableName("t_audit_record")
@Data
public class AuditRecord {

    @ApiModelProperty(value = "审核ID")
    @TableId("audit_id")
    private Long auditId;    

    @ApiModelProperty(value = "病例id")
    @TableField("case_id")
    private Long caseId;    

    @ApiModelProperty(value = "审核类型：RIS导入，本地导入，随访转其他，个人转科室")
    @TableField("audit_type")
    private String auditType;    

    @ApiModelProperty(value = "发布人")
    @TableField("pub_user_id")
    private Long pubUserId;    

    @ApiModelProperty(value = "发布时间")
    @TableField("pub_time")
    private Date pubTime;    

    @ApiModelProperty(value = "审核人")
    @TableField("audited_by")
    private Long auditedBy;    

    @ApiModelProperty(value = "审核时间")
    @TableField("audited_time")
    private Date auditedTime;    

    @ApiModelProperty(value = "审核状态：0-待审核 1-通过 2-驳回")
    @TableField("status")
    private String status;    

    @ApiModelProperty(value = "审核意见")
    @TableField("audit_comment")
    private String auditComment;    

}
