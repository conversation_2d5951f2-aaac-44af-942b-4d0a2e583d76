package com.jusha.caselibrary.sickcase.service;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseCreateReq;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseUpdateReq;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;

import java.util.List;

/**
 * @ClassName PersonalCaseService
 * @Description 个人病例服务接口
 * <AUTHOR>
 * @Date 2025/7/10 09:32
 **/
public interface PersonalCaseService {

    /**
     * @description 个人病例库详细列表
     * <AUTHOR>
     * @date 2025/7/11 14:46
     * @param req
     * @return List<PersonalCaseDetailResp>
     **/
    List<PersonalCaseDetailResp> getPersonalCaseDetailList(PersonalCaseSearchReq req);

    /**
     * @description 个人病例库详细列表分页
     * <AUTHOR>
     * @date 2025/7/11 16:08
     * @param req
     * @return PageInfo<PersonalCaseDetailResp>
     **/
    PageInfo<PersonalCaseDetailResp> getPersonalCaseDetailPage(PersonalCaseSearchReq req);

    /**
     * @description 个人病例库病例详情查询
     * <AUTHOR>
     * @date 2025/7/11 16:12
     * @param userCaseId
     * @return PersonalCaseDetailResp
     **/
    PersonalCaseDetailResp getPersonalCaseDetail(Long userCaseId);

    /**
     * @param userCaseId
     * @param catalogId
     * @return void
     * @description 个人病例库病例删除
     * <AUTHOR>
     * @date 2025/7/11 16:13
     **/
    void deletePersonalCaseById(Long userCaseId, Long catalogId);

    /**
     * @description 在指定目录下创建个人病例
     * <AUTHOR>
     * @date 2025/7/14 13:56
     * @param req
     * @return long
     **/
    long createPersonalCaseInCatalog(PersonalCaseCreateReq req);

    /**
     * @description 修改个人病例库病例（基本信息）
     * <AUTHOR>
     * @date 2025/7/14 14:24
     * @param req
     * @return void
     **/
    void updatePersonalCase(PersonalCaseUpdateReq req);
}
