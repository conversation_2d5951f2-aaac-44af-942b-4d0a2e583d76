package com.jusha.ris.docking.common.util;

import org.apache.commons.io.IOUtils;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HttpContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLSession;
import javax.servlet.ServletOutputStream;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.security.GeneralSecurityException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;

/**
 * HttpClient4.5.1工具类 支持HTTP、HTTPS请求GET、POST调用
 * <AUTHOR>
 * @version V1.0
 */
public class HttpClientUtil {

	private final static Logger log = LoggerFactory.getLogger(HttpClientUtil.class);

	private static HttpClient client = null;

	private static final int NUM_OF_RETRIES = 3;

	// 连接超时
	protected static final Integer DEFAULT_CONNECTION_TIME_OUT = 600000;

	// 读取超时
	protected static final Integer DEFAULT_SOCKET_TIME_OUT = 600000;

	protected static final String DEFAULT_CHAR_SET = "UTF-8";

	static {
		HttpRequestRetryHandler handler = new HttpRequestRetryHandler() {
			@Override
			public boolean retryRequest(IOException exception, int executionCount, HttpContext context) {
				if (executionCount > NUM_OF_RETRIES) {
					return false;
				}
				if (exception instanceof UnknownHostException
						|| exception instanceof ConnectTimeoutException
						|| exception instanceof InterruptedIOException
						|| exception instanceof SSLException) {
					return false;
				}
				if (exception instanceof NoHttpResponseException
						|| exception instanceof SocketException
						|| exception instanceof ConnectException) {
					return true;
				}
				HttpClientContext clientContext = HttpClientContext
						.adapt(context);
				HttpRequest request = clientContext.getRequest();
				// 如果请求是幂等的，就再次尝试
				if (!(request instanceof HttpEntityEnclosingRequest)) {
					return true;
				}
				return false;
			}
		};
		PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
		cm.setMaxTotal(40);
		cm.setDefaultMaxPerRoute(20);
		client = HttpClients.custom().setConnectionManager(cm).setRetryHandler(handler).build();
	}

	public static String doPostDefaultSecurity(String url, String jsonText) throws Exception {
		return doPost(url, jsonText, null, null);
	}

	public static String doPostDefaultSecurity(String url, String jsonText, Map<String, String> header, Map<String, String> returnHeader) throws Exception {
		return doPost(url, jsonText, header, returnHeader);
	}

	public static String doPostDefaultSecurity(String url, String jsonText, Map<String, String> header) throws Exception {
		return doPost(url, jsonText, header, null);
	}

	public static String doGetDefaultSecurity(String url) throws Exception {
		return doGet(url, null, null,null);
	}

	public static String doGetDefaultSecurity(String url, Boolean doubleAuth) throws Exception {
		return doGet(url, null, doubleAuth,null);
	}


	public static String doGetDefaultSecurity(String url, Map<String, String> header) throws Exception {
		return doGet(url, header, null,null);
	}

	public static String doGetDefaultSecurity(String url, Map<String, String> header, Map<String, String> returnHeader) throws Exception {
		return doGet(url, header, null,returnHeader);
	}

	/**
	 * <p>
	 * 支持HTTP、HTTPS POST请求
	 * </p>
	 *
	 * <AUTHOR>
	 * @version V1.0
	 * @date 2017年5月19日
	 * @param url
	 * @param jsonText
	 * @return
	 * @throws Exception
	 *
	 */
	private static String doPost(String url, String jsonText, Map<String, String> header, Map<String, String> returnHeader) throws Exception {
		HttpClient client = null;
		HttpResponse res = null;
		HttpPost post = new HttpPost(url);
		InputStream is = null;
		try {
			if (jsonText != null && !jsonText.isEmpty()) {
				StringEntity entity = new StringEntity(jsonText, ContentType.APPLICATION_JSON);
				post.setEntity(entity);
			}
			// 设置参数
			Builder customReqConf = RequestConfig.custom();
			customReqConf.setConnectTimeout(DEFAULT_CONNECTION_TIME_OUT);
			customReqConf.setSocketTimeout(DEFAULT_CONNECTION_TIME_OUT);
			post.setConfig(customReqConf.build());
			//设置消息体格式
			post.setHeader("Accept", "application/json");
			post.setHeader("Content-Type", "application/json;charset=UTF-8");
			if (header != null && header.size() > 0) {
				for (Map.Entry<String, String> entry : header.entrySet()) {
					post.setHeader(entry.getKey(), entry.getValue());
				}
			}
			if (url.startsWith("https")) {
				// 执行 Https 请求.
				client = createSSLInsecureClient();
				res = client.execute(post);
			} else {
				// 执行 Http 请求.
				client = HttpClientUtil.client;
				res = client.execute(post);
			}

			is = res.getEntity().getContent();
			String result = IOUtils.toString(is, DEFAULT_CHAR_SET);
			// header放进去
			if(null != returnHeader){
				Header[] headers = res.getAllHeaders();
				if(null !=headers && headers.length>0){
					for(Header head : headers){
						if(null != head){
							returnHeader.put(head.getName(), head.getValue());
						}
					}
				}
			}
			return result;
		} catch (Exception e) {
			// 加这步为了能统一记分布式日志，失败的情况
			log.error("doPost error", e);
			throw e;
		} finally {
			if(null != is){
				is.close();
			}

			post.releaseConnection();
			if (url.startsWith("https") && client != null && client instanceof CloseableHttpClient) {
				((CloseableHttpClient) client).close();
			}
		}
	}

	/**
	 * <p>
	 * 支持HTTP、HTTPS GET请求
	 * </p>
	 *
	 * <AUTHOR>
	 * @version V1.0
	 * @date 2017年5月19日
	 * @param url
	 * @return
	 * @throws Exception
	 */
	private static String doGet(String url, Map<String, String> header, Boolean doubleAuth, Map<String, String> returnHeader) throws Exception {
		HttpClient client = null;
		HttpGet get = new HttpGet(url);
		HttpResponse res = null;
		InputStream is = null;
		try {
			// 设置参数
			Builder customReqConf = RequestConfig.custom();
			customReqConf.setConnectTimeout(DEFAULT_CONNECTION_TIME_OUT);
			customReqConf.setSocketTimeout(DEFAULT_CONNECTION_TIME_OUT);
			get.setConfig(customReqConf.build());
			// 埋点库
//			TraceContext tcIn = TraceLocalUtil.getTreaceContext();
//			if (tcIn != null) {
//				get.setHeader(TraceLogEnum.TRACE_ID.getName(), tcIn.getTraceId());
//				get.setHeader(TraceLogEnum.SPAN_ID.getName(), tcIn.getSpanId());
//			}
			//设置消息体格式
//			get.setHeader("Accept", "application/json");
//			get.setHeader("Content-Type", "application/json;charset=UTF-8");

			if (header != null && header.size() > 0) {
				for (Map.Entry<String, String> entry : header.entrySet()) {
					get.setHeader(entry.getKey(), entry.getValue());
				}
			}

			if (url.startsWith("https")) {
				// 执行 Https 请求.
				client = createSSLInsecureClient();
				res = client.execute(get);
			} else {
				// 执行 Http 请求.
				client = HttpClientUtil.client;
				res = client.execute(get);
			}
			is = res.getEntity().getContent();
			String result = IOUtils.toString(is, DEFAULT_CHAR_SET);
			if(null != returnHeader){
				Header[] headers = res.getAllHeaders();
				if(null != header && headers.length > 0){
					for(Header head : headers){
						if(null != head){
							returnHeader.put(head.getName(), head.getValue());
						}
					}
				}
			}
			return result;
		} catch (Exception e) {
			// 加这步为了能统一记分布式日志，失败的情况
			log.error("doGet error", e);
			throw e;
		} finally {
			// 资源释放
			if(null != is){
				is.close();
			}

			get.releaseConnection();
			if (url.startsWith("https") && client != null && client instanceof CloseableHttpClient) {
				((CloseableHttpClient) client).close();
			}

		}
	}

	public static void doGetDefaultSecurityWithStream(ServletOutputStream outputStream, String url, String key, Map<String, String> header) throws Exception {
		HttpClient client = null;
		HttpGet get = new HttpGet(url);
		BufferedInputStream inputStream = null;
		try {
			if (url.indexOf("?") > -1) {
			} else {
			}
			// 设置参数
			Builder customReqConf = RequestConfig.custom();
			customReqConf.setConnectTimeout(DEFAULT_CONNECTION_TIME_OUT);
			customReqConf.setSocketTimeout(DEFAULT_CONNECTION_TIME_OUT);
			get.setConfig(customReqConf.build());
			HttpResponse res = null;
			if (header != null && header.size() > 0) {
				for (Map.Entry<String, String> entry : header.entrySet()) {
					get.setHeader(entry.getKey(), entry.getValue());
				}
			}
			if (url.startsWith("https")) {
				// 执行 Https 请求.
				client = createSSLInsecureClient();
				res = client.execute(get);
			} else {
				// 执行 Http 请求.
				client = HttpClientUtil.client;
				res = client.execute(get);
			}
			inputStream = new BufferedInputStream(res.getEntity().getContent());
			//写文件
			byte[] buffer = new byte[1024];
			int length;
			while ((length = inputStream.read(buffer)) > 0) {
				outputStream.write(buffer, 0, length);
			}
		} catch (Exception e) {
			// 加这步为了能统一记分布式日志，失败的情况
			log.error("doGet error", e);
			throw e;
		} finally {
			if (inputStream != null){
				inputStream.close();
			}
			if (outputStream != null){
				outputStream.flush();
				outputStream.close();
			}
			get.releaseConnection();
			if (url.startsWith("https") && client != null && client instanceof CloseableHttpClient) {
				((CloseableHttpClient) client).close();
			}
		}
	}

	/**
	 * <p>
	 * 支持HTTP、HTTPS POST请求
	 * </p>
	 *
	 * <AUTHOR>
	 * @version V1.0
	 * @date 2017年5月19日
	 * @param url
	 * @return
	 * @throws Exception
	 *
	 * @modificationHistory=========================逻辑或功能性重大变更记录
	 * @modify by user: pangdaiqiang 2017年5月19日
	 * @modify by reason:{方法名}:{原因}
	 * @since
	 */
	public static String transEncryptShankHandsPost(String url) throws Exception {
		HttpClient client = null;
		HttpResponse res = null;
		HttpPost post = new HttpPost(url);
		InputStream is = null;
		try {
			// 设置参数
			Builder customReqConf = RequestConfig.custom();
			customReqConf.setConnectTimeout(DEFAULT_CONNECTION_TIME_OUT);
			customReqConf.setSocketTimeout(DEFAULT_CONNECTION_TIME_OUT);
			post.setConfig(customReqConf.build());
			//设置消息体格式
			post.setHeader("Accept", "application/json");
			post.setHeader("Content-Type", "application/json;charset=UTF-8");

			if (url.startsWith("https")) {
				// 执行 Https 请求.
				client = createSSLInsecureClient();
				res = client.execute(post);
			} else {
				// 执行 Http 请求.
				client = HttpClientUtil.client;
				res = client.execute(post);
			}
			is = res.getEntity().getContent();
		} catch (Exception e) {

		}
		String result = IOUtils.toString(is, DEFAULT_CHAR_SET);
		return result;
	}



	/**
	 * <p>
	 * </p>
	 *
	 * <AUTHOR>
	 * @version V1.0
	 * @date 2016年3月2日 下午5:24:07
	 * @return
	 * @throws GeneralSecurityException
	 */
	public static CloseableHttpClient createSSLInsecureClient() throws GeneralSecurityException {
		try {
			SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {

				@Override
				public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
					return true;
				}
			}).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, new HostnameVerifier() {

				@Override
				public boolean verify(String hostname, SSLSession session) {
					return true;
				}
			});
			return HttpClients.custom().setSSLSocketFactory(sslsf).build();
		} catch (GeneralSecurityException e) {
			throw e;
		}
	}

	public static String doPostText(String url, String jsonText) throws Exception {
		return doPost(url, jsonText);
	}

	private static String doPost(String url, String jsonText) throws Exception {
		HttpClient client = null;
		HttpResponse res = null;
		HttpPost post = new HttpPost(url);
		InputStream is = null;
		try {
			if (jsonText != null && !jsonText.isEmpty()) {
				StringEntity entity = new StringEntity(jsonText, ContentType.APPLICATION_JSON);
				post.setEntity(entity);
			}
			// 设置参数
			Builder customReqConf = RequestConfig.custom();
			customReqConf.setConnectTimeout(DEFAULT_CONNECTION_TIME_OUT);
			customReqConf.setSocketTimeout(DEFAULT_CONNECTION_TIME_OUT);
			post.setConfig(customReqConf.build());
			//设置消息体格式
			post.setHeader("Accept", "*/*");
			post.setHeader("Content-Type", "text/plain;charset=UTF-8");
			if (url.startsWith("https")) {
				// 执行 Https 请求.
				client = createSSLInsecureClient();
				res = client.execute(post);
			} else {
				// 执行 Http 请求.
				client = HttpClientUtil.client;
				res = client.execute(post);
			}

			is = res.getEntity().getContent();
			String result = IOUtils.toString(is, DEFAULT_CHAR_SET);
			return result;
		} catch (Exception e) {
			// 加这步为了能统一记分布式日志，失败的情况
			log.error("doPost error", e);
			throw e;
		} finally {
			if(null != is){
				is.close();
			}

			post.releaseConnection();
			if (url.startsWith("https") && client != null && client instanceof CloseableHttpClient) {
				((CloseableHttpClient) client).close();
			}
		}
	}

	public static String doPostFormDefaultSecurity(String url, Map<String, String> parmMap, Map<String, String> header) throws Exception {
		return doPostForm(url, parmMap, header, null);
	}


	private static String doPostForm(String url, Map<String, String> parmMap, Map<String, String> header, Map<String, String> returnHeader) throws Exception {
		HttpClient client = null;
		HttpResponse res = null;
		HttpPost post = new HttpPost(url);
		InputStream is = null;
		try {
			ArrayList<BasicNameValuePair> paramList = new ArrayList<>();
			parmMap.forEach((key, value) -> paramList.add(new BasicNameValuePair(key, value)));

			if (Objects.nonNull(parmMap) && parmMap.size() >0) {
				post.setEntity(new UrlEncodedFormEntity(paramList, "UTF-8"));
			}


			// 设置参数
			Builder customReqConf = RequestConfig.custom();
			customReqConf.setConnectTimeout(DEFAULT_CONNECTION_TIME_OUT);
			customReqConf.setSocketTimeout(DEFAULT_CONNECTION_TIME_OUT);
			post.setConfig(customReqConf.build());
			//设置消息体格式
			post.setHeader("Accept", "application/json");
			post.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
			if (header != null && header.size() > 0) {
				for (Map.Entry<String, String> entry : header.entrySet()) {
					post.setHeader(entry.getKey(), entry.getValue());
				}
			}
			if (url.startsWith("https")) {
				// 执行 Https 请求.
				client = createSSLInsecureClient();
				res = client.execute(post);
			} else {
				// 执行 Http 请求.
				client = HttpClientUtil.client;
				res = client.execute(post);
			}

			is = res.getEntity().getContent();
			String result = IOUtils.toString(is, DEFAULT_CHAR_SET);
			// header放进去
			if(null != returnHeader){
				Header[] headers = res.getAllHeaders();
				if(null !=headers && headers.length>0){
					for(Header head : headers){
						if(null != head){
							returnHeader.put(head.getName(), head.getValue());
						}
					}
				}
			}
			return result;
		} catch (Exception e) {
			// 加这步为了能统一记分布式日志，失败的情况
			log.error("doPost error", e);
			throw e;
		} finally {
			if(null != is){
				is.close();
			}

			post.releaseConnection();
			if (url.startsWith("https") && client != null && client instanceof CloseableHttpClient) {
				((CloseableHttpClient) client).close();
			}
		}
	}
}
