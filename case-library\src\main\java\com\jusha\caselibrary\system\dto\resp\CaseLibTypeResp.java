package com.jusha.caselibrary.system.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.Date;

/**
 * @ClassName CaseLibTypeResp
 * @Description 病例库类型管理响应类
 * <AUTHOR>
 * @Date 2025/7/3 16:30
 **/
@Data
public class CaseLibTypeResp {

    @ApiModelProperty(value = "病例库类型id")
    private Long caseTypeId;

    @ApiModelProperty(value = "病例库类型名称")
    private String caseTypeName;

    @ApiModelProperty(value = "病例库地址")
    private String address;

    @ApiModelProperty(value = "是否需要审核0不需要1需要")
    private String audit;

    @ApiModelProperty(value = "修改时间")
    private String updateTime;

}
