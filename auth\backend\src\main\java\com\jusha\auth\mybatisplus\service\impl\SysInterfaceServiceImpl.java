package com.jusha.auth.mybatisplus.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jusha.auth.mybatisplus.entity.SysInterface;
import com.jusha.auth.mybatisplus.mapper.SysInterfaceMapper;
import com.jusha.auth.mybatisplus.service.SysInterfaceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 菜单权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Service
public class SysInterfaceServiceImpl extends ServiceImpl<SysInterfaceMapper, SysInterface> implements SysInterfaceService {

}
