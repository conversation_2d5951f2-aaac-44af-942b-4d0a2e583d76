# 病例导出功能说明

## 功能概述

病例导出功能提供了完整的异步导出解决方案，支持将病例数据导出为Excel文件，包含以下特性：

- 异步导出处理，支持大数据量导出
- 支持复杂数据结构（包含List字段）的导出
- 支持单Sheet和多Sheet导出模式
- 支持导出进度跟踪
- 支持List字段的合并单元格处理
- 完善的异常处理和日志记录
- 支持导出文件的自动清理

## 核心组件

### 1. DTO类

#### CaseExportDataDto
- 病例导出数据DTO，继承自DeptCaseDetailResp
- 包含所有病例相关信息和导出元数据

#### CaseExportRequestDto
- 病例导出请求DTO
- 包含导出参数配置和格式设置

### 2. 工具类

#### CaseExportUtil
- 核心导出工具类
- 使用fastexcel 1.2.0进行Excel操作
- 支持单Sheet和多Sheet导出
- 支持复杂数据结构的处理

### 3. 任务类

#### CaseDetailExportTask
- 异步导出任务执行器
- 支持泛型参数 `<D extends CaseExportDataDto, T extends CaseExportRequestDto>`
- 提供进度跟踪和结果管理

### 4. 控制器

#### CaseExportController
- 提供REST API接口
- 支持任务提交、进度查询、结果获取、文件下载等操作

## 使用方法

### 1. 基本导出

```java
// 创建导出请求
CaseExportRequestDto requestDto = new CaseExportRequestDto();
requestDto.setCaseIds(Arrays.asList(1L, 2L, 3L));
requestDto.setExportUserId(123L);
requestDto.setExportUserName("张三");

// 提交导出任务
CompletableFuture<String> future = caseDetailExportTask.executeExportTask(requestDto);
```

### 2. 高级配置

```java
CaseExportRequestDto requestDto = new CaseExportRequestDto();
requestDto.setCaseIds(caseIds);
requestDto.setExportUserId(userId);
requestDto.setFileName("病例数据导出_" + DateUtil.today());

// 配置导出选项
requestDto.setIncludeStudyDetails(true);
requestDto.setIncludeTagInfo(true);
requestDto.setIncludeFollowInfo(true);
requestDto.setIncludeDiseaseOverview(true);

// 配置格式选项
CaseExportRequestDto.ExportFormatConfig formatConfig = new CaseExportRequestDto.ExportFormatConfig();
formatConfig.setMultipleSheets(true);
formatConfig.setMergeListCells(true);
formatConfig.setListSeparator("；");
formatConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
requestDto.setFormatConfig(formatConfig);
```

### 3. REST API使用

#### 提交导出任务
```http
POST /api/case/export/submit
Content-Type: application/json

{
  "caseIds": [1, 2, 3],
  "exportUserId": 123,
  "exportUserName": "张三",
  "includeStudyDetails": true,
  "includeTagInfo": true,
  "includeFollowInfo": true,
  "includeDiseaseOverview": true,
  "formatConfig": {
    "multipleSheets": true,
    "mergeListCells": true,
    "listSeparator": "；",
    "dateFormat": "yyyy-MM-dd HH:mm:ss"
  }
}
```

#### 查询导出进度
```http
GET /api/case/export/progress/{taskId}
```

#### 获取导出结果
```http
GET /api/case/export/result/{taskId}
```

#### 下载导出文件
```http
GET /api/case/export/download/{taskId}
```

## 导出格式说明

### 单Sheet模式
所有数据导出到一个工作表中，List字段使用分隔符连接。

### 多Sheet模式
- **病例基本信息**: 主要的病例信息
- **检查报告详情**: 详细的检查报告信息
- **标签信息**: 病例标签信息
- **随访信息**: 随访记录信息

## 配置参数

### application-export.yml
```yaml
case:
  export:
    temp:
      path: ${java.io.tmpdir}/case-export/  # 临时文件路径
    file:
      retention:
        days: 7  # 文件保留天数
    max:
      export:
        count: 1000  # 最大导出数量
```

## 性能优化

1. **分批查询**: 避免一次性查询大量数据
2. **异步处理**: 使用专用线程池处理导出任务
3. **内存管理**: 及时释放不需要的对象
4. **文件清理**: 定期清理过期的导出文件

## 异常处理

1. **参数验证**: 导出前验证请求参数
2. **数据检查**: 检查病例数据的完整性
3. **文件操作**: 处理文件读写异常
4. **任务超时**: 处理长时间运行的任务

## 日志记录

系统会记录以下关键操作的日志：
- 导出任务的开始和结束
- 数据查询和处理过程
- 文件生成和保存
- 异常和错误信息

## 注意事项

1. **数据量限制**: 建议单次导出不超过1000条记录
2. **文件大小**: Excel文件单个单元格最大支持32767个字符
3. **并发控制**: 同一用户同时只能有一个导出任务
4. **文件清理**: 导出文件会在7天后自动清理
5. **权限控制**: 确保用户有相应病例的查看权限

## 扩展说明

### 自定义导出格式
可以通过继承CaseExportDataDto和CaseExportRequestDto来实现自定义的导出格式。

### 添加新的导出类型
可以在CaseExportUtil中添加新的导出方法来支持其他格式（如CSV、PDF等）。

### 集成通知系统
可以在导出完成后发送邮件或短信通知用户。