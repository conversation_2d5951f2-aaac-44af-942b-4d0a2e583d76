<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px" @submit.native.prevent>
      <el-form-item label="平台名称" prop="platName">
        <el-input
          maxlength="50"
          v-model="queryParams.platName"
          placeholder="请输入平台名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-if="keyWords.includes('add')"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="platList">
      <el-table-column label="平台ID" align="center" prop="platId" />
      <el-table-column label="平台名称" prop="platName" />
      <!-- <el-table-column label="平台地址" align="center" prop="platHost" /> -->
      <el-table-column label="平台标识" align="center" prop="platTag" />
      <!-- <el-table-column label="API可见性" align="center" width="200">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.visible"
            active-value="0"
            inactive-value="1"
            @change="handleVisibleChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column> -->
      <el-table-column label="创建时间" align="center" prop="createTime" width="180"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-if="keyWords.includes('edit')"
          >修改</el-button>
          <el-button
            v-if="scope.row.platId!=0 && keyWords.includes('remove')"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改业务平台信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="平台名称" prop="platName">
          <el-input v-model="form.platName" placeholder="请输入平台名称" />
        </el-form-item>
        <!-- <el-form-item label="平台地址" prop="platHost">
          <el-input v-model="form.platHost" placeholder="请输入平台地址" />
        </el-form-item> -->
        <el-form-item label="平台标识" prop="platTag">
          <el-input v-model="form.platTag" placeholder="请输入平台标识" />
        </el-form-item>
        <!-- <el-form-item label="API可见性">
          <el-radio-group v-model="form.visible">
            <el-radio
              v-for="dict in dict.type.plat_visible"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPlat, getPlat, delPlat, addPlat, updatePlat,changePlatVisible} from "@/api/system/plat";

export default {
  name: "Plat",
  dicts: ['plat_visible'],
  data() {
    return {
      keyWords : [],
      // 遮罩层
      loading: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 业务平台信息表格数据
      platList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        platName: [
          { required: true, message: "平台名称不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '平台名称长度必须介于 2 和 20 之间', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/,
            message: "仅支持中文、英文、数字及下划线",
            trigger: "blur"
          }
        ],
        // platHost: [
        //   { min: 8, max: 20, message: '平台地址长度必须介于 8 和 50 之间', trigger: 'blur' }
        // ],
        platTag: [
          { min: 2, max: 20, message: '平台标识长度必须介于 2 和 20 之间', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9_]{2,20}$/,
            message: "仅支持英文字母、数字及下划线",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getButton();
    this.getList();
  },
  methods: {
    getButton(){
      this.keyWords = this.$route.meta.keyWords
    },
    /** 查询业务平台信息列表 */
    getList() {
      this.loading = true;
      listPlat(this.queryParams).then(response => {
        this.platList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        platId: null,
        platName: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加业务平台信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const platId = row.platId
      getPlat(platId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改业务平台信息";
      });
    },
    // 修改可见状态
    handleVisibleChange(row) {
      let text = row.visible === "0" ? "可见" : "不可见";
      this.$modal.confirm('确认要将"'+ row.platName +'"设置为'  + text +  '吗？').then(function() {
        return changePlatVisible(row.platId, row.visible);
      }).then(() => {
        this.$modal.msgSuccess("设置成功");
      }).catch(function() {
        row.visible = row.visible === "0" ? "1" : "0";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.platId != null) {
            updatePlat(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPlat(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const platId = row.platId;
      this.$modal.confirm('是否确认删除？').then(function() {
        return delPlat(platId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
  }
};
</script>
