##需要修改的配置
SVC_IP: *************
#nacos
NACOS_PORT: 8848
NACOS_NAMESPACE: pacs-test   #命名空间ID,为空表示取默认命名空间(public)
#服务名和端口
SERVER_PORT: 9088
SERVER_NAME: caselibrary


server:
  port: ${SERVER_PORT}
  servlet:
    context-path: /${SERVER_NAME}
  undertow:
    threads:
      io: 16                 #IO线程数
      worker: 512            #阻塞任务线程池
    buffer-size: 1024
    direct-buffers: true
    max-parameters: 2000

spring:
  application:
    name: ${SERVER_NAME}
  cloud:
    nacos:
      config:
        server-addr: ${SVC_IP}:${NACOS_PORT}
        namespace:  ${NACOS_NAMESPACE}
        group: DEFAULT_GROUP
        name: ${SERVER_NAME}
        file-extension: yml
      discovery:
        server-addr: ${SVC_IP}:${NACOS_PORT}
        namespace:  ${NACOS_NAMESPACE}

feign:
  client:
    config:
      default:
        connectTimeout: 2000  #连接超时
        readTimeout: 10000    #读超时
  httpclient:
    enabled: true