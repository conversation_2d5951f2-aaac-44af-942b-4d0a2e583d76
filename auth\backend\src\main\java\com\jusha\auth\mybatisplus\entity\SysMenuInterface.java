package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 接口菜单关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Getter
@Setter
@TableName("sys_menu_interface")
@ApiModel(value = "SysMenuInterface对象", description = "接口菜单关联表")
public class SysMenuInterface {

    @ApiModelProperty(value = "接口ID")
    @TableField("interface_id")
    private Long interfaceId;

    @ApiModelProperty(value = "菜单ID")
    @TableField("menu_id")
    private Long menuId;

    public SysMenuInterface(Long menuId,Long interfaceId) {
        this.menuId = menuId;
        this.interfaceId = interfaceId;
    }
}
