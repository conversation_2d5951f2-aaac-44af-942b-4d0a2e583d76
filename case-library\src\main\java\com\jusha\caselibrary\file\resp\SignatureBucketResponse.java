package com.jusha.caselibrary.file.resp;

import com.jusha.caselibrary.file.dto.PostPolicyCOS;
import com.jusha.caselibrary.file.dto.PostPolicyMinio;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取文件上传临时签名
 *
 * <AUTHOR>
 * @date 2025/02/07
 **/
@Data
public class SignatureBucketResponse {

    @ApiModelProperty(value = "服务类型 1minio 2腾讯云对象存储COS 3NAS")
    private String serverType;

    @ApiModelProperty(value = "minio上传凭证")
    private PostPolicyMinio postPolicyMinio;

    @ApiModelProperty(value = "腾讯云COS上传凭证")
    private PostPolicyCOS postPolicyCOS;
}
