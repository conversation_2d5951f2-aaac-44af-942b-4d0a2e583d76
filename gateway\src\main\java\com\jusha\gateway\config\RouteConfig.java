package com.jusha.gateway.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * 自定义路由
 */
@Configuration
public class RouteConfig {

    @Value("${SERVICE_NAMES:pacs}")
    private String serviceNames;
    

    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        List<String> serviceList = Arrays.asList(serviceNames.trim().split(","));

        RouteLocatorBuilder.Builder routes = builder.routes();
        serviceList.stream().forEach(service -> {
            if(StringUtils.isNotBlank(service)){
                String path = StringUtils.join("/", service, "/**");
                String uri = StringUtils.join("lb://", service);
                routes.route(service, p -> p.path(path).uri(uri));
            }
        });

        return routes.build();
    }

}