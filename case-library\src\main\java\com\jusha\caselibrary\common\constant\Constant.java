package com.jusha.caselibrary.common.constant;

/**
 * 常量
 */
public class Constant {

    /**
     * 线上环境的 spring.profiles.active
     */
    public static final String SPRING_PROFILES_PROD = "prod";

    /**
     * Header中：traceId的键名
     */
    public static final String TRACEID = "traceId";

    /**
     * Header中：token的键名
     */
    public static final String TOKEN_KEY = "Authorization";

    /**
     * 网络环境 redis key
     */
    public static final String NETWORK_ENVIRONMENT = "network_environment:";

    /**
     * 当前用户网络环境: LAN 内网, WAN 外网
     */
    public static final String NETWORK_LAN = "LAN";
    public static final String NETWORK_WAN = "WAN";

    /**
     * Header中：platId的键名
     */
    public static final String PLAT_ID = "platId";

    /**
     * dicom上传任务名
     */
    public static final String TASK_NAME_DICOM_UPLOAD = "dicomUpload";

    /**
     * 异步任务结果在redis中存放过期时间（分钟）
     */
    public static final Integer TASK_OVER_TIME = 30;

    /**
     * 病例编码自增值长度
     */
    public static final Integer CASE_NUMBER_LEN = 8;

    /**
     * C-STORE：dicom文件后缀
     */
    public static final String DCM_EXT = ".dcm";

    /**
     * 压缩包后缀
     */
    public static final String ZIP = ".zip";


    /**
     * 删除标识；0未删除，1删除
     */
    public static final Boolean DEL_FLAG_NOT = false;
    public static final Boolean DEL_FLAG_YES = true;

    /**
     * 中心医院：0-是 1-否
     */
    public static final String CENTER_YES = "0";
    public static final String CENTER_NO = "1";

    /**
     * 角色类型: 1管理员 2老师 3学员
     */
    public static final String ROLE_TYPE_1 = "1";
    public static final String ROLE_TYPE_2 = "2";
    public static final String ROLE_TYPE_3 = "3";

    /**
     * 拓展病例类型：1-教学病例 2-练习病例
     */
    public static final Short CASE_EXT_TYPE_1 = 1;
    public static final Short CASE_EXT_TYPE_2 = 2;

    /**
     * 拓展病例审核状态：0-待审核 1-通过 2-驳回
     */
    public static final Short CASE_EXT_STATUS_0 = 0;
    public static final Short CASE_EXT_STATUS_1 = 1;
    public static final Short CASE_EXT_STATUS_2 = 2;

    /**
     * 病例分类层级：1-器官系统 2-疾病类型
     */
    public static final Short CASE_CLASSIFY_LEVEL_1 = 1;
    public static final Short CASE_CLASSIFY_LEVEL_2 = 2;

    /**
     * 病例关联文件类型:3-关联资料 4-参考(文件) 5-参考(课程)
     */
    public static final Short REL_TYPE_3 = 3;
    public static final Short REL_TYPE_4 = 4;
    public static final Short REL_TYPE_5 = 5;

    /**
     * 消息类型：12-病例审核 13-病例审核结果
     */
    public static final Short MESSAGE_TYPE_12 = 12;
    public static final Short MESSAGE_TYPE_13 = 13;

    /**
     * 消息读取状态：1-未读 2-已读
     */
    public static final Short MESSAGE_READ_STATUS_1 = 1;
    public static final Short MESSAGE_READ_STATUS_2 = 2;

    /**
     * 消息处理状态：1-未处理 2-已处理
     */
    public static final Short MESSAGE_HANDLE_STATUS_1 = 1;
    public static final Short MESSAGE_HANDLE_STATUS_2 = 2;

    /**
     * 病例导入、导出详情状态：0-未开始 1-进行中 2-成功 3-失败
     */
    public static final Short CASE_OUT_STATE_0 = 0;
    public static final Short CASE_OUT_STATE_1 = 1;
    public static final Short CASE_OUT_STATE_2 = 2;
    public static final Short CASE_OUT_STATE_3 = 3;

    public static final String SEPARATOR = ",";

    public static final String EMPTY_STRING = "";

    /** 根目录 */
    public static final String DEPT_ROOT = "0";

    public static final Long ZERO_LONG = 0L;

    public static final Long ALL_MINUS1L = -1L;

    public final static boolean NOT_UNIQUE = false;

    public final static boolean UNIQUE = true;

    /**
     * 通用删除标识
     */
    public static final String DELETE_FLAG = "1";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 病例库目录名称
     */
    public static final String CASE_LIB_MENU_NAME = "科室病例库";

    /**
     * 权限系统顶级目录菜单Id
     */
    public final static Long ROOT_MENU_ID = 0L;

    /** 菜单类型（目录） */
    public static final String TYPE_DIR = "M";

    /** 菜单类型（菜单） */
    public static final String TYPE_MENU = "C";

    /** 菜单类型（按钮） */
    public static final String TYPE_BUTTON = "F";

    /** 正常状态 */
    public static final String NORMAL = "0";

    /** Redis字典前缀 */
    public static final String REDIS_DICT_KEY = "sys_dict:";


    /**
     * ES相关
     **/
    //科室病例库索引类型
    public static final String DEP_CASE_INDEX_NAME = "department";

    //个人病例库索引类型
    public static final String PERSON_CASE_INDEX_NAME = "personal";

    //索引Id
    public static final String ES_INDEX_ID = "caseId";
    public static final String ES_USER_INDEX_ID = "userCaseId";

    //ES同步操作类型
    public static final String OPERATION_TYPE_CREATE = "CREATE";
    public static final String OPERATION_TYPE_UPDATE = "UPDATE";
    public static final String OPERATION_TYPE_DELETE = "DELETE";
    public static final String OPERATION_TYPE_BATCH_CREATE = "BATCH_CREATE";
    public static final String OPERATION_TYPE_BATCH_UPDATE = "BATCH_UPDATE";
    public static final String OPERATION_TYPE_BATCH_DELETE = "BATCH_DELETE";


    /**
     * 凭证过期时间（分钟）
     */
    public static final long upload_expire_time = 10L;

    public static final String UPLOAD_BUCKET = "upload-bucket";

    public static final String FILE_SEPARATOR = "/";

}
