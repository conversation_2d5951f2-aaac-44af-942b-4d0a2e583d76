package com.jusha.ris.docking.mock.task;

import com.jusha.ris.docking.mock.dto.StudyInfoDto;
import com.jusha.ris.docking.mock.service.MockDockingService;
import com.jusha.ris.docking.mock.utils.ChineseUtil;
import com.jusha.ris.docking.mock.utils.IdentityUtil;
import com.jusha.ris.docking.mock.utils.PhoneNumberUtil;
import com.jusha.ris.docking.mock.utils.RandomNumberStringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Random;

/**
 * <AUTHOR>
 * @title MockDockingTask
 * @description 模拟生成检查数据对接任务
 * @date 2025/2/24
 */

@Component
public class MockDockingTask {

    @Autowired
    private MockDockingService mockDockingService;

    private final String[] patientTypes = new String[] {"住院", "门诊", "急诊", "体检"};

    private final String[] studyTypes = new String[] {"CT", "MR", "DR"};

    private final String[] applyDoctorArray = new String[] {"韩束", "周洋", "王川", "林之路", "易小川", "张兰", "李冰", "韩梅梅"};

    private final String[] artificerArray = new String[]{"李小伟", "王芳", "张文涛", "刘洋", "陈志强", "杨静", "赵子磊", "黄梅", "周国鹏",
            "吴莉", "徐晓军", "孙玉婷", "郭欣怡", "何丽华", "林峰", "高美娜", "郑华", "谢丹", "宋杰", "曹丽娜", "邓丽", "崔宇", "田芳",
            "魏宁", "蒋志平", "韩雪", "冯子涛", "曾华", "吕婷婷", "胡亮", "任小静", "尹峰", "潘志强", "杜丽", "钟美娜"};

    private final String[] hospitalArray = new String[]{"盘城街道社区医院", "泰山接到社区医院", "顶山街道社区医院"};

    /**
     * 自动模拟数据
     */
//    @Scheduled(cron = "0 */5 8-23 * * *")
    public void mockDockingStudyInfo() {
        StudyInfoDto studyInfoDto = new StudyInfoDto();
        studyInfoDto.setPatientName(ChineseUtil.randomChineseName());
        studyInfoDto.setBirthDate(LocalDate.now().minusYears(50));
        studyInfoDto.setPatientSex(new Random().nextInt(3));
        studyInfoDto.setIdCard(IdentityUtil.getRandomID());
        studyInfoDto.setPhone(PhoneNumberUtil.generatePhoneNumber());
        studyInfoDto.setPatientNo(RandomNumberStringUtil.generateRandomNumberString(12));
        studyInfoDto.setApplyNo(RandomNumberStringUtil.generateRandomNumberString(10));
        studyInfoDto.setPatientType(patientTypes[new Random().nextInt(patientTypes.length)]);
        studyInfoDto.setOutInPatientNo(RandomNumberStringUtil.generateRandomNumberString(14));
        studyInfoDto.setPatientAge("50岁");
        studyInfoDto.setStudyType(studyTypes[new Random().nextInt(studyTypes.length)]);
        studyInfoDto.setStudyParts("胸部");
        studyInfoDto.setStudyItems("测试测试检查项目");
        studyInfoDto.setApplyDepartment("测试科室");
        studyInfoDto.setApplyDoctor(applyDoctorArray[new Random().nextInt(applyDoctorArray.length)]);
        studyInfoDto.setApplyTime(LocalDateTime.now().minusHours(1));
        studyInfoDto.setStudyPurpose("检查检查");
        studyInfoDto.setPhysicalSign("体温36.5℃   脉搏90次/分   呼吸19次/分   血压134/84mmHg");
        studyInfoDto.setSelfComplaints("胸闷、气短");
        studyInfoDto.setHistoryDisease("1月余前体力活动后出现胸闷、气短，伴纳差，无发热、咳嗽、咳痰、出汗，无恶心、呕吐，无大汗、乏力，无头晕、头痛等。就诊于河南科技大学第一附属医院，查胸部CT示：1、双肺感染，较前增多，建议结合临床；2、右肺中叶实变，较前减少；3、双肺上叶肺气肿；4、双侧胸膜稍增厚。近来上述症状加重，性质同前，活动后加重，为为进一步治疗今来我院，门诊以肺部感染收入。自发病来，神志清，精神可，饮食差，睡眠可，大小便正常，体重无明显变化。");
        studyInfoDto.setClinicalDiagnosis(" 1:感染性发热 肺部感染");
        studyInfoDto.setStudyNo(RandomNumberStringUtil.generateRandomNumberString(9));
        studyInfoDto.setAccessNumber("");
        studyInfoDto.setStudyUid("1.2.156.112605.189250954516775.************.2.12776.15195");
        studyInfoDto.setStudyDevice("");
        studyInfoDto.setStudyTime(LocalDateTime.now().minusMinutes(30));
        studyInfoDto.setArtificer(artificerArray[new Random().nextInt(artificerArray.length)]);


        mockDockingService.addStudyApplyByDocking(studyInfoDto);

    }


}
