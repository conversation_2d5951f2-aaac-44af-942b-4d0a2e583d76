package com.jusha.ris.docking.study.service;

import com.jusha.ris.docking.study.dto.req.StudyReq;
import com.jusha.ris.docking.study.dto.resp.StudyResp;

import java.util.List;

/**
 * <AUTHOR>
 * @title MockDockingService
 * @description
 * @date 2025/2/24
 */
public interface StudyService {

    /**
     * 模拟对接数据
     *
     * @param studyReq
     */
    public List<StudyResp> queryStudyList(StudyReq studyReq);
}
