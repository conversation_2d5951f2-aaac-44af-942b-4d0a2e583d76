package com.jusha.caselibrary.file.controller;

import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.file.dto.FileReportDto;
import com.jusha.caselibrary.file.dto.req.StreamUploadReq;
import com.jusha.caselibrary.file.dto.rsp.MinioMsgRsp;
import com.jusha.caselibrary.file.resp.FileUploadResp;
import com.jusha.caselibrary.file.resp.SignatureBucketResponse;
import com.jusha.caselibrary.file.service.FileService;
import com.jusha.caselibrary.mybatisplus.entity.Resource;
import com.jusha.caselibrary.mybatisplus.service.ResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName FileController
 * @Description 文件管理控制器
 * <AUTHOR>
 * @Date 2025/7/11 11:00
 **/
@Api(tags = "文件上传")
@RequestMapping("/file")
@Slf4j
@RequiredArgsConstructor
@RestController
public class FileController {

    private final ResourceService resourceService;

    @ApiOperation("单个文件上传到服务器(minio/对象存储)")
    @PostMapping(value = "/upload")
    public ResultBean<FileUploadResp> upload(@ApiParam("文件") @RequestParam("file") MultipartFile file) {

        FileUploadResp uploadResp = new FileUploadResp();
        uploadResp.setMultipartFile(file);
        uploadResp.setSeq(1);
        //调单个上传
        FileService fileService = ContextHolder.getBean(StringUtils.join("fileService_", LoginUtil.getNetwork()), FileService.class);
        fileService.upload(uploadResp);
        return ResultBean.success(uploadResp);
    }


    @ApiOperation("多个文件上传到服务器(minio/对象存储)")
    @PostMapping(value = "/uploadMulti")
    public ResultBean<List<FileUploadResp>> uploadMulti(@ApiParam("文件") @RequestParam("file") MultipartFile[] file) {
        FileService fileService = ContextHolder.getBean(StringUtils.join("fileService_", LoginUtil.getNetwork()), FileService.class);
        List<FileUploadResp> resp = fileService.uploadMulti(file);
        return ResultBean.success(resp);
    }

    @ApiOperation("文件删除")
    @PostMapping(value = "/remove")
    public ResultBean<Void> deleteFile(@ApiParam("资源文件ID") @RequestParam Long resourceId) {
        FileService fileService = ContextHolder.getBean(StringUtils.join("fileService_", LoginUtil.getNetwork()), FileService.class);
        fileService.deleteFile(resourceId);
        return ResultBean.success();
    }

    @ApiOperation("获取(内网)minio文件服务信息")
    @GetMapping("/lan/msg")
    public ResultBean<MinioMsgRsp> minioMsg(){
        MinioMsgRsp rsp = new MinioMsgRsp();
        String network = LoginUtil.getNetwork();
        rsp.setNetwork(network);
        if(Constant.NETWORK_LAN.equals(network)){
            BeanUtils.copyProperties(ContextHolder.propertiesBean(), rsp);
        }
        return ResultBean.success(rsp);
    }

    /**
     * 大文件流式上传(minio/对象存储)
     */
    @PostMapping(value = "/stream/upload/forbid")
    public ResultBean<Void> streamUpload(@Valid @RequestBody StreamUploadReq req) {
        FileService fileService = ContextHolder.getBean(StringUtils.join("fileService_", LoginUtil.getNetwork()), FileService.class);
        fileService.streamUpload(req);
        return ResultBean.success();
    }


    @ApiOperation("前端自己上传文件后，上报给后端")
    @PostMapping(value = "/report")
    public ResultBean<Resource> report(@Valid @RequestBody FileReportDto fileReportDto) {
        FileService fileService = ContextHolder.getBean(StringUtils.join("fileService_", LoginUtil.getNetwork()), FileService.class);
        Resource resp = fileService.report(fileReportDto);
        return ResultBean.success(resp);
    }

    @ApiOperation("查询所有资源列表")
    @PostMapping(value = "/forbid/list")
    public ResultBean<List<Resource>> resourceList() {
        List<Resource> resp = resourceService.list();
        return ResultBean.success(resp);
    }

    /**
     * Upload result bean.
     * @return the result bean
     */
    @ApiOperation(value = "获取上传策略")
    @GetMapping(value = "/getPostPolicy")
    public ResultBean<SignatureBucketResponse> getPostPolicy(@RequestParam("fileName")String fileName, String bucketName){
        FileService fileService = ContextHolder.getBean(StringUtils.join("fileService_", LoginUtil.getNetwork()), FileService.class);
        SignatureBucketResponse signatureBucketResponse = fileService.getPostPolicy(fileName,bucketName);
        return ResultBean.success(signatureBucketResponse);
    }
}
