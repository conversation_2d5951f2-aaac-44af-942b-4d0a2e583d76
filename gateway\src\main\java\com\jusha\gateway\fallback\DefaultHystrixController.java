package com.jusha.gateway.fallback;

import com.jusha.gateway.constant.Constant;
import com.jusha.gateway.resp.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

/**
 * 默认降级Controller
 */
@Slf4j
@RestController
public class DefaultHystrixController {

    @RequestMapping("/defaultFallback")
    public ResultBean defaultfallback(ServerWebExchange exchange){
        String traceId = "";
        if(exchange != null){
            ServerHttpRequest serverRequest = exchange.getRequest();
            if(serverRequest != null){
                HttpHeaders headers = serverRequest.getHeaders();
                if(headers != null){
                    traceId = headers.getFirst(Constant.TRACEID);
                }
            }
        }
        log.error("DefaultHystrixController,traceid:{}", traceId);

        return ResultBean.error(Constant.COMMON_ERROR, "系统繁忙...");
    }

}