package com.jusha.ris.docking.mock.service;

import com.jusha.ris.docking.common.exception.BusinessException;
import com.jusha.ris.docking.common.resp.ResultBean;
import com.jusha.ris.docking.feign.RisBaseClient;
import com.jusha.ris.docking.feign.StudyClient;
import com.jusha.ris.docking.mock.dto.HospitalInfoResp;
import com.jusha.ris.docking.mock.dto.StudyInfoDto;
import com.jusha.ris.docking.mock.dto.req.StudyInfoReq;
import com.jusha.ris.docking.mock.utils.RandomNumberStringUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR>
 * @title MockDockingService
 * @description
 * @date 2025/2/24
 */
@Service
@RequiredArgsConstructor
public class MockDockingService {

    private static final Logger log = LoggerFactory.getLogger(MockDockingService.class);

    @Autowired
    private StudyClient studyClient;

    @Autowired
    private RisBaseClient risBaseClient;

    /**
     * 对接添加检查、申请单信息
     *
     * @param studyInfoDto
     */
    public void addStudyApplyByDocking(StudyInfoDto studyInfoDto) {
        //添加医院、分组信息
        ResultBean<List<HospitalInfoResp>> resultBean1 = risBaseClient.searchHospitalListForbid();
        List<HospitalInfoResp> hospitalInfoRespList = resultBean1.getData();
        HospitalInfoResp hospital = hospitalInfoRespList.get(new Random().nextInt(hospitalInfoRespList.size()));
        studyInfoDto.setUploadHospitalId(hospital.getHospitalId());
        studyInfoDto.setGroupId(hospital.getGroupId());
        ResultBean resultBean = studyClient.addStudyApplyByDocking(studyInfoDto);
        if (resultBean.getState()) {
            log.info("--------------对接成功一条数据");
        }
    }

    /**
     * 模拟对接数据
     *
     * @param studyInfoReq
     */
    public void addStudyApplyMock(StudyInfoReq studyInfoReq) {
        StudyInfoDto studyInfoDto = new StudyInfoDto();
        BeanUtils.copyProperties(studyInfoReq, studyInfoDto);
        ResultBean<List<HospitalInfoResp>> resultBean1 = risBaseClient.searchHospitalListForbid();
        List<HospitalInfoResp> hospitalInfoRespList = resultBean1.getData();
        HospitalInfoResp hospital = hospitalInfoRespList.get(new Random().nextInt(hospitalInfoRespList.size()));
        studyInfoDto.setUploadHospitalId(hospital.getHospitalId());
        studyInfoDto.setGroupId(hospital.getGroupId());
        ResultBean resultBean = studyClient.addStudyApplyByDocking(studyInfoDto);
        if (resultBean.getState()) {
            log.info("--------------对接成功一条数据");
        }
        if (!resultBean.getState()) {
            throw new BusinessException(resultBean.getMessage());
        }
    }
}
