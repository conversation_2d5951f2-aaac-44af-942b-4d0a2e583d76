<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属平台" prop="platId">
        <el-select :default-first-option="false" v-model="queryParams.platId" placeholder="所属平台">
          <el-option
            v-for="item in this.platList"
            :key="item.platId"
            :label="item.platName"
            :value="item.platId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="接口名称" prop="interfaceName">
        <el-input
          maxlength="50"
          v-model="queryParams.interfaceName"
          placeholder="请输入接口名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接口路径" prop="interfacePath">
        <el-input
          maxlength="50"
          v-model="queryParams.interfacePath"
          placeholder="请输入接口路径"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-if="keyWords.includes('add')"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="interfaceList" >
      <el-table-column label="接口名称" align="left" prop="interfaceName" />
      <el-table-column label="接口路径" align="left" prop="interfacePath" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-if="keyWords.includes('edit')"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-if="keyWords.includes('remove')"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改接口对话框  -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="所属平台" prop="platId">
              <el-select v-model="form.platId" placeholder="所属平台" style="width: 340px;">
                <el-option
                  v-for="item in this.platList"
                  :key="item.platId"
                  :label="item.platName"
                  :value="item.platId"
                />
              </el-select>
            </el-form-item>
        <el-form-item label="接口名称" prop="interfaceName">
          <el-input v-model="form.interfaceName" placeholder="请输入接口名称" />
        </el-form-item>
        <el-form-item prop="interfacePath">
          <el-input v-model="form.interfacePath" placeholder="请输入接口路径" />
            <span slot="label">
              <el-tooltip content="如/system/user/list" placement="top">
              <i class="el-icon-question">接口路径</i>
              </el-tooltip>
            </span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPlat} from "@/api/system/plat";
import { listInterface, getInterface, delInterface, addInterface, updateInterface } from "@/api/system/interface";

export default {
  name: "Interface",
  data() {
    return {
      keyWords : [],
      platList:[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 接口表格数据
      interfaceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platId: null,
        interfaceName: null,
        interfacePath: null,
      },
      queryParamsDialog: {
        platId : undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        platId: [
          { required: true, message: "所属平台不能为空", trigger: "blur" }
        ],
        interfaceName: [
          { required: true, message: "接口名称不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '接口名称长度必须介于 2 和 20 之间', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/,
            message: "仅支持中文、英文、数字及下划线",
            trigger: "blur"
          }
        ],
        interfacePath: [
          { required: true, message: "接口路径不能为空", trigger: "blur" },
          { min: 5, max: 100, message: '接口路径长度必须介于 5 和 100 之间', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9\/]{5,100}$/,
            message: "仅支持英文、数字及/",
            trigger: "blur"
          }
        ],
      }
    };
  },
  created() {
    this.getButton();
    this.getPlatList();
  },
  methods: {
    getButton(){
      this.keyWords = this.$route.meta.keyWords
    },
    getPlatList(){
      listPlat(this.queryParams).then(response => {
        this.platList = response.rows;
        if(response.total!=0){
          this.queryParams.platId = this.platList[0].platId
          this.queryParams.pageNum = 1;
          this.getList();
        }else{
          this.queryParams.platId = undefined
          this.queryParams.pageNum = 1;
          this.getList();
        }
      }); 
    },
    /** 查询接口列表 */
    getList() {
      this.loading = true;
      listInterface(this.queryParams).then(response => {
        this.interfaceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        interfaceId: null,
        platId: null,
        interfaceName: null,
        interfacePath: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.pageNum = null;
      this.getPlatList();
      // this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.platId = this.queryParams.platId
      this.open = true;
      this.title = "添加接口";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const interfaceId = row.interfaceId || this.ids
      getInterface(interfaceId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改接口";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.interfaceId != null) {
            updateInterface(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInterface(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const interfaceIds = row.interfaceId || this.ids;
      this.$modal.confirm('是否确认删除接口？').then(function() {
        return delInterface(interfaceIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/interface/export', {
        ...this.queryParams
      }, `interface_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
