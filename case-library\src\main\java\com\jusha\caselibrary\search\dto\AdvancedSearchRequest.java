package com.jusha.caselibrary.search.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName AdvancedSearchRequest
 * @Description 高级检索请求DTO
 * <AUTHOR>
 * @Date 2025/7/7 15:56
 **/
@Data
public class AdvancedSearchRequest {

    /**
     * 检索类型：department（科室病例库）、personal（个人病例库）
     */
    private String searchType = "department";

    /**
     * 用户ID（个人病例库检索时必填）
     */
    private Long userId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 影像号
     */
    private String accessNumber;

    /**
     * 住院号
     */
    private String inPatientNo;

    /**
     * 门诊号
     */
    private String outPatientNo;

    /**
     * 检查项目
     */
    private String studyItemName;

    /**
     * 检查部位
     */
    private String partName;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 标签ID列表
     */
    private List<Long> tagIds;

    /**
     * 标签名称列表
     */
    private List<String> tagNames;

    /**
     * 疾病ID列表（科室病例库）
     */
    private List<Long> diseaseIds;

    /**
     * 疾病名称列表（科室病例库）
     */
    private List<String> diseaseNames;

    /**
     * 分类ID列表（科室病例库）
     */
    private List<Long> categoryIds;

    /**
     * 分类名称列表（科室病例库）
     */
    private List<String> categoryNames;

    /**
     * 目录ID列表（个人病例库）
     */
    private List<Long> catalogIds;

    /**
     * 目录名称列表（个人病例库）
     */
    private List<String> catalogNames;

    /**
     * 检查日期范围 - 开始日期
     */
    private LocalDateTime studyTimeStart;

    /**
     * 检查日期范围 - 结束日期
     */
    private LocalDateTime studyTimeEnd;

    /**
     * 创建日期范围 - 开始日期
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建日期范围 - 结束日期
     */
    private LocalDateTime createTimeEnd;

    /**
     * 更新日期范围 - 开始日期
     */
    private LocalDateTime updateTimeStart;

    /**
     * 更新日期范围 - 结束日期
     */
    private LocalDateTime updateTimeEnd;

    /**
     * 诊断信息
     */
    private String diagnosis;

    /**
     * 临床诊断
     */
    private String clinicalDiagnosis;

    /**
     * 影像诊断
     */
    private String reportDiagnose;

    /**
     * 影像描述
     */
    private String reportDescribe;

    /**
     * 病史信息
     */
    private String medicalHistory;

    /**
     * 体征信息
     */
    private String physicalSign;

    /**
     * 自述症状
     */
    private String selfReportedSymptom;

    /**
     * 病例分析（个人病例库）
     */
    private String caseAnalysis;

    /**
     * 病例名称（个人病例库）
     */
    private String caseName;

    /**
     * 病例编号（个人病例库）
     */
    private String caseNo;

    /**
     * 难度等级（个人病例库）
     */
    private String difficulty;

    /**
     * 患者性别
     */
    private String patientSex;

    /**
     * 患者年龄范围 - 最小年龄
     */
    private Integer patientAgeMin;

    /**
     * 患者年龄范围 - 最大年龄
     */
    private Integer patientAgeMax;

    /**
     * 是否阳性
     */
    private String isPositive;

    /**
     * 检查状态
     */
    private String studyState;

    /**
     * 报告医生
     */
    private String reporter;

    /**
     * 审核医生
     */
    private String checker;

    /**
     * 申请科室
     */
    private String applyDepartment;

    /**
     * 申请医生
     */
    private String applyDoctor;

    /**
     * 序列描述
     */
    private String seriesDescription;

    /**
     * 成像方式
     */
    private String modality;

    /**
     * 是否关键帧
     */
    private Boolean isKeyframe;

    /**
     * 关键字搜索（全文检索）
     */
    private String keyword;

    /**
     * 高亮显示
     */
    private Boolean highlight = true;

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向：asc、desc
     */
    private String sortDirection = "desc";

    /**
     * 页码（从1开始）
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 20;

    /**
     * 是否包含总数统计
     */
    private Boolean includeCount = true;

    /**
     * 聚合统计字段
     */
    private List<String> aggregationFields;

    /**
     * 最小匹配度（0-1之间）
     */
    private Float minScore;

    /**
     * 搜索超时时间（毫秒）
     */
    private Long timeoutMs = 30000L;

    // ==================== 新增随访相关搜索参数 ====================
    
    /**
     * 随访类型
     */
    private String followType;
    
    /**
     * 随访结果
     */
    private String followupResult;
    
    /**
     * 随访时间范围 - 开始时间
     */
    private LocalDateTime followTimeStart;
    
    /**
     * 随访时间范围 - 结束时间
     */
    private LocalDateTime followTimeEnd;

    // ==================== 新增疾病概述相关搜索参数 ====================
    
    /**
     * 疾病概述
     */
    private String diseaseOverview;
    
    /**
     * 病理表现
     */
    private String pathology;
    
    /**
     * 临床表现
     */
    private String clinical;
    
    /**
     * 影像学表现
     */
    private String imaging;
    
    /**
     * 诊断依据
     */
    private String diagnosisBasis;
    
    /**
     * 鉴别诊断
     */
    private String differential;
    
    /**
     * 关键帧
     */
    private String keyframe;

    // ==================== 新增性能优化相关参数 ====================
    
    /**
     * 是否启用缓存
     */
    private Boolean enableCache = true;
    
    /**
     * 缓存过期时间（秒）
     */
    private Integer cacheExpireSeconds = 300;
    
    /**
     * 是否使用search_after分页（避免深度分页问题）
     */
    private Boolean useSearchAfter = false;
    
    /**
     * search_after排序值（用于深度分页优化）
     */
    private Object[] searchAfter;
    
    /**
     * 查询模板ID（预编译查询优化）
     */
    private String queryTemplateId;
    
    /**
     * 是否启用智能查询优化
     */
    private Boolean enableSmartOptimization = true;

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        // 基本验证
        if (pageNum == null || pageNum < 1) {
            return false;
        }
        if (pageSize == null || pageSize < 1 || pageSize > 1000) {
            return false;
        }

        // 个人病例库检索时必须提供用户ID
        if ("personal".equals(searchType) && userId == null) {
            return false;
        }

        // 日期范围验证
        if (studyTimeStart != null && studyTimeEnd != null && studyTimeStart.isAfter(studyTimeEnd)) {
            return false;
        }
        if (createTimeStart != null && createTimeEnd != null && createTimeStart.isAfter(createTimeEnd)) {
            return false;
        }
        if (updateTimeStart != null && updateTimeEnd != null && updateTimeStart.isAfter(updateTimeEnd)) {
            return false;
        }
        if (followTimeStart != null && followTimeEnd != null && followTimeStart.isAfter(followTimeEnd)) {
            return false;
        }

        // 年龄范围验证
        return patientAgeMin == null || patientAgeMax == null || patientAgeMin <= patientAgeMax;
    }

    /**
     * 是否为空搜索（没有任何搜索条件）
     */
    public boolean isEmpty() {
        return keyword == null && patientName == null && accessNumber == null &&
                inPatientNo == null && outPatientNo == null && studyItemName == null &&
                partName == null && deviceType == null && deviceName == null &&
                (tagIds == null || tagIds.isEmpty()) && (tagNames == null || tagNames.isEmpty()) &&
                (diseaseIds == null || diseaseIds.isEmpty()) && (diseaseNames == null || diseaseNames.isEmpty()) &&
                (categoryIds == null || categoryIds.isEmpty()) && (categoryNames == null || categoryNames.isEmpty()) &&
                (catalogIds == null || catalogIds.isEmpty()) && (catalogNames == null || catalogNames.isEmpty()) &&
                studyTimeStart == null && studyTimeEnd == null &&
                createTimeStart == null && createTimeEnd == null &&
                updateTimeStart == null && updateTimeEnd == null &&
                followTimeStart == null && followTimeEnd == null &&
                diagnosis == null && clinicalDiagnosis == null && reportDiagnose == null &&
                reportDescribe == null && medicalHistory == null && physicalSign == null &&
                selfReportedSymptom == null && caseAnalysis == null && caseName == null &&
                caseNo == null && difficulty == null && patientSex == null &&
                patientAgeMin == null && patientAgeMax == null && isPositive == null &&
                studyState == null && reporter == null && checker == null &&
                applyDepartment == null && applyDoctor == null && seriesDescription == null &&
                modality == null && isKeyframe == null &&
                // 新增随访相关字段检查
                followType == null && followupResult == null &&
                // 新增疾病概述相关字段检查
                diseaseOverview == null && pathology == null && clinical == null &&
                imaging == null && diagnosisBasis == null && differential == null &&
                keyframe == null;
    }
    
    /**
     * 获取缓存键
     */
    public String getCacheKey() {
        if (enableCache == null || !enableCache) {
            return null;
        }
        
        StringBuilder keyBuilder = new StringBuilder("advanced_search:");
        keyBuilder.append("type:").append(searchType);
        if (userId != null) {
            keyBuilder.append(":user:").append(userId);
        }
        
        // 添加主要搜索条件到缓存键
        if (keyword != null) {
            keyBuilder.append(":keyword:").append(keyword.hashCode());
        }
        if (patientName != null) {
            keyBuilder.append(":patient:").append(patientName.hashCode());
        }
        if (diagnosis != null) {
            keyBuilder.append(":diagnosis:").append(diagnosis.hashCode());
        }
        
        keyBuilder.append(":page:").append(pageNum).append(":size:").append(pageSize);
        keyBuilder.append(":sort:").append(sortField).append(":").append(sortDirection);
        
        return keyBuilder.toString();
    }
    
    /**
     * 判断是否为复杂查询（需要特殊优化）
     */
    public boolean isComplexQuery() {
        int conditionCount = 0;
        
        // 统计搜索条件数量
        if (keyword != null) conditionCount++;
        if (patientName != null) conditionCount++;
        if (diagnosis != null) conditionCount++;
        if (clinicalDiagnosis != null) conditionCount++;
        if (reportDiagnose != null) conditionCount++;
        if (reportDescribe != null) conditionCount++;
        if (medicalHistory != null) conditionCount++;
        if (followupResult != null) conditionCount++;
        if (pathology != null) conditionCount++;
        if (clinical != null) conditionCount++;
        if (imaging != null) conditionCount++;
        
        if (tagIds != null && !tagIds.isEmpty()) conditionCount++;
        if (diseaseIds != null && !diseaseIds.isEmpty()) conditionCount++;
        if (categoryIds != null && !categoryIds.isEmpty()) conditionCount++;
        
        // 日期范围查询
        if (studyTimeStart != null || studyTimeEnd != null) conditionCount++;
        if (createTimeStart != null || createTimeEnd != null) conditionCount++;
        if (followTimeStart != null || followTimeEnd != null) conditionCount++;
        
        // 复杂查询的判断标准：条件数量 >= 5 或者包含全文搜索 + 其他条件
        return conditionCount >= 5 || (keyword != null && conditionCount >= 3);
    }

    /**
     * 判断是否需要深度分页优化
     */
    public boolean needsDeepPaginationOptimization() {
        return pageNum != null && pageSize != null && (pageNum - 1) * pageSize > 10000;
    }
}
