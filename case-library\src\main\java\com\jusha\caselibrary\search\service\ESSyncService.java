package com.jusha.caselibrary.search.service;


import com.jusha.caselibrary.search.dto.SyncMessage;

import java.util.List;

/**
 * ES数据同步服务接口
 * 
 * <AUTHOR>
 * @date 2025/07/07
 */
public interface ESSyncService {

    /**
     * 同步科室病例数据到ES
     * 
     * @param caseIdList 病例IDs
     * @param operation 操作类型（CREATE, UPDATE, DELETE）
     */
    void syncDepartmentCase(List<Long> caseIdList, String operation);

    /**
     * 同步个人病例数据到ES
     * 
     * @param caseIdList 病例IDs
     * @param operation 操作类型（CREATE, UPDATE, DELETE）
     */
    void syncPersonalCase(List<Long> caseIdList, String operation);

    /**
     * 处理同步消息
     * 
     * @param message 同步消息
     */
    void processSyncMessage(SyncMessage message);

    /**
     * 全量同步科室病例数据
     */
    void fullSyncDepartmentCases();

    /**
     * 全量同步个人病例数据
     */
    void fullSyncPersonalCases(Long userId);

    /**
     * 检查ES索引是否存在，不存在则创建
     */
    void ensureIndexesExistOrCreate();

    /**
     * 删除并重建ES索引
     */
    void recreateIndexes();
}