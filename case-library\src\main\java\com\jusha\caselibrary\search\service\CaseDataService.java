package com.jusha.caselibrary.search.service;

import com.jusha.caselibrary.mybatisplus.entity.DepCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.search.document.DepartmentCaseDocument;
import com.jusha.caselibrary.search.document.PersonalCaseDocument;

import java.util.List;

/**
 * 病例数据服务接口
 * 
 * <AUTHOR>
 * @date 2025/07/07
 */
public interface CaseDataService {

    /**
     * 根据ID获取科室病例
     */
    DepCase getDepartmentCaseById(Long caseId);

    /**
     * 根据ID获取个人病例
     */
    UserCase getPersonalCaseById(Long caseId);

    /**
     * 批量获取科室病例
     */
    List<DepCase> getDepartmentCasesByIds(List<Long> caseIds);

    /**
     * 批量获取个人病例
     */
    List<UserCase> getPersonalCasesByIds(List<Long> caseIds);

    /**
     * 获取所有科室病例（用于全量同步）
     */
    List<DepCase> getAllDepartmentCases();

    /**
     * 获取所有个人病例（用于全量同步）
     */
    List<UserCase> getAllPersonalCases();

    /**
     * 根据用户ID获取个人病例
     */
    List<UserCase> getPersonalCasesByUserId(Long userId);

    /**
     * 获取所有科室病例ID列表
     */
    List<Long> getAllDepartmentCaseIds();

    /**
     * 获取所有个人病例ID列表
     */
    List<Long> getAllPersonalCaseIds();

    /**
     * 构建科室病例ES文档
     */
    DepartmentCaseDocument buildDepartmentCaseDocument(Long caseId);

    /**
     * 构建个人病例ES文档
     */
    PersonalCaseDocument buildPersonalCaseDocument(Long caseId);

    /**
     * 批量构建科室病例ES文档
     */
    List<DepartmentCaseDocument> buildSyncDepartmentCasesByIds(List<Long> caseIdList);

    /**
     * 批量构建个人病例ES文档
     */
    List<DepartmentCaseDocument> buildSyncPersonalCasesByIds(List<Long> caseIdList, Long userId);
}