package com.jusha.auth.common.core.service;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.exception.HttpStatus;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.ServletUtils;
import com.jusha.auth.common.utils.ip.AddressUtils;
import com.jusha.auth.common.utils.ip.IpUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.utils.uuid.IdUtils;
import eu.bitwalker.useragentutils.UserAgent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {
    private static final Logger log = LoggerFactory.getLogger(TokenService.class);

    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    // 令牌有效期（默认30分钟）
    @Value("${token.expireTime}")
    private int expireTime;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser() {
        // 获取请求携带的令牌
        String token = getToken();
        if (StringUtils.isNotEmpty(token)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                HashMap loginMap = redisCache.getCacheObject(token);
                LoginUser loginUser = JSONObject.parseObject(objectMapper.writeValueAsString(loginMap), LoginUser.class);
                if(loginUser == null){
                    log.error("获取用户信息为空");
                    throw new ServiceException(MessageUtils.message("user.login.again.error"), HttpStatus.UNAUTHORIZED);
                }
                return loginUser;
            } catch (Exception e) {
                log.error("获取用户信息异常'{}'", e.getMessage());
                throw new ServiceException(MessageUtils.message("user.login.again.error"), HttpStatus.UNAUTHORIZED);
            }
        }
        return null;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUserCanNull() {
        // 获取请求携带的令牌
        String token = getToken();
        if (StringUtils.isNotEmpty(token)) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                HashMap loginMap = redisCache.getCacheObject(token);
                LoginUser loginUser = JSONObject.parseObject(objectMapper.writeValueAsString(loginMap), LoginUser.class);
                if(loginUser == null){
                    log.error("获取用户信息为空");
                }
                return loginUser;
            } catch (Exception e) {
                log.error("获取用户信息异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 获取当前用户ID
     **/
    public Long getUserId() {
        Long userId = null;
        try {
            userId = getLoginUser().getUserId();
        } catch (Exception e) {
            log.error(MessageUtils.message("get.userId.exception"));
            throw new ServiceException(MessageUtils.message("get.userId.exception"), HttpStatus.UNAUTHORIZED);
        }
        return userId;
    }

    /**
     * 获取当前用户ID（可以为空）
     **/
    public Long getUserIdCanNull() {
        Long userId = null;
        try {
            userId = getLoginUser().getUserId();
        } catch (Exception e) {
            log.info(MessageUtils.message("get.userId.exception"));
        }
        return userId;
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            redisCache.deleteObject(token);
        }
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createToken(LoginUser loginUser) {
        String token = Constants.LOGIN_TOKEN_KEY + loginUser.getUserId() + ":" + IdUtils.fastUUID();
        loginUser.setToken(token);
        setUserAgent(loginUser);
        refreshToken(loginUser);
        return token;
    }



    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + expireTime * MILLIS_MINUTE);
        redisCache.setCacheObject(loginUser.getToken(), JSONObject.parseObject(JSONObject.toJSONString(loginUser)), expireTime, TimeUnit.MINUTES);

    }

    /**
     * 设置用户代理信息
     *
     * @param loginUser 登录信息
     */
    public void setUserAgent(LoginUser loginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = IpUtils.getIpAddr();
        loginUser.setIpaddr(ip);
        loginUser.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
        loginUser.setBrowser(userAgent.getBrowser().getName());
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    /**
     * 获取请求token
     *
     * @return token
     */
    private String getToken() {
        HttpServletRequest request = ServletUtils.getRequest();
        return request.getHeader(header);
    }

}
