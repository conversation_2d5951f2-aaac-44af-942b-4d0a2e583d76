package com.jusha.ris.docking.mock.utils;

import java.util.Random;

/**
 * <AUTHOR>
 * @title PhoneNumberUtil
 * @description
 * @date 2025/2/24
 */
public class PhoneNumberUtil {

    private static final String[] PREFIXES = {"3", "4", "5", "6", "7", "8", "9"};
    private static final Random random = new Random();

    public static String generatePhoneNumber() {
        // 以 1 开头
        StringBuilder phoneNumber = new StringBuilder("1");

        // 第二位是随机选择的 3、4、5、6、7、8、9
        phoneNumber.append(PREFIXES[random.nextInt(PREFIXES.length)]);

        // 后面 9 位是随机生成的数字
        for (int i = 0; i < 9; i++) {
            phoneNumber.append(random.nextInt(10));
        }

        return phoneNumber.toString();
    }

}
