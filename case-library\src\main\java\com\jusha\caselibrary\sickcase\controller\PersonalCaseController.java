package com.jusha.caselibrary.sickcase.controller;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.aop.EscapeWildcard;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.search.service.CaseManagementService;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseCreateReq;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseUpdateReq;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName PersonalCaseController
 * @Description  个人病例库控制层
 * <AUTHOR>
 * @Date 2025/7/10 09:29
 **/
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/personal/case")
@Api(tags = "病例管理-个人病例库管理")
public class PersonalCaseController {

    private final PersonalCaseService personalCaseService;

    @Value("${TMP-LOCATIONS}")
    private String tmpPath;

    @EscapeWildcard
    @ApiOperation("个人病例库详细列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResultBean<List<PersonalCaseDetailResp>> list(@RequestBody PersonalCaseSearchReq req) {
        List<PersonalCaseDetailResp> resp = personalCaseService.getPersonalCaseDetailList(req);
        return ResultBean.success(resp);
    }

    @EscapeWildcard
    @ApiOperation("个人病例库详细列表分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResultBean<PageInfo<PersonalCaseDetailResp>> page(@RequestBody PersonalCaseSearchReq req) {
        PageInfo<PersonalCaseDetailResp> resp = personalCaseService.getPersonalCaseDetailPage(req);
        return ResultBean.success(resp);
    }

    @ApiOperation("个人病例库病例详情查询")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public ResultBean<PersonalCaseDetailResp> detail(@RequestParam("userCaseId") Long userCaseId) {
        PersonalCaseDetailResp resp = personalCaseService.getPersonalCaseDetail(userCaseId);
        return ResultBean.success(resp);
    }

    @ApiOperation("在指定目录下创建个人病例（基本信息）")
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = Constant.ES_USER_INDEX_ID)
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultBean<Long> createPersonalCaseInCatalog(@RequestBody PersonalCaseCreateReq req) {
        long userCaseId = personalCaseService.createPersonalCaseInCatalog(req);
        return ResultBean.success(userCaseId);
    }

    @ApiOperation("在指定目录下修改个人病例（基本信息）")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultBean<Void> update(@RequestBody PersonalCaseUpdateReq req) {
        personalCaseService.updatePersonalCase(req);
        return ResultBean.success();
    }

    @ApiOperation("个人病例库病例删除")
    @ESSync(type = ESSync.SyncType.DELETE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = Constant.ES_USER_INDEX_ID)
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultBean<Void> delete(@RequestParam("userCaseId") Long userCaseId, @RequestParam("catalogId") Long catalogId) {
        personalCaseService.deletePersonalCaseById(userCaseId, catalogId);
        return ResultBean.success();
    }

}
