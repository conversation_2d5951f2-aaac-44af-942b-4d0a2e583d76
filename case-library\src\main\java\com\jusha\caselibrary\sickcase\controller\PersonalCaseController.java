package com.jusha.caselibrary.sickcase.controller;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.common.aop.EscapeWildcard;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.search.service.CaseManagementService;
import com.jusha.caselibrary.sickcase.dto.req.CatalogOperationRequest;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName PersonalCaseController
 * @Description  个人病例库控制层
 * <AUTHOR>
 * @Date 2025/7/10 09:29
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/personal/case")
@Api(tags = "病例管理-个人病例库管理")
public class PersonalCaseController {

    private final PersonalCaseService personalCaseService;
    private final CaseManagementService caseManagementService;

    @Value("${TMP-LOCATIONS}")
    private String tmpPath;

    @EscapeWildcard
    @ApiOperation("个人病例库详细列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResultBean<List<PersonalCaseDetailResp>> list(@RequestBody PersonalCaseSearchReq req) {
        List<PersonalCaseDetailResp> resp = personalCaseService.getPersonalCaseDetailList(req);
        return ResultBean.success(resp);
    }

    @EscapeWildcard
    @ApiOperation("个人病例库详细列表分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResultBean<PageInfo<PersonalCaseDetailResp>> page(@RequestBody PersonalCaseSearchReq req) {
        PageInfo<PersonalCaseDetailResp> resp = personalCaseService.getPersonalCaseDetailPage(req);
        return ResultBean.success(resp);
    }

    @ApiOperation("个人病例库病例详情查询")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public ResultBean<PersonalCaseDetailResp> detail(@RequestParam("caseId") Long caseId) {
        PersonalCaseDetailResp resp = personalCaseService.getPersonalCaseDetail(caseId);
        return ResultBean.success(resp);
    }

    @ApiOperation("在指定目录下创建个人病例")
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    @RequestMapping(value = "/create-in-catalog", method = RequestMethod.POST)
    public ResultBean<Boolean> createPersonalCaseInCatalog(@RequestBody @Valid CatalogOperationRequest request) {
        try {
            log.info("在目录下创建个人病例，病例ID: {}, 目录ID: {}", request.getCaseId(), request.getCatalogId());
            Boolean result = caseManagementService.createPersonalCaseInCatalog(request.getCaseId(), request.getCatalogId());
            log.info("在目录下创建个人病例操作完成，结果: {}", result);
            return ResultBean.success(result);
        } catch (Exception e) {
            log.error("在目录下创建个人病例失败，病例ID: {}, 目录ID: {}", request.getCaseId(), request.getCatalogId(), e);
            return ResultBean.error("在目录下创建个人病例失败: " + e.getMessage());
        }
    }

    @ApiOperation("从指定目录删除个人病例")
    @ESSync(type = ESSync.SyncType.DELETE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    @RequestMapping(value = "/delete-from-catalog", method = RequestMethod.DELETE)
    public ResultBean<Boolean> deletePersonalCaseFromCatalog(@RequestBody @Valid CatalogOperationRequest request) {
        try {
            log.info("从目录中删除个人病例，病例ID: {}, 目录ID: {}", request.getCaseId(), request.getCatalogId());
            Boolean result = caseManagementService.deletePersonalCaseFromCatalog(request.getCaseId(), request.getCatalogId());
            log.info("从目录中删除个人病例操作完成，结果: {}", result);
            return ResultBean.success(result);
        } catch (Exception e) {
            log.error("从目录中删除个人病例失败，病例ID: {}, 目录ID: {}", request.getCaseId(), request.getCatalogId(), e);
            return ResultBean.error("从目录中删除个人病例失败: " + e.getMessage());
        }
    }

    @ApiOperation("个人病例库病例删除（兼容旧接口）")
    @ESSync(type = ESSync.SyncType.DELETE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultBean<Void> delete(@RequestParam("caseId") Long caseId, @RequestParam("catalogId") Long catalogId) {
        personalCaseService.deletePersonalCaseById(caseId, catalogId);
        return ResultBean.success();
    }

}
