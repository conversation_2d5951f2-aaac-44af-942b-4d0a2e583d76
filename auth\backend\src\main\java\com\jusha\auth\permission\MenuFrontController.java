package com.jusha.auth.permission;

import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.mybatisplus.entity.SysMenu;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.system.domain.RouterVo;
import com.jusha.auth.system.service.ISysMenuService;
import com.jusha.auth.system.service.ISysPlatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "菜单路由管理")
@RestController
@RequestMapping("/menu")
public class MenuFrontController extends BaseController {

    @Autowired
    private ISysMenuService iSysmenuService;

    @Autowired
    private ISysPlatService iSysPlatService;

    @Autowired
    private RedisCache redisCache;

    @Value("${token.expireTime}")
    private int expireTime;


    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @ApiOperation("路由信息")
    @GetMapping("/getMenuRouters")
    public ResultBean getMenuRouters() {
        List<RouterVo> routerVos;
        String platId = getPlatId();
        long userId = getLoginUser().getUserId();
        if(redisCache.getCacheObject(Constants.MENU_ROUTER + userId + ":" + platId) == null){
            List<SysMenu> menus = iSysmenuService.selectMenuTreeByUserId(userId, Long.parseLong(platId));
            routerVos = iSysmenuService.buildMenus(menus,userId);
            redisCache.setCacheObject(Constants.MENU_ROUTER + userId + ":" + platId, routerVos , expireTime, TimeUnit.MINUTES);
        }else {
            routerVos = redisCache.getCacheObject(Constants.MENU_ROUTER + userId + ":" + platId);
            if(routerVos.isEmpty()){
                List<SysMenu> menus = iSysmenuService.selectMenuTreeByUserId(userId, Long.parseLong(platId));
                routerVos = iSysmenuService.buildMenus(menus,userId);
            }
            redisCache.setCacheObject(Constants.MENU_ROUTER + userId + ":" + platId, routerVos , expireTime, TimeUnit.MINUTES);
        }
        return ResultBean.success(routerVos);
    }

    /**
     * 加载对应角色菜单列表树
     */
    @ApiOperation("对应角色菜单列表树")
    @GetMapping(value = "/menuTreeselect")
    public ResultBean roleMenuTreeselect(@ApiParam(required = true, name = "角色ID") @RequestParam Long roleId) {
        List<SysMenu> menus = iSysmenuService.selectAllMenuList(roleId);
        HashMap map = new HashMap();
        map.put("checkedKeys", iSysmenuService.selectMenuListByRoleId(roleId));
        map.put("menus", iSysmenuService.buildMenuTreeSelect(menus));
        return ResultBean.success(map);
    }

    @ApiOperation("获取所有接口权限列表")
    @GetMapping("/getPermission")
    public ResultBean getPermission() {
        String platId = getPlatId();
        SysUser user = getLoginUser().getSysUser();
        Set<String> interfacePaths = iSysmenuService.selectIterfacePathsByUserId(user, Long.parseLong(platId));
        return ResultBean.success(interfacePaths);
    }
}
