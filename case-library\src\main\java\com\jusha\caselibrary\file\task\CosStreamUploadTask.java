package com.jusha.caselibrary.file.task;

import com.alibaba.fastjson.JSON;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.FileUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.file.service.impl.FileCOSServiceImpl;
import com.jusha.caselibrary.mybatisplus.entity.Resource;
import com.jusha.caselibrary.mybatisplus.service.ResourceService;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Date;

/**
 * 腾讯云大文件流式上传任务
 */
@Slf4j
@RequiredArgsConstructor
public class CosStreamUploadTask implements Runnable {

    /**
     * 任务ID（异步流式上传完后，回调用）
     */
    private final Long taskId;

    /**
     * 文件名(包含后缀，必须在临时文件目录下)
     */
    private final String fileName;


    @Override
    public void run(){
        ResourceService resourceService = ContextHolder.getBean(ResourceService.class);
        FileCOSServiceImpl fileService = ContextHolder.getBean(FileCOSServiceImpl.class);

        String fileFullPath = StringUtils.join(ContextHolder.propertiesBean().getTmpLocation(), File.separator, fileName);

        //实际存储文件名称
        String tempName = StringUtils.join(FilenameUtils.getBaseName(fileName), "_", DateUtil.convertDateToStr(new Date(), "yyyyMMddHHmmss"));
        String actualName = StringUtils.join(DigestUtils.md5Hex(tempName), ".", FilenameUtils.getExtension(fileName));
        String key = StringUtils.join("/", DateUtil.convertDateToStr(new Date(), "yyyyMM"), "/", DateUtil.convertDateToStr(new Date(), "dd"), "/", actualName);

        try {
            //本地文件流
            InputStream inputStream = new FileInputStream(fileFullPath);
            //创建上传请求
            PutObjectRequest putObjectRequest = new PutObjectRequest(ContextHolder.propertiesBean().getBucketName(), key, inputStream, null);
            //上传文件
            PutObjectResult putObjectResult = fileService.getCosClient().putObject(putObjectRequest);
            log.info("CosStreamUploadTask upload result:{}", JSON.toJSONString(putObjectResult));
        } catch (Exception e) {
            log.error("CosStreamUploadTask error:{}", e);
        }

        //入库
        Resource resource = new Resource();
        resource.setResourceId(YitIdHelper.nextId());
        resource.setActualName(key);
        resource.setOriginName(fileName);
        String fileUrl = StringUtils.join(ContextHolder.propertiesBean().getEndpoint(), key);
        resource.setFileUrl(fileUrl);
        resource.setLmGpId(LoginUtil.isLogin()?LoginUtil.lmGroupId():null);
        resourceService.save(resource);
        //删除：导出任务压缩包
        FileUtil.delFile(new File(fileFullPath));
        //回调,通知caselib服务上传已完成
    }

}