package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Getter
@Setter
@TableName("sys_user")
@ApiModel(value = "SysUser对象", description = "用户信息表")
public class SysUser {

    @ApiModelProperty(value = "用户ID")
    @TableId(value = "user_id")
    private Long userId;

    @ApiModelProperty(value = "用户账号")
    @TableField("user_name")
    @Size(message = "用户账号长度不合法",max = 20)
    private String userName;

    @ApiModelProperty(value = "用户姓名")
    @TableField("nick_name")
    @Size(message = "用户姓名长度不合法",max = 20)
    private String nickName;

    @ApiModelProperty(value = "手机号码")
    @TableField("phone_number")
    @Size(message = "手机号码长度不合法",max = 11)
    private String phoneNumber;

    @ApiModelProperty(value = "工号")
    @TableField("work_number")
    @Size(message = "工号长度不合法",max = 20)
    private String workNumber;

    @ApiModelProperty(value = "密码")
    @TableField("password")
    private String password;

    @ApiModelProperty(value = "帐号状态（0正常 1停用）")
    @TableField("status")
    private String status;

    @ApiModelProperty(value = "删除标志（0代表存在 1代表删除）")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;

    @ApiModelProperty(value = "最后登录IP")
    @TableField("login_ip")
    private String loginIp;

    @ApiModelProperty(value = "最后登录时间")
    @TableField("login_date")
    private Date loginDate;

    @ApiModelProperty(value = "创建者")
    @TableField("create_by")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField("update_by")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    /** 角色对象 */
    @TableField(exist = false)
    private List<SysRole> roles;

    /** 角色组 */
    @TableField(exist = false)
    private Long[] roleIds;

    /** 角色ID */
    @TableField(exist = false)
    private Long roleId;

    public SysUser() {
    }

    public SysUser(Long userId) {
        this.userId = userId;
    }

    @JsonIgnore
    public boolean isAdmin() {
        return isAdmin(this.userId);
    }

    @JsonIgnore
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

//    @JsonIgnore  这个注释不能加，加上了会导致用户管理分配角色时，这个值拿不到实际的值，一直为0。当初为什么加这个想不起来了，下次遇到再说
    @TableField(exist = false)
    private long platId;

}
