package com.jusha.caselibrary.search.service.impl;

import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.mybatisplus.entity.DepCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCaseCatalog;
import com.jusha.caselibrary.mybatisplus.mapper.DepCaseMapper;
import com.jusha.caselibrary.mybatisplus.mapper.UserCaseMapper;
import com.jusha.caselibrary.mybatisplus.service.UserCaseCatalogService;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.search.service.CaseManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @ClassName CaseManagementServiceImpl
 * @Description 病例管理服务实现类 @ESSync注解的使用，通过AOP切面自动处理ES同步
 * <AUTHOR>
 * @Date 2025/7/7 17:04
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseManagementServiceImpl implements CaseManagementService {

    private final DepCaseMapper depCaseMapper;

    private final UserCaseMapper userCaseMapper;

    private final UserCaseCatalogService userCaseCatalogService;

    /**
     * 创建科室病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
//    @ESSync(type = ESSync.SyncType.CREATE, indexType  = Constant.DEP_CASE_INDEX_NAME)
    public Long createDepartmentCase(DepCase depCase) {
        try {
            // 设置创建时间
            long caseId = YitIdHelper.nextId();
            depCase.setCaseId(caseId);
            depCase.setCreateTime(new Date());
            depCase.setUpdateTime(new Date());
            depCase.setDelFlag("0");

            int result = depCaseMapper.insert(depCase);
            log.info("创建科室病例成功，ID: {}", depCase.getCaseId());
            if (result > 0) {
                return depCase.getCaseId();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("创建科室病例失败", e);
            return null;
        }
    }

    /**
     * 更新科室病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.DEP_CASE_INDEX_NAME)
    public Long updateDepartmentCase(DepCase depCase) {
        try {
            // 设置更新时间
            depCase.setUpdateTime(new Date());

            int result = depCaseMapper.updateById(depCase);
            log.info("更新科室病例成功，ID: {}", depCase.getCaseId());
            if (result > 0) {
                return depCase.getCaseId();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("更新科室病例失败，ID: {}", depCase.getCaseId(), e);
            return null;
        }
    }

    /**
     * 删除科室病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.DELETE, indexType = Constant.DEP_CASE_INDEX_NAME)
    public boolean deleteDepartmentCase(Long caseId) {
        try {
            int result = depCaseMapper.deleteById(caseId);
            log.info("删除科室病例成功，ID: {}", caseId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除科室病例失败，ID: {}", caseId, e);
            return false;
        }
    }

    /**
     * 创建个人病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    public boolean createPersonalCase(UserCase userCase) {
        try {
            // 设置创建时间
            userCase.setCreateTime(new Date());
            userCase.setUpdateTime(new Date());
            userCase.setDelFlag("0");

            int result = userCaseMapper.insert(userCase);
            log.info("创建个人病例成功，ID: {}", userCase.getCaseId());
            return result > 0;
        } catch (Exception e) {
            log.error("创建个人病例失败", e);
            return false;
        }
    }

    /**
     * 更新个人病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    public boolean updatePersonalCase(UserCase userCase) {
        try {
            // 设置更新时间
            userCase.setUpdateTime(new Date());

            int result = userCaseMapper.updateById(userCase);
            log.info("更新个人病例成功，ID: {}", userCase.getCaseId());
            return result > 0;
        } catch (Exception e) {
            log.error("更新个人病例失败，ID: {}", userCase.getCaseId(), e);
            return false;
        }
    }

    /**
     * 删除个人病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.DELETE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    public boolean deletePersonalCase(Long caseId) {
        try {
            int result = userCaseMapper.deleteById(caseId);
            log.info("删除个人病例成功，ID: {}", caseId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除个人病例失败，ID: {}", caseId, e);
            return false;
        }
    }

    /**
     * 批量导入科室病例 - 使用@ESSync注解自动批量同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.DEP_CASE_INDEX_NAME, async = true)
    public boolean batchImportDepartmentCases(List<DepCase> depCases) {
        try {
            if (depCases == null || depCases.isEmpty()) {
                return true;
            }

            Date now = new Date();
            for (DepCase depCase : depCases) {
                depCase.setCreateTime(now);
                depCase.setUpdateTime(now);
                depCase.setDelFlag("0");
                depCaseMapper.insert(depCase);
            }

            log.info("批量导入科室病例成功，数量: {}", depCases.size());
            return true;
        } catch (Exception e) {
            log.error("批量导入科室病例失败", e);
            return false;
        }
    }

    /**
     * 批量导入个人病例 - 使用@ESSync注解自动批量同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId", async = true)
    public boolean batchImportPersonalCases(List<UserCase> userCases) {
        try {
            if (userCases == null || userCases.isEmpty()) {
                return true;
            }

            Date now = new Date();
            for (UserCase userCase : userCases) {
                userCase.setCreateTime(now);
                userCase.setUpdateTime(now);
                userCase.setDelFlag("0");
                userCaseMapper.insert(userCase);
            }

            log.info("批量导入个人病例成功，数量: {}", userCases.size());
            return true;
        } catch (Exception e) {
            log.error("批量导入个人病例失败", e);
            return false;
        }
    }

    /**
     * 在指定目录下创建个人病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    public boolean createPersonalCaseInCatalog(Long caseId, Long catalogId) {
        try {
            // 参数校验
            if (caseId == null || catalogId == null) {
                log.error("添加个人病例到目录失败：参数不能为空，caseId: {}, catalogId: {}", caseId, catalogId);
                return false;
            }

            // 检查病例是否存在
            UserCase userCase = userCaseMapper.selectById(caseId);
            if (userCase == null) {
                log.error("添加个人病例到目录失败：病例不存在，caseId: {}", caseId);
                return false;
            }

            // 检查关联关系是否已存在，避免重复添加
            long existCount = userCaseCatalogService.lambdaQuery()
                    .eq(UserCaseCatalog::getUserCaseId, caseId)
                    .eq(UserCaseCatalog::getCatalogId, catalogId)
                    .count();
            
            if (existCount > 0) {
                log.warn("个人病例已存在于该目录中，caseId: {}, catalogId: {}", caseId, catalogId);
                return true; // 已存在则返回成功
            }

            // 在t_user_case_catalog表中插入关联记录
            UserCaseCatalog userCaseCatalog = new UserCaseCatalog();
            userCaseCatalog.setUserCaseId(caseId);
            userCaseCatalog.setCatalogId(catalogId);
            
            boolean result = userCaseCatalogService.save(userCaseCatalog);
            if (result) {
                log.info("添加个人病例到目录成功，caseId: {}, catalogId: {}", caseId, catalogId);
            } else {
                log.error("添加个人病例到目录失败，caseId: {}, catalogId: {}", caseId, catalogId);
            }
            return result;
        } catch (Exception e) {
            log.error("添加个人病例到目录异常，caseId: {}, catalogId: {}", caseId, catalogId, e);
            return false;
        }
    }

    /**
     * 从指定目录删除个人病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.DELETE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    public boolean deletePersonalCaseFromCatalog(Long caseId, Long catalogId) {
        try {
            // 参数校验
            if (caseId == null || catalogId == null) {
                log.error("从目录移除个人病例失败：参数不能为空，caseId: {}, catalogId: {}", caseId, catalogId);
                return false;
            }

            // 检查关联关系是否存在
            long existCount = userCaseCatalogService.lambdaQuery()
                    .eq(UserCaseCatalog::getUserCaseId, caseId)
                    .eq(UserCaseCatalog::getCatalogId, catalogId)
                    .count();
            
            if (existCount == 0) {
                log.warn("个人病例不存在于该目录中，caseId: {}, catalogId: {}", caseId, catalogId);
                return true; // 不存在则返回成功
            }

            // 从t_user_case_catalog表中删除关联记录
            boolean result = userCaseCatalogService.lambdaUpdate()
                    .eq(UserCaseCatalog::getUserCaseId, caseId)
                    .eq(UserCaseCatalog::getCatalogId, catalogId)
                    .remove();

            if (result) {
                log.info("从目录移除个人病例成功，caseId: {}, catalogId: {}", caseId, catalogId);
                
                // 查询该病例是否还有其他目录关联
                long remainingCount = userCaseCatalogService.lambdaQuery()
                        .eq(UserCaseCatalog::getUserCaseId, caseId)
                        .count();
                
                if (remainingCount == 0) {
                    log.info("个人病例没有其他目录关联，将触发ES DELETE同步，caseId: {}", caseId);
                    // 注意：这里不需要手动删除ES文档，ES同步切面会根据CATALOG_REMOVE类型和剩余关联数量自动处理
                } else {
                    log.info("个人病例还有其他目录关联，将触发ES CATALOG_REMOVE同步，caseId: {}, 剩余关联数: {}", caseId, remainingCount);
                }
            } else {
                log.error("从目录移除个人病例失败，caseId: {}, catalogId: {}", caseId, catalogId);
            }
            return result;
        } catch (Exception e) {
            log.error("从目录移除个人病例异常，caseId: {}, catalogId: {}", caseId, catalogId, e);
            return false;
        }
    }

    /**
     * 将个人病例添加到指定目录（兼容旧接口）
     */
    @Override
    @Deprecated
    public boolean addPersonalCaseToCatalog(Long caseId, Long catalogId) {
        // 委托给新的方法实现
        return createPersonalCaseInCatalog(caseId, catalogId);
    }

    /**
     * 从指定目录移除个人病例（兼容旧接口）
     */
    @Override
    @Deprecated
    public boolean removePersonalCaseFromCatalog(Long caseId, Long catalogId) {
        // 委托给新的方法实现
        return deletePersonalCaseFromCatalog(caseId, catalogId);
    }

}
