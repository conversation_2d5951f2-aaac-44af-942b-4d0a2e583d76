package com.jusha.caselibrary.search.service.impl;

import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.mybatisplus.entity.DepCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCaseCatalog;
import com.jusha.caselibrary.mybatisplus.mapper.DepCaseMapper;
import com.jusha.caselibrary.mybatisplus.mapper.UserCaseMapper;
import com.jusha.caselibrary.mybatisplus.service.UserCaseCatalogService;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.search.service.CaseManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @ClassName CaseManagementServiceImpl
 * @Description 病例管理服务实现类 @ESSync注解的使用，通过AOP切面自动处理ES同步
 * <AUTHOR>
 * @Date 2025/7/7 17:04
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseManagementServiceImpl implements CaseManagementService {

    private final DepCaseMapper depCaseMapper;

    private final UserCaseMapper userCaseMapper;

    /**
     * 创建科室病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
//    @ESSync(type = ESSync.SyncType.CREATE, indexType  = Constant.DEP_CASE_INDEX_NAME)
    public Long createDepartmentCase(DepCase depCase) {
        try {
            // 设置创建时间
            long caseId = YitIdHelper.nextId();
            depCase.setCaseId(caseId);
            depCase.setCreateTime(new Date());
            depCase.setUpdateTime(new Date());
            depCase.setDelFlag("0");

            int result = depCaseMapper.insert(depCase);
            log.info("创建科室病例成功，ID: {}", depCase.getCaseId());
            if (result > 0) {
                return depCase.getCaseId();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("创建科室病例失败", e);
            return null;
        }
    }

    /**
     * 更新科室病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.DEP_CASE_INDEX_NAME)
    public Long updateDepartmentCase(DepCase depCase) {
        try {
            // 设置更新时间
            depCase.setUpdateTime(new Date());

            int result = depCaseMapper.updateById(depCase);
            log.info("更新科室病例成功，ID: {}", depCase.getCaseId());
            if (result > 0) {
                return depCase.getCaseId();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("更新科室病例失败，ID: {}", depCase.getCaseId(), e);
            return null;
        }
    }

    /**
     * 删除科室病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.DELETE, indexType = Constant.DEP_CASE_INDEX_NAME)
    public boolean deleteDepartmentCase(Long caseId) {
        try {
            int result = depCaseMapper.deleteById(caseId);
            log.info("删除科室病例成功，ID: {}", caseId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除科室病例失败，ID: {}", caseId, e);
            return false;
        }
    }

    /**
     * 创建个人病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    public boolean createPersonalCase(UserCase userCase) {
        try {
            // 设置创建时间
            userCase.setCreateTime(new Date());
            userCase.setUpdateTime(new Date());
            userCase.setDelFlag("0");

            int result = userCaseMapper.insert(userCase);
            log.info("创建个人病例成功，ID: {}", userCase.getCaseId());
            return result > 0;
        } catch (Exception e) {
            log.error("创建个人病例失败", e);
            return false;
        }
    }

    /**
     * 更新个人病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.UPDATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    public boolean updatePersonalCase(UserCase userCase) {
        try {
            // 设置更新时间
            userCase.setUpdateTime(new Date());

            int result = userCaseMapper.updateById(userCase);
            log.info("更新个人病例成功，ID: {}", userCase.getCaseId());
            return result > 0;
        } catch (Exception e) {
            log.error("更新个人病例失败，ID: {}", userCase.getCaseId(), e);
            return false;
        }
    }

    /**
     * 删除个人病例 - 使用@ESSync注解自动同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.DELETE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId")
    public boolean deletePersonalCase(Long caseId) {
        try {
            int result = userCaseMapper.deleteById(caseId);
            log.info("删除个人病例成功，ID: {}", caseId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除个人病例失败，ID: {}", caseId, e);
            return false;
        }
    }

    /**
     * 批量导入科室病例 - 使用@ESSync注解自动批量同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.DEP_CASE_INDEX_NAME, async = true)
    public boolean batchImportDepartmentCases(List<DepCase> depCases) {
        try {
            if (depCases == null || depCases.isEmpty()) {
                return true;
            }

            Date now = new Date();
            for (DepCase depCase : depCases) {
                depCase.setCreateTime(now);
                depCase.setUpdateTime(now);
                depCase.setDelFlag("0");
                depCaseMapper.insert(depCase);
            }

            log.info("批量导入科室病例成功，数量: {}", depCases.size());
            return true;
        } catch (Exception e) {
            log.error("批量导入科室病例失败", e);
            return false;
        }
    }

    /**
     * 批量导入个人病例 - 使用@ESSync注解自动批量同步到ES
     */
    @Override
    @Transactional
    @ESSync(type = ESSync.SyncType.CREATE, indexType = Constant.PERSON_CASE_INDEX_NAME, idField = "userCaseId", async = true)
    public boolean batchImportPersonalCases(List<UserCase> userCases) {
        try {
            if (userCases == null || userCases.isEmpty()) {
                return true;
            }

            Date now = new Date();
            for (UserCase userCase : userCases) {
                userCase.setCreateTime(now);
                userCase.setUpdateTime(now);
                userCase.setDelFlag("0");
                userCaseMapper.insert(userCase);
            }

            log.info("批量导入个人病例成功，数量: {}", userCases.size());
            return true;
        } catch (Exception e) {
            log.error("批量导入个人病例失败", e);
            return false;
        }
    }

}
