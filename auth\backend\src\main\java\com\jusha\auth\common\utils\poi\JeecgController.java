package com.jusha.auth.common.utils.poi;

import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

/**
 * @Description: Controller基类
 * @Author: <EMAIL>
 * @Date: 2019-4-21 8:13
 * @Version: 1.0
 */
@Slf4j
public class JeecgController<T> {

    /**
     * 导出excel
     * @date 2020-06-05
     * @auther zax
     * @param pageList 数据列表
     * @param clazz 数据实体类class
     * @param title 报表标题
     * @param sheetName 报表sheet名称
     * @param fileName 报表文件名称
     */
    public ModelAndView exportXls(List<T> pageList, Class<T> clazz, String title,String sheetName,String fileName) {

        //AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, fileName); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.CLASS, clazz);
        ExportParams params = new ExportParams(title, sheetName, ExcelType.XSSF);
        params.setAddIndex(true);
        mv.addObject(NormalExcelConstants.PARAMS, params);
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 导出excel
     * @date 2020-06-05
     * @auther zax
     * @param pageList 数据列表
     * @param selections 导出指定字段,逗号隔开
     * @param entity map
     * @param title 报表标题
     * @param sheetName 报表sheet名称
     * @param fileName 报表文件名称
     */
    public ModelAndView exportXlsForMap(List<T> pageList, String selections, List<ExcelExportEntity> entity, String title, String sheetName, String fileName) {

        //AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelViewForMap());
        mv.addObject(NormalExcelConstants.FILE_NAME, fileName); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject("entityList", entity);
        ExportParams params = new ExportParams(title, sheetName, ExcelType.XSSF);
        params.setAddIndex(true);
        mv.addObject(NormalExcelConstants.PARAMS, params);
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 导出excel
     * @date 2022-03-03
     * @auther zax
     * @param pageList 数据列表
     * @param selections 导出指定字段,逗号隔开
     * @param entity map
     * @param title 报表标题
     * @param sheetName 报表sheet名称
     * @param fileName 报表文件名称
     */
    public ModelAndView exportXlsForMapSheet(List<Map<String, Object>> listMap, String title, String sheetName, String fileName) {

        //AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelViewForMap());
        mv.addObject(NormalExcelConstants.FILE_NAME, fileName); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject("weijianwei", "卫健委");
        mv.addObject(NormalExcelConstants.MAP_LIST, listMap);
        return mv;
    }
}
