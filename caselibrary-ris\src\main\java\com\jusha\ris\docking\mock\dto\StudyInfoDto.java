package com.jusha.ris.docking.mock.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title StudyInfoDto
 * @description
 * @date 2025/2/24
 */
@Data
public class StudyInfoDto {

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("出生日期")
    private LocalDate birthDate;

    @ApiModelProperty("患者性别")
    private Integer patientSex;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("唯一患者编号")
    private String patientNo;

    @ApiModelProperty("申请单号")
    private String applyNo;

    @ApiModelProperty("患者类型")
    private String patientType;

    @ApiModelProperty("门诊/住院号")
    private String outInPatientNo;

    @ApiModelProperty("患者年龄")
    private String patientAge;

    @ApiModelProperty("检查类型")
    private String studyType;

    @ApiModelProperty("检查部位")
    private String studyParts;

    @ApiModelProperty("检查项目")
    private String studyItems;

    @ApiModelProperty("申请科室")
    private String applyDepartment;

    @ApiModelProperty("申请医生")
    private String applyDoctor;

    @ApiModelProperty("申请时间")
    private LocalDateTime applyTime;

    @ApiModelProperty("检查目的")
    private String studyPurpose;

    @ApiModelProperty("体征")
    private String physicalSign;

    @ApiModelProperty("患者主诉")
    private String selfComplaints;

    @ApiModelProperty("病史")
    private String historyDisease;

    @ApiModelProperty("临床诊断")
    private String clinicalDiagnosis;

    @ApiModelProperty("唯一检查号")
    private String studyNo;

    @ApiModelProperty("影像号")
    private String accessNumber;

    @ApiModelProperty("检查uid")
    private String studyUid;

    @ApiModelProperty("检查设备")
    private String studyDevice;

    @ApiModelProperty("检查时间")
    private LocalDateTime studyTime;

    @ApiModelProperty("检查技师")
    private String artificer;

    @ApiModelProperty("上传医院")
    private Long uploadHospitalId;

    @ApiModelProperty("所属组id")
    private Long groupId;


}
