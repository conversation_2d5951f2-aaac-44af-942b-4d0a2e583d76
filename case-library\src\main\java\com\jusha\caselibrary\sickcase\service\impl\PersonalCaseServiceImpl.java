package com.jusha.caselibrary.sickcase.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCaseCatalog;
import com.jusha.caselibrary.mybatisplus.service.UserCaseCatalogService;
import com.jusha.caselibrary.mybatisplus.service.UserCaseService;
import com.jusha.caselibrary.mybatisplus.system.entity.UserCatalog;
import com.jusha.caselibrary.mybatisplus.system.service.UserCatalogService;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseCreateReq;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseUpdateReq;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.mapper.PersonalCaseMapper;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @ClassName PersonalCaseServiceImpl
 * @Description 个人病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/10 09:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalCaseServiceImpl implements PersonalCaseService {

    private final PersonalCaseMapper personalCaseMapper;

    private final UserCaseService usrCaseService;

    private final UserCaseCatalogService userCaseCatalogService;

    private final UserCatalogService userCatalogService;

    @Override
    public List<PersonalCaseDetailResp> getPersonalCaseDetailList(PersonalCaseSearchReq req) {
        List<PersonalCaseDetailResp> list = personalCaseMapper.getPersonalCaseDetailList(req, LoginUtil.getLoginUserId());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(PersonalCaseDetailResp::setDictLabels);
        }
        return list;
    }

    @Override
    public PageInfo<PersonalCaseDetailResp> getPersonalCaseDetailPage(PersonalCaseSearchReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<PersonalCaseDetailResp> list = getPersonalCaseDetailList(req);
        return new PageInfo<>(list);
    }

    @Override
    public PersonalCaseDetailResp getPersonalCaseDetail(Long userCaseId) {
        PersonalCaseSearchReq req = new PersonalCaseSearchReq();
        req.setUserCaseId(userCaseId);
        List<PersonalCaseDetailResp> list = getPersonalCaseDetailList(req);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return new PersonalCaseDetailResp();
    }

    @Override
    public void deletePersonalCaseById(Long userCaseId, Long catalogId) {
        //1、删除个人病例与目录的关联关系
        userCaseCatalogService.lambdaUpdate()
                .eq(UserCaseCatalog::getCatalogId, catalogId)
                .eq(UserCaseCatalog::getUserCaseId, userCaseId).remove();
        //2、如果该病例不存在于其他目录，则删除病例
        if (userCaseCatalogService.lambdaQuery().eq(UserCaseCatalog::getUserCaseId, userCaseId).count() == 0) {
            usrCaseService.lambdaUpdate().eq(UserCase::getUserCaseId, userCaseId).remove();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long createPersonalCaseInCatalog(PersonalCaseCreateReq req) {
        Long loginUserId = LoginUtil.getLoginUserId();
        Long caseId = req.getCaseId();
        //检查目录是否存在
        Long catalogId = req.getCatalogId();
        UserCatalog userCatalog = userCatalogService.getById(catalogId);
        if (userCatalog == null) {
            throw new BusinessException(LocaleUtil.getLocale("catalog.not.exist"));
        }
        //保存病例
        UserCase userCase;
        if (caseId != null) {
            userCase = usrCaseService.lambdaQuery().eq(UserCase::getCaseId, caseId)
                    .eq(UserCase::getCreateBy, loginUserId).one();
        } else {
            userCase = new UserCase();
            long userCaseId = YitIdHelper.nextId();
            caseId = YitIdHelper.nextId();
            BeanUtils.copyProperties(req, userCase);
            userCase.setCaseId(caseId);
            userCase.setUserCaseId(userCaseId);
            userCase.setCreateBy(LoginUtil.getLoginUserId());
            usrCaseService.save(userCase);
        }
        //保存病例与目录的关联关系
        UserCaseCatalog userCaseCatalog = new UserCaseCatalog();
        userCaseCatalog.setId(YitIdHelper.nextId());
        userCaseCatalog.setUserCaseId(userCase.getUserCaseId());
        userCaseCatalog.setCatalogId(catalogId);
        userCaseCatalogService.save(userCaseCatalog);
        return userCase.getUserCaseId();
    }

    @Override
    public void updatePersonalCase(PersonalCaseUpdateReq req) {
        UserCase userCase = usrCaseService.getById(req.getUserCaseId());
        if (userCase == null) {
            throw new BusinessException(LocaleUtil.getLocale("case.not.exist"));
        }
        BeanUtils.copyProperties(req, userCase);
        userCase.setUpdateTime(new Date());
        userCase.setUpdateBy(LoginUtil.getLoginUserId());
        userCase.setModifyUsers(userCase.getModifyUsers() + "," + LoginUtil.getLoginUserId());
        usrCaseService.updateById(userCase);
    }
}
