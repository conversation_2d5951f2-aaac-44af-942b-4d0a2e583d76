package com.jusha.caselibrary.sickcase.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCaseCatalog;
import com.jusha.caselibrary.mybatisplus.service.UserCaseCatalogService;
import com.jusha.caselibrary.mybatisplus.service.UserCaseService;
import com.jusha.caselibrary.sickcase.dto.req.PersonalCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.PersonalCaseDetailResp;
import com.jusha.caselibrary.sickcase.mapper.PersonalCaseMapper;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName PersonalCaseServiceImpl
 * @Description 个人病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/10 09:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonalCaseServiceImpl implements PersonalCaseService {

    private final PersonalCaseMapper personalCaseMapper;

    private final UserCaseService usrCaseService;

    private final UserCaseCatalogService userCaseCatalogService;

    @Override
    public List<PersonalCaseDetailResp> getPersonalCaseDetailList(PersonalCaseSearchReq req) {
        List<PersonalCaseDetailResp> list = personalCaseMapper.getPersonalCaseDetailList(req, LoginUtil.getLoginUserId());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(PersonalCaseDetailResp::setDictLabels);
        }
        return list;
    }

    @Override
    public PageInfo<PersonalCaseDetailResp> getPersonalCaseDetailPage(PersonalCaseSearchReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<PersonalCaseDetailResp> list = getPersonalCaseDetailList(req);
        return new PageInfo<>(list);
    }

    @Override
    public PersonalCaseDetailResp getPersonalCaseDetail(Long caseId) {
        PersonalCaseSearchReq req = new PersonalCaseSearchReq();
        req.setCaseId(caseId);
        List<PersonalCaseDetailResp> list = getPersonalCaseDetailList(req);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return new PersonalCaseDetailResp();
    }

    @Override
    public void deletePersonalCaseById(Long caseId, Long catalogId) {
        //1、删除个人病例与目录的关联关系
        userCaseCatalogService.lambdaUpdate()
                .eq(UserCaseCatalog::getCatalogId, catalogId)
                .eq(UserCaseCatalog::getCaseId, caseId).remove();
        //2、如果该病例不存在于其他目录，则删除病例
        if (userCaseCatalogService.lambdaQuery().eq(UserCaseCatalog::getCaseId, caseId).count() == 0) {
            usrCaseService.lambdaUpdate().eq(UserCase::getCaseId, caseId).remove();
        }
    }
}
