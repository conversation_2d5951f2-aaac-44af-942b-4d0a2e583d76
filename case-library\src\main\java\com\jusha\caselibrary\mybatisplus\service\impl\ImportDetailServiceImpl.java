package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.ImportDetail;
import com.jusha.caselibrary.mybatisplus.mapper.ImportDetailMapper;
import com.jusha.caselibrary.mybatisplus.service.ImportDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 病例导入详情表 服务实现类
 * <AUTHOR>
 */
@Service
public class ImportDetailServiceImpl extends ServiceImpl<ImportDetailMapper, ImportDetail> implements ImportDetailService {

}
