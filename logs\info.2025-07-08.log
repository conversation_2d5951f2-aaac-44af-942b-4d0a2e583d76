09:41:15.153 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
09:41:16.143 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
09:41:16.144 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
09:41:16.147 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
09:41:16.338 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:41:16.339 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:41:16.348 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
09:41:17.346 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
09:41:17.369 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0
09:41:17.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:41:17.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/584698209
09:41:17.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/1936670366
09:41:17.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:41:17.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:41:17.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
09:41:26.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1751938888703_172.30.192.1_10170
09:41:26.376 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Notify connected event to listeners.
09:41:26.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Connected,notify listen context...
09:41:26.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:26.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1255958078
09:41:26.504 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
09:41:27.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:41:27.681 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Success to connect a server [*************:8848], connectionId = 1751938890532_172.30.192.1_10186
09:41:27.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Abandon prev connection, server is *************:8848, connectionId is 1751938888703_172.30.192.1_10170
09:41:27.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1751938888703_172.30.192.1_10170
09:41:27.705 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Notify disconnected event to listeners
09:41:27.705 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] DisConnected,clear listen context...
09:41:27.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Notify connected event to listeners.
09:41:27.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Connected,notify listen context...
09:41:28.151 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:41:28.362 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
09:41:28.455 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
09:41:29.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Success to connect a server [*************:8848], connectionId = 1751938891376_172.30.192.1_10191
09:41:29.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Abandon prev connection, server is *************:8848, connectionId is 1751938890532_172.30.192.1_10186
09:41:29.329 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1751938890532_172.30.192.1_10186
09:41:29.331 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Notify disconnected event to listeners
09:41:29.331 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] DisConnected,clear listen context...
09:41:29.331 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Notify connected event to listeners.
09:41:29.332 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [6d8f3675-f3c6-425e-a029-80793f1dfa0c_config-0] Connected,notify listen context...
09:41:36.303 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
09:41:37.825 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
09:41:40.505 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
09:41:40.505 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
09:41:40.505 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
09:41:40.603 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
09:41:40.604 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
09:41:40.604 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
09:41:40.965 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
09:41:40.966 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
09:41:40.966 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
09:41:41.083 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
09:41:41.083 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
09:41:41.084 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
09:41:41.206 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
09:41:41.207 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
09:41:41.208 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
09:41:41.295 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
09:41:41.296 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
09:41:41.296 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
09:41:41.410 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
09:41:41.411 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
09:41:41.411 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
09:47:13.344 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
09:47:14.928 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
09:47:14.928 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
09:47:14.933 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
09:47:15.167 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:47:15.167 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:47:15.181 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
09:47:16.033 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
09:47:16.038 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0
09:47:16.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:47:16.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/1374432753
09:47:16.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/261052089
09:47:16.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:47:16.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:47:16.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
09:47:23.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1751939245999_172.30.192.1_11821
09:47:23.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:47:23.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1860118977
09:47:23.386 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Notify connected event to listeners.
09:47:23.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [ba5c427b-7424-4f36-9c9b-15a5f4aba331_config-0] Connected,notify listen context...
09:47:23.477 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
09:47:23.615 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
09:47:23.682 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
09:47:27.825 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
09:47:29.141 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
09:47:31.185 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
09:47:31.186 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
09:47:31.186 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
09:47:31.253 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
09:47:31.254 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
09:47:31.254 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
09:47:31.504 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
09:47:31.504 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
09:47:31.504 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
09:47:31.580 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
09:47:31.580 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
09:47:31.580 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
09:47:31.641 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
09:47:31.642 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
09:47:31.642 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
09:47:31.687 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
09:47:31.687 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
09:47:31.688 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
09:47:31.758 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
09:47:31.759 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
09:47:31.759 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
09:47:32.178 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:47:33.559 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:47:34.165 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
09:47:34.166 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
09:47:34.166 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictData not contain MppMultiId anno
09:47:34.235 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
09:47:34.236 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
09:47:34.236 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysDictType not contain MppMultiId anno
09:47:34.870 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
09:47:34.870 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
09:47:34.871 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysLogininfor not contain MppMultiId anno
09:47:35.108 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
09:47:35.108 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
09:47:35.109 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysInterface not contain MppMultiId anno
09:47:36.420 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
09:47:36.430 [main] INFO  c.g.j.m.c.PlusConfig - [initRM,51] - mpp.utilBasePath is null no util alias for xml
09:47:36.476 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
09:47:36.497 [main] INFO  c.n.c.s.URLConfigurationSource - [<init>,127] - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
09:47:39.997 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,62] - initializer namespace from System Property : null
09:47:39.998 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$0,66] - initializer namespace from System Environment :null
09:47:39.998 [main] INFO  c.a.n.client.naming - [lambda$initNamespaceForNaming$1,73] - initializer namespace from System Property :null
09:47:40.032 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:47:40.032 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:47:40.056 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 12c5cc9c-aa2b-490f-88ca-1182080fa36a
09:47:40.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] RpcClient init label, labels = {module=naming, source=sdk}
09:47:40.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:47:40.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:47:40.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:47:40.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
09:47:40.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Success to connect to server [*************:8848] on start up, connectionId = 1751939261785_172.30.192.1_11962
09:47:40.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:47:40.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1860118977
09:47:40.356 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Notify connected event to listeners.
09:47:40.357 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
09:47:40.365 [main] INFO  c.a.n.client.naming - [subscribe,141] - [SUBSCRIBE-SERVICE] service:auth-server, group:DEFAULT_GROUP, clusters:DEFAULT 
09:47:40.402 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
09:47:40.433 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> []
09:47:40.564 [main] INFO  io.undertow - [start,117] - starting server: Undertow - 2.1.7.Final
09:47:40.606 [main] INFO  org.xnio - [<clinit>,95] - XNIO version 3.8.0.Final
09:47:40.641 [main] INFO  org.xnio.nio - [<clinit>,59] - XNIO NIO Implementation Version 3.8.0.Final
09:47:40.869 [main] INFO  org.jboss.threads - [<clinit>,52] - JBoss Threads version 3.1.0.Final
09:47:41.007 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:47:41.008 [nacos-grpc-client-executor-*************-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:47:41.852 [main] INFO  c.a.n.client.naming - [registerService,111] - [REGISTER-SERVICE] pacs-test registering service auth-server with instance Instance{instanceId='null', ip='***********', port=9025, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
09:47:41.863 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP auth-server ***********:9025 register finished
09:47:42.236 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,160] - Context refreshed
09:47:42.368 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,163] - Found 1 custom documentation plugin(s)
09:47:42.520 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
09:47:42.521 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:47:42.527 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [isChangedServiceInfo,234] - new ips(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"***********#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"***********","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
09:47:42.527 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@auth-server@@DEFAULT -> [{"instanceId":"***********#9025#DEFAULT#DEFAULT_GROUP@@auth-server","ip":"***********","port":9025,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@auth-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
09:47:42.529 [nacos-grpc-client-executor-*************-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c5cc9c-aa2b-490f-88ca-1182080fa36a] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:47:42.999 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
09:47:43.000 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_1
09:47:43.001 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_1
09:47:43.003 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
09:47:43.005 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
09:47:43.021 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: logoutUsingPOST_1
09:47:43.033 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_2
09:47:43.034 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_2
09:47:43.035 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getMenuRoutersUsingGET_1
09:47:43.036 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getPermissionUsingGET_1
09:47:43.038 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_2
09:47:43.039 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_1
09:47:43.043 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
09:47:43.045 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
09:47:43.051 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_3
09:47:43.068 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_3
09:47:43.069 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
09:47:43.073 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
09:47:43.074 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_3
09:47:43.081 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_4
09:47:43.084 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_1
09:47:43.085 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_1
09:47:43.086 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_1
09:47:43.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_1
09:47:43.087 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: childrenListUsingGET_1
09:47:43.088 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_1
09:47:43.089 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_4
09:47:43.091 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_3
09:47:43.093 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_1
09:47:43.094 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_4
09:47:43.096 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_4
09:47:43.099 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_1
09:47:43.101 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_1
09:47:43.102 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: userListUsingGET_1
09:47:43.103 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_4
09:47:43.106 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_5
09:47:43.106 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_5
09:47:43.107 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_5
09:47:43.110 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_5
09:47:43.111 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_5
09:47:43.113 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_6
09:47:43.114 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_6
09:47:43.115 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_6
09:47:43.118 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_6
09:47:43.118 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_6
09:47:43.121 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_7
09:47:43.122 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_7
09:47:43.123 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_7
09:47:43.126 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_7
09:47:43.126 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: refreshCacheUsingPOST_1
09:47:43.127 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_7
09:47:43.128 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_8
09:47:43.129 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_8
09:47:43.130 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: excludeChildUsingGET_2
09:47:43.130 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_8
09:47:43.131 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_8
09:47:43.132 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_8
09:47:43.135 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_9
09:47:43.135 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_9
09:47:43.136 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_9
09:47:43.140 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_9
09:47:43.141 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_9
09:47:43.145 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_10
09:47:43.146 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_10
09:47:43.147 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_10
09:47:43.148 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_10
09:47:43.148 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_10
09:47:43.152 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_11
09:47:43.152 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_11
09:47:43.153 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: roleMenuTreeselectUsingGET_2
09:47:43.157 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_11
09:47:43.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_11
09:47:43.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_11
09:47:43.159 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_12
09:47:43.159 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_12
09:47:43.164 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_12
09:47:43.166 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: allocatedListUsingGET_2
09:47:43.166 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserUsingPOST_2
09:47:43.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: cancelAuthUserAllUsingPOST_2
09:47:43.167 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_2
09:47:43.168 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: dataScopeUsingPOST_2
09:47:43.168 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_12
09:47:43.169 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_12
09:47:43.169 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: groupTreeUsingGET_2
09:47:43.171 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_13
09:47:43.172 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_13
09:47:43.172 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: selectAuthUserAllUsingPOST_2
09:47:43.174 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: unallocatedListUsingGET_2
09:47:43.176 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_13
09:47:43.177 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_3
09:47:43.177 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_13
09:47:43.179 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_13
09:47:43.180 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_14
09:47:43.183 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_14
09:47:43.184 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_14
09:47:43.185 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_15
09:47:43.188 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_14
09:47:43.189 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_1
09:47:43.190 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_4
09:47:43.191 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_14
09:47:43.192 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
09:47:43.193 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_1
09:47:43.199 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_15
09:47:43.200 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_1
09:47:43.200 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
09:47:43.201 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_1
09:47:43.201 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
09:47:43.202 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_15
09:47:43.202 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_1
09:47:43.204 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_2
09:47:43.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_15
09:47:43.207 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addExistUsingPOST_1
09:47:43.208 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: authRoleUsingGET_2
09:47:43.209 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: changeStatusUsingPOST_5
09:47:43.210 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPOST_15
09:47:43.218 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
09:47:43.219 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forceLogoutUsingPOST_2
09:47:43.220 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: forgetPwdUsingPOST_1
09:47:43.221 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_16
09:47:43.221 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
09:47:43.222 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importExcelUsingPOST_2
09:47:43.222 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
09:47:43.222 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: insertAuthRoleUsingPOST_2
09:47:43.224 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_16
09:47:43.224 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_16
09:47:43.225 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_3
09:47:43.225 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: updatePwdUsingPOST_4
09:47:43.303 [main] INFO  c.j.a.AuthServerApplication - [logStarted,61] - Started AuthServerApplication in 33.189 seconds (JVM running for 38.568)
09:47:43.313 [main] INFO  c.a.n.c.c.i.CacheData - [<clinit>,65] - nacos.cache.data.init.snapshot = true 
09:47:43.314 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server+DEFAULT_GROUP+pacs-test
09:47:43.316 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server, group=DEFAULT_GROUP, cnt=1
09:47:43.324 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,373] - [fixed-pacs-test-*************_8848] [subscribe] auth-server.yml+DEFAULT_GROUP+pacs-test
09:47:43.325 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,180] - [fixed-pacs-test-*************_8848] [add-listener] ok, tenant=pacs-test, dataId=auth-server.yml, group=DEFAULT_GROUP, cnt=1
15:58:26.101 [SpringContextShutdownHook] INFO  io.undertow - [stop,252] - stopping server: Undertow - 2.1.7.Final
15:58:27.432 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:58:27.454 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,255] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
15:58:27.454 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,140] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
15:58:27.454 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,142] - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
15:58:27.454 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,257] - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
15:58:27.455 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,182] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
15:58:27.455 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,130] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
15:58:27.808 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,132] - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
15:58:27.810 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,192] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
15:58:27.811 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,197] - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
15:58:27.811 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,527] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
15:58:27.812 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,162] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
15:58:27.812 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,164] - com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
15:58:27.812 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,530] - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
15:58:27.812 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,453] - Shutdown rpc client, set status to shutdown
15:58:27.831 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [shutdown,455] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@52f0b4cd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:58:27.831 [SpringContextShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1751939261785_172.30.192.1_11962
15:58:27.861 [nacos-grpc-client-executor-*************-4449] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751939261785_172.30.192.1_11962]Ignore complete event,isRunning:false,isAbandon=false
15:58:27.948 [SpringContextShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,129] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@75d704ba[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 4450]
15:58:27.949 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,267] - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@1d3c5a04[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 7396]
15:58:27.998 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialWatcher - [stop,105] - [null] CredentialWatcher is stopped
15:58:28.004 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialService - [free,99] - [null] CredentialService is freed
15:58:28.005 [SpringContextShutdownHook] INFO  c.a.n.client.naming - [shutdown,189] - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
15:58:28.269 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:58:28.320 [SpringContextShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
