package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例导入失败记录表
 * <AUTHOR>
 */
@TableName("t_import_error")
@Data
public class ImportError {

    @ApiModelProperty(value = "导入任务id")
    @TableField("inport_id")
    private Long inportId;    

    @ApiModelProperty(value = "失败信息")
    @TableField("err_msg")
    private String errMsg;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

}
