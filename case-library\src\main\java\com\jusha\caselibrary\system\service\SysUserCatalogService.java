package com.jusha.caselibrary.system.service;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.resp.TreeSelect;
import com.jusha.caselibrary.mybatisplus.system.entity.UserCatalog;
import com.jusha.caselibrary.system.dto.req.UserCatalogReq;
import com.jusha.caselibrary.system.dto.req.UserCatalogUpdateReq;

import java.util.List;

/**
 * 目录管理 服务层
 *
 * <AUTHOR>
 */
public interface SysUserCatalogService {

    /**
     * 查询目录管理数据
     *
     * @param UserCatalogReq 目录信息查询入参
     * @return 目录信息集合
     */
    public List<UserCatalog> selectUserCatalogList(UserCatalogReq UserCatalogReq);

    /**
     * 新增保存目录信息
     *
     * @param UserCatalog 目录信息
     * @return 结果
     */
    public UserCatalog insertUserCatalog(UserCatalog UserCatalog);

    /**
     * 查询目录树结构信息
     *
     * @param UserCatalogReq 目录信息查询入参
     * @return 目录树信息集合
     */
    public List<TreeSelect> selectUserCatalogTreeList(UserCatalogReq UserCatalogReq);

    /**
     * 构建前端所需要树结构
     *
     * @param UserCatalogs 目录列表
     * @return 树结构列表
     */
    public List<UserCatalog> buildUserCatalogTree(List<UserCatalog> UserCatalogs);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param UserCatalogs 目录列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildUserCatalogTreeSelect(List<UserCatalog> UserCatalogs);

    /**
     * 根据目录ID查询信息
     *
     * @param CatalogId 目录ID
     * @return 目录信息
     */
    public UserCatalog selectUserCatalogById(Long CatalogId);

    /**
     * 是否存在目录子节点
     *
     * @param CatalogId 目录ID
     * @return 结果
     */
    public boolean hasChildByCatalogId(Long CatalogId);

    /**
     * 校验目录名称是否唯一
     *
     * @param UserCatalog 目录信息
     * @return 结果
     */
    public boolean checkUserCatalogNameUnique(UserCatalog UserCatalog);

    /**
     * 校验中心目录是否唯一
     *
     * @param UserCatalog 目录信息
     * @return 结果
     */
    public boolean checkUserCatalogCenterUnique(UserCatalog UserCatalog);

    /**
     * 修改保存目录信息
     *
     * @param userCatalogUpdateReq 目录信息
     * @return 结果
     */
    public ResultBean updateUserCatalog(UserCatalogUpdateReq userCatalogUpdateReq);

    /**
     * 删除目录管理信息
     *
     * @param userCatalogId 目录ID
     * @return 结果
     */
    public boolean deleteUserCatalogById(Long userCatalogId);
}
