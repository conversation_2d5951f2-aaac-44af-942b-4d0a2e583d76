package com.jusha.auth.system.controller;

import com.github.pagehelper.Constant;
import com.jusha.auth.common.annotation.EscapeWildcard;
import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.mybatisplus.entity.SysGroup;
import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.mybatisplus.entity.SysUserRole;
import com.jusha.auth.system.service.ISysGroupService;
import com.jusha.auth.system.service.ISysMenuService;
import com.jusha.auth.system.service.ISysRoleService;
import com.jusha.auth.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/role")
public class SysRoleController extends BaseController {
    @Autowired
    private ISysRoleService iSysroleService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysMenuService iSysmenuService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysGroupService iSysGroupService;

    @HasPermissions
    @GetMapping("/list")
    @EscapeWildcard
    public TableDataInfo list(SysRole role) {
        startPage();
        List<SysRole> list = iSysroleService.selectRoleList(role);
        return getDataTable(list);
    }

    /**
     * 根据角色编号获取详细信息
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long roleId) {
        return success(iSysroleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    @HasPermissions
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysRole role) {
        if (!iSysroleService.checkRoleNameUnique(role)) {
            return error(MessageUtils.message("role.already.exist.add"));
        }
        return success(iSysroleService.insertRole(role));
    }

    /**
     * 修改保存角色
     */
    @HasPermissions
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysRole role) {
        iSysroleService.checkRoleAllowed(role);
        if (!iSysroleService.checkRoleNameUnique(role)) {
            return error(MessageUtils.message("role.already.exist.edit"));
        }
        if (iSysroleService.updateRole(role)) {
            // 更新缓存用户权限（20240920这部分很奇怪，因为修改某个用户的时候，不应该改变当前登录用户的角色缓存，忘记当年怎么想的了）
//            LoginUser loginUser = getLoginUser();
//            if (StringUtils.isNotNull(loginUser.getSysUser()) && !loginUser.getSysUser().isAdmin()) {
//                loginUser.setPermissions(iSysmenuService.selectIterfacePathsByUserId(loginUser.getSysUser(), role.getPlatId()));
//                loginUser.setSysUser(iSysUserService.selectUserByUserName(loginUser.getSysUser().getUserName()));
//                tokenService.setLoginUser(loginUser);
//            }
            //诊断平台特殊需求
            if (role.getPlatId() != null && role.getPlatId().equals(223253731762245L)) {
                //只允许修改基准角色时才修改同类型的角色
                if (Arrays.asList(Constants.RIS_ROLE_NAME_PLAT_ADMIN, Constants.RIS_ROLE_NAME_GROUP_LEADER,
                        Constants.RIS_ROLE_NAME_HOSPITAL_ADMIN, Constants.RIS_ROLE_NAME_DOCTOR).contains(role.getRoleName())) {
                    //查询所有类型与其相同的角色
                    List<SysRole> sysRoles = iSysroleService.selectRoleListByType(223253731762245L, role.getRoleType());
                    for (SysRole sysRole : sysRoles) {
                        sysRole.setMenuIds(role.getMenuIds());
                        iSysroleService.updateRole(sysRole);
                    }
                }
            }
            return success();
        }
        return ResultBean.success();
    }

    /**
     * 修改保存数据权限
     */
    @HasPermissions
    @PostMapping(value = "/dataScope")
    public ResultBean dataScope(@Validated @RequestBody SysRole role) {
        iSysroleService.checkRoleAllowed(role);
        return resultBean(iSysroleService.authDataScope(role));
    }

    /**
     * 状态修改
     */
    @HasPermissions
    @PostMapping(value = "/changeStatus")
    public ResultBean changeStatus(@Validated @RequestBody SysRole role) {
        iSysroleService.checkRoleAllowed(role);
        return resultBean(iSysroleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @HasPermissions
    @PostMapping("/remove")
    public ResultBean remove(@RequestBody SysRole role) {
        return resultBean(iSysroleService.deleteRoleById(role.getRoleId()));
    }


    /**
     * 查询已分配用户角色列表
     */
    @HasPermissions
    @GetMapping("/authUser/allocatedList")
    public TableDataInfo allocatedList(SysUser user) {
        startPage();
        List<SysUser> list = iSysUserService.selectAllocatedList(user);
        return getDataTable(list);
    }

    /**
     * 查询未分配用户角色列表
     */
    @HasPermissions
    @GetMapping("/authUser/unallocatedList")
    public TableDataInfo unallocatedList(SysUser user) {
        startPage();
        List<SysUser> list = iSysUserService.selectUnallocatedList(user);
        return getDataTable(list);
    }

    /**
     * 取消授权用户
     */
    @HasPermissions
    @PostMapping("/authUser/cancel")
    public ResultBean cancelAuthUser(@Validated @RequestBody SysUserRole userRole) {
        return resultBean(iSysroleService.deleteAuthUser(userRole));
    }

    /**
     * 批量取消授权用户
     */
    @HasPermissions
    @PostMapping("/authUser/cancelAll")
    public ResultBean cancelAuthUserAll(@RequestParam Long roleId, @RequestParam Long[] userIds) {
        return resultBean(iSysroleService.deleteAuthUsers(roleId, userIds));
    }

    /**
     * 批量选择用户授权
     */
    @HasPermissions
    @PostMapping("/authUser/selectAll")
    public ResultBean selectAuthUserAll(@RequestParam Long roleId, @RequestParam Long[] userIds) {
        return resultBean(iSysroleService.insertAuthUsers(roleId, userIds));
    }

    /**
     * 获取对应角色分组树列表
     */
    @HasPermissions
    @GetMapping(value = "/groupTree/query")
    public ResultBean groupTree(@RequestParam Long roleId, @RequestParam Long platId) {
        ResultBean resultBean = ResultBean.success();
        resultBean.put("checkedKeys", iSysGroupService.selectGroupListByRoleId(roleId, platId));
        resultBean.put("groups", iSysGroupService.selectGroupTreeList(new SysGroup(platId)));
        return resultBean;
    }
}
