package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.ImportUpload;
import com.jusha.caselibrary.mybatisplus.mapper.ImportUploadMapper;
import com.jusha.caselibrary.mybatisplus.service.ImportUploadService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 病例导入文件上传 服务实现类
 * <AUTHOR>
 */
@Service
public class ImportUploadServiceImpl extends ServiceImpl<ImportUploadMapper, ImportUpload> implements ImportUploadService {

}
