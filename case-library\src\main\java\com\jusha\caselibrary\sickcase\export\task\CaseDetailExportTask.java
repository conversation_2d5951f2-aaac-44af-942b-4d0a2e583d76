package com.jusha.caselibrary.sickcase.export.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.RedisUtil;
import com.jusha.caselibrary.file.resp.FileUploadResp;
import com.jusha.caselibrary.file.service.impl.FileServiceImpl;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.CaseExportProcessResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.FollowCaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.utils.CaseExportUtil;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import com.jusha.caselibrary.common.util.FileUtil;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName CaseDetailExportTask
 * @Description 病例详情导出任务类 - 支持复杂单元格合并
 * <AUTHOR>
 * @Date 2025/7/10 14:41
 **/
@Slf4j
public class CaseDetailExportTask<D, T> implements Runnable {

    private final String taskId;
    private final T searchRequest;
    private final String tmpPath;

    /**
     * 构造函数
     *
     * @param taskId 任务ID
     * @param searchRequest 查询请求参数
     * @param tmpPath 临时文件路径
     */
    public CaseDetailExportTask(String taskId, T searchRequest, String tmpPath) {
        this.taskId = taskId;
        this.searchRequest = searchRequest;
        this.tmpPath = tmpPath;
    }

    @Override
    public void run() {
        try {
            log.info("开始执行病例导出任务，任务ID: {}", taskId);
            
            // 初始化进度
            updateProgress(0, LocaleUtil.getLocale("export.task.start"));
            
            // 查询病例数据
            updateProgress(10, LocaleUtil.getLocale("export.task.querying.data"));
            List<D> exportDataList = queryExportData();
            
            if (CollUtil.isEmpty(exportDataList)) {
                updateProgress(100, LocaleUtil.getLocale("export.task.complete.no.data"));
                return;
            }
            
            // 数据转换和处理
            updateProgress(30, LocaleUtil.getLocale("export.task.processing.data"));
            processExportData(exportDataList);
            
            // 生成导出文件
            updateProgress(50, LocaleUtil.getLocale("export.task.generating.file"));
            String exportFilePath = generateExportFile(exportDataList);
            
            // 上传导出文件
            updateProgress(90, LocaleUtil.getLocale("export.task.uploading.file"));
            uploadExportedFile(new File(exportFilePath), taskId, exportDataList.size());
            
            // 完成导出
            updateProgress(100, LocaleUtil.getLocale("export.task.complete.success"));
            
            log.info("病例导出任务完成，任务ID: {}, 文件路径: {}, 导出数量: {}", 
                    taskId, exportFilePath, exportDataList.size());
            
        } catch (Exception e) {
            log.error("病例导出任务执行失败，任务ID: {}, 错误信息: {}", taskId, e.getMessage(), e);
            updateProgress(-1, getMessage("export.task.failed", e.getMessage()));
        }
    }

    /**
     * 查询导出数据
     */
    @SuppressWarnings("unchecked")
    private List<D> queryExportData() {
        List<D> exportDataList = new ArrayList<>();
        
        try {
            // 获取DeptCaseService实例
            DeptCaseService deptCaseService = ContextHolder.getBean(DeptCaseService.class);
            
            // 调用业务服务查询病例详情列表
            List<DeptCaseDetailResp> caseDetailList = deptCaseService.getDeptCaseDetailList((DeptCaseSearchReq) searchRequest);
            
            if (CollUtil.isNotEmpty(caseDetailList)) {
                if (isFollowCaseExportType()) {
                    // FollowCaseExportDataDto类型：创建复合数据结构
                    FollowCaseExportDataDto followExportData = createFollowCaseExportData(caseDetailList);
                    exportDataList.add((D) followExportData);
                } else {
                    // CaseExportDataDto类型：逐个转换
                    for (DeptCaseDetailResp caseDetail : caseDetailList) {
                        try {
                            D exportData = convertToExportData(caseDetail);
                            if (exportData != null) {
                                exportDataList.add(exportData);
                            }
                        } catch (Exception e) {
                            log.warn("转换病例数据失败，病例ID: {}, 错误: {}", caseDetail.getCaseId(), e.getMessage());
                            // 继续处理其他病例
                        }
                    }
                }
                
                // 更新进度
                updateProgress(25, getMessage("export.task.query.data.found", caseDetailList.size()));
            }
            
        } catch (Exception e) {
            log.error("查询导出数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询导出数据失败: " + e.getMessage(), e);
        }
        
        return exportDataList;
    }

    /**
     * 创建FollowCaseExportDataDto复合数据结构
     */
    private FollowCaseExportDataDto createFollowCaseExportData(List<DeptCaseDetailResp> caseDetailList) {
        FollowCaseExportDataDto followExportData = new FollowCaseExportDataDto();
        
        // 转换病例数据列表
        List<CaseExportDataDto> caseExportDataList = new ArrayList<>();
        for (DeptCaseDetailResp caseDetail : caseDetailList) {
            try {
                CaseExportDataDto caseExportData = (CaseExportDataDto) convertToCaseExportData(caseDetail);
                caseExportDataList.add(caseExportData);
            } catch (Exception e) {
                log.warn("转换病例数据失败，病例ID: {}, 错误: {}", caseDetail.getCaseId(), e.getMessage());
            }
        }
        
        // 设置病例数据列表
        followExportData.setCaseExportDataDtoList(caseExportDataList);
        
        // 计算统计信息
        followExportData.calculateStatistics();
        
        log.info("创建FollowCaseExportDataDto完成，病例数量: {}, 统计字段数量: {}",
                caseExportDataList.size(), followExportData.getDynamicExportFieldNames().size());
        
        return followExportData;
    }

    /**
     * 转换为导出数据DTO
     * 支持复杂的数据结构转换，使用字典标签而不是原始值
     */
    @SuppressWarnings("unchecked")
    private D convertToExportData(DeptCaseDetailResp caseDetail) {
        // 根据泛型类型判断转换方式
        if (isFollowCaseExportType()) {
            // 这里暂时返回null，实际转换逻辑在processExportData中处理
            return null;
        } else {
            // 原有的CaseExportDataDto转换逻辑
            return convertToCaseExportData(caseDetail);
        }
    }

    /**
     * 转换为CaseExportDataDto
     */
    @SuppressWarnings("unchecked")
    private D convertToCaseExportData(DeptCaseDetailResp caseDetail) {
        CaseExportDataDto exportData = new CaseExportDataDto();
        
        // 复制基本属性
        BeanUtils.copyProperties(caseDetail, exportData);
        
        // 使用字典标签值替换原始字典值
        exportData.setDifficulty(StrUtil.isNotBlank(caseDetail.getDifficultyLabel()) ?
                caseDetail.getDifficultyLabel() : caseDetail.getDifficulty());

        exportData.setCaseCategory(StrUtil.isNotBlank(caseDetail.getCaseCategoryLabel()) ?
                caseDetail.getCaseCategoryLabel() : caseDetail.getCaseCategory());

        exportData.setSourceType(StrUtil.isNotBlank(caseDetail.getSourceTypeLabel()) ?
                caseDetail.getSourceTypeLabel() : caseDetail.getSourceType());

        exportData.setFollowStatus(StrUtil.isNotBlank(caseDetail.getFollowStatusLabel()) ?
                caseDetail.getFollowStatusLabel() : caseDetail.getFollowStatus());

        exportData.setQualityMatch(StrUtil.isNotBlank(caseDetail.getQualityMatchLabel()) ?
                caseDetail.getQualityMatchLabel() : caseDetail.getQualityMatch());

        exportData.setPositionMatch(StrUtil.isNotBlank(caseDetail.getPositionMatchLabel()) ?
                caseDetail.getPositionMatchLabel() : caseDetail.getPositionMatch());

        // 处理StudyInfoDto中的字典标签值
        if (CollUtil.isNotEmpty(caseDetail.getStudyInfoDtoList())) {
            caseDetail.getStudyInfoDtoList().forEach(study -> {
                // 将检查信息中的字典标签值也进行转换
                if (StrUtil.isNotBlank(study.getPatientTypeLabel())) {
                    study.setPatientType(study.getPatientTypeLabel());
                }
                if (StrUtil.isNotBlank(study.getStudyStateLabel())) {
                    study.setStudyState(study.getStudyStateLabel());
                }
                if (StrUtil.isNotBlank(study.getIsPostiveLabel())) {
                    study.setIsPostive(study.getIsPostiveLabel());
                }
                if (StrUtil.isNotBlank(study.getIsPublicLabel())) {
                    study.setIsPublic(study.getIsPublicLabel());
                }
            });
        }

        // 处理FollowInfoDto中的字典标签值
        if (CollUtil.isNotEmpty(caseDetail.getFollowInfoDtoList())) {
            caseDetail.getFollowInfoDtoList().forEach(follow -> {
                if (StrUtil.isNotBlank(follow.getFollowTypeLabel())) {
                    follow.setFollowType(follow.getFollowTypeLabel());
                }
            });
        }

        // 设置导出相关信息
        exportData.setExportTime(LocalDateTime.now());
        
        // 复制关联的数据列表（已经转换了字典值）
        exportData.setStudyInfoDtoList(caseDetail.getStudyInfoDtoList());
        exportData.setTagInfoDtoList(caseDetail.getTagInfoDtoList());
        exportData.setFollowInfoDtoList(caseDetail.getFollowInfoDtoList());
        exportData.setDiseaseOverviewInfoDto(caseDetail.getDiseaseOverviewInfoDto());
        
        return (D) exportData;
    }

    /**
     * 判断是否为FollowCaseExportDataDto类型
     */
    private boolean isFollowCaseExportType() {
        // 通过检查任务ID或其他方式判断，这里简单通过类名判断
        return this.getClass().getGenericSuperclass().toString().contains("FollowCaseExportDataDto");
    }

    /**
     * 处理导出数据
     * 主要进行数据清洗和格式化
     */
    private void processExportData(List<D> exportDataList) {
        try {
            if (isFollowCaseExportType()) {
                // FollowCaseExportDataDto类型：处理复合数据结构
                for (D data : exportDataList) {
                    FollowCaseExportDataDto followData = (FollowCaseExportDataDto) data;
                    // 对病例数据列表进行清洗和格式化
                    if (CollUtil.isNotEmpty(followData.getCaseExportDataDtoList())) {
                        followData.getCaseExportDataDtoList().forEach(this::cleanAndFormatCaseData);
                    }
                }
            } else {
                // CaseExportDataDto类型：直接清洗和格式化
                exportDataList.forEach(this::cleanAndFormatData);
            }
            
            log.info("数据处理完成，共处理 {} 条记录", exportDataList.size());
            
        } catch (Exception e) {
            log.error("处理导出数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("处理导出数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清洗和格式化数据
     */
    private void cleanAndFormatData(D exportData) {
        CaseExportDataDto data = (CaseExportDataDto) exportData;
        cleanAndFormatCaseData(data);
    }

    /**
     * 清洗和格式化病例数据
     */
    private void cleanAndFormatCaseData(CaseExportDataDto data) {
        // 清理空值和格式化文本
        if (StrUtil.isEmpty(data.getPatientName())) {
            data.setPatientName("");
        }
        
        if (StrUtil.isEmpty(data.getPatientSex())) {
            data.setPatientSex("");
        }
        
        // 格式化长文本，移除多余的空白字符
        if (StrUtil.isNotEmpty(data.getMedicalHistory())) {
            data.setMedicalHistory(cleanText(data.getMedicalHistory()));
        }
        
        if (StrUtil.isNotEmpty(data.getCaseAnalysis())) {
            data.setCaseAnalysis(cleanText(data.getCaseAnalysis()));
        }
        
        // 清理疾病名称和诊断信息
        if (StrUtil.isNotEmpty(data.getDiseaseName())) {
            data.setDiseaseName(cleanText(data.getDiseaseName()));
        }
        
        if (StrUtil.isNotEmpty(data.getDiagnosis())) {
            data.setDiagnosis(cleanText(data.getDiagnosis()));
        }
    }

    /**
     * 清理文本内容
     */
    private String cleanText(String text) {
        if (StrUtil.isEmpty(text)) {
            return "";
        }
        // 移除多余的空白字符，保留换行符
        return text.trim().replaceAll("[ \\t]+", " ");
    }

    /**
     * 生成导出文件
     */
    private String generateExportFile(List<D> exportDataList) {
        try {
            // 确保导出目录存在
            File exportDir = new File(tmpPath);
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }
            
            // 获取CaseExportUtil实例
            CaseExportUtil caseExportUtil = ContextHolder.getBean(CaseExportUtil.class);
            
            // 生成文件名
            String fileName = "病例导出_" + DateUtil.format(LocalDateTime.now(),
                    com.jusha.caselibrary.common.util.DateUtil.SHORT_PATTERN) + taskId + ".xlsx";
            String filePath = tmpPath + File.separator + taskId + "_" + fileName;
            
            String resultPath;
            
            if (isFollowCaseExportType() && !exportDataList.isEmpty()) {
                // FollowCaseExportDataDto类型：使用复合数据导出
                FollowCaseExportDataDto followData = (FollowCaseExportDataDto) exportDataList.get(0);
                resultPath = caseExportUtil.exportFollowCaseToExcel(followData, filePath);
            } else {
                // CaseExportDataDto类型：使用标准导出
                List<CaseExportDataDto> dataList = new ArrayList<>();
                for (D data : exportDataList) {
                    dataList.add((CaseExportDataDto) data);
                }
                resultPath = caseExportUtil.exportToExcel(dataList, filePath);
            }
            
            log.info("Excel文件生成成功: {}", resultPath);
            return resultPath;
            
        } catch (Exception e) {
            log.error("生成导出文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成导出文件失败: " + e.getMessage(), e);
        }
    }

    // 上传导出的文件
    private void uploadExportedFile(File file, String taskId, int exportCount) {
        try {
            FileServiceImpl fileService = ContextHolder.getBean(FileServiceImpl.class);
            // 确保文件写入完成
            try (FileOutputStream fos = new FileOutputStream(file, true)) {
                fos.getFD().sync();
            }
            try (InputStream fileInputStream = Files.newInputStream(Paths.get(file.getAbsolutePath()))) {
                // 创建MultipartFile
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                        MediaType.MULTIPART_FORM_DATA_VALUE, fileInputStream);

                // 上传文件
                FileUploadResp uploadResp = new FileUploadResp();
                uploadResp.setMultipartFile(multipartFile);
                uploadResp.setSeq(1);
                fileService.upload(uploadResp);

                // 更新任务状态
                // 更新任务进度信息，包含文件路径
                CaseExportProcessResp taskDto = new CaseExportProcessResp();
                taskDto.setTaskId(taskId);
                taskDto.setProgress(100);
                taskDto.setMessage(LocaleUtil.getLocale("export.task.complete.success"));
                taskDto.setFilePath(uploadResp.getFileUrl());
                taskDto.setFileName(uploadResp.getActualName());
                taskDto.setExportCount(exportCount);
                taskDto.setCreateTime(LocalDateTime.now());

                String taskDtoJson = JSON.toJSONString(taskDto);
                ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                        Constant.TASK_OVER_TIME, TimeUnit.MINUTES);

                log.info("导出结果保存成功，任务ID: {}", taskId);
            }
        }  catch (Exception e) {
            log.error("上传文件失败：{}", e.getMessage(), e);
        } finally {
            // 删除临时文件
            if (file.exists()) {
                FileUtil.delFile(file);
            }
        }
    }

    /**
     * 更新导出进度
     */
    private void updateProgress(int progress, String message) {
        try {
            CaseExportProcessResp taskDto = new CaseExportProcessResp();
            taskDto.setTaskId(taskId);
            taskDto.setProgress(progress);
            taskDto.setMessage(message);
            
            String taskDtoJson = JSON.toJSONString(taskDto);
            ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                    Constant.TASK_OVER_TIME, TimeUnit.MINUTES);
            
            log.debug("更新导出进度，任务ID: {}, 进度: {}%, 消息: {}", taskId, progress, message);
            
        } catch (Exception e) {
            log.error("更新导出进度失败: {}", e.getMessage(), e);
            // 这里不抛异常，避免影响主流程
        }
    }

    /**
     * 获取国际化消息
     */
    private String getMessage(String code, Object... args) {
        try {
            return ContextHolder.getBean(MessageSource.class).getMessage(code, args, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            log.warn("获取国际化消息失败, code: {}", code);
            // 返回code作为备用
            return code;
        }
    }
}
