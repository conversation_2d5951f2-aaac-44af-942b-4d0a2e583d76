package com.jusha.ris.docking.common.config;

import cn.hutool.core.net.NetUtil;
import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * 雪花ID生成配置
 */
@Slf4j
@Component
public class SnowIdConfig implements InitializingBean {

    @Override
    public void afterPropertiesSet() {
        Long workerId = 1L;
        try{
            String localhostStr = NetUtil.getLocalhostStr();
            workerId = NetUtil.ipv4ToLong(localhostStr);
        }
        catch (Exception e){
           log.warn("convert-to-workerId-error:", e);
        }

        IdGeneratorOptions options = new IdGeneratorOptions();
        options.Method = 1;                 //1表示雪花漂移，2表示传统雪花算法
        options.BaseTime = 1685548800000L;  //时间戳标准时间（对应时间：2023-06-01 00:00:00）
        options.WorkerIdBitLength = 6;      //机器码位长
        workerId = workerId % 64;
        options.WorkerId = workerId.shortValue();  //机器码
        options.SeqBitLength = 6;            //自增序列位长
        options.MinSeqNumber = 5;            //序列号最小值
        options.MaxSeqNumber = 0;            //序列号最大值（为0会被处理成：自增序列位长范围内的最大值）
        options.TopOverCostCount = 2000;     //最大漂移次数
        log.info("SnowIdConfig workerId:{}, shortValue:{}", workerId, workerId.shortValue());
        YitIdHelper.setIdGenerator(options);
    }

}