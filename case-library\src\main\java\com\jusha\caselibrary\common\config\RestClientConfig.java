package com.jusha.caselibrary.common.config;

import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.search.service.ESSyncService;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.RestClients;
import org.springframework.data.elasticsearch.config.AbstractElasticsearchConfiguration;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

/**
 * @ClassName RestClientConfig
 * @Description ES RestClient 配置
 * <AUTHOR>
 * @Date 2025/7/7 13:44
 **/
@Configuration
public class RestClientConfig {

    @Value("${ES_HOST}")
    private String servers;

    @Value("${ES_PORT}")
    private int port;

    @Value("${ES_USER}")
    private String user;

    @Value("${ES_PWD}")
    private String pwd;
    @Bean
    public RestHighLevelClient elasticsearchClient() {
        ClientConfiguration clientConfiguration = ClientConfiguration.builder()
                .connectedTo(servers + ":" + port)
                .withBasicAuth(user, pwd)
                .build();
        return RestClients.create(clientConfiguration).rest();
    }

    @Bean
    public ElasticsearchRestTemplate elasticsearchTemplate() {
        return new ElasticsearchRestTemplate(elasticsearchClient());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        ESSyncService esSyncService = ContextHolder.getBean(ESSyncService.class);
        // 应用完全启动后确保索引存在
        esSyncService.ensureIndexesExistOrCreate();
    }
}