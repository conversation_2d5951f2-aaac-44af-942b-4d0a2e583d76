package com.jusha.caselibrary.sickcase.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.DictConvertUtil;
import com.jusha.caselibrary.sickcase.dto.FollowInfoDto;
import com.jusha.caselibrary.sickcase.dto.StudyInfoDto;
import com.jusha.caselibrary.sickcase.dto.TagInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName PersonalCaseDetailResp
 * @Description 个人病例详情返回实体类
 * <AUTHOR>
 * @Date 2025/7/10 10:36
 **/
@Data
public class PersonalCaseDetailResp {

    @ApiModelProperty(value = "个人病例Id")
    private Long userCaseId;

    @ApiModelProperty(value = "病例Id")
    private Long caseId;

    @ApiModelProperty(value = "病例名称")
    private String caseName;

    @ApiModelProperty(value = "病例编号")
    private String caseNo;

    @ApiModelProperty(value = "疾病Id")
    private Long diseaseId;

    @ApiModelProperty(value = "最终诊断(疾病名称)")
    private String diagnosis;

    @ApiModelProperty(value = "患者Id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "患者性别")
    private String patientSex;

    @ApiModelProperty(value = "患者生日")
    private String patientBirthDate;

    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ApiModelProperty(value = "患者病史")
    private String medicalHistory;

    @ApiModelProperty(value = "难度等级")
    private String difficulty;

    @ApiModelProperty(value = "难度等级标签")
    private String difficultyLabel;

    @ApiModelProperty(value = "是否典型")
    private String caseCategory;

    @ApiModelProperty(value = "是否典型标签")
    private String caseCategoryLabel;

    @ApiModelProperty(value = "征象")
    private String sign;

    @ApiModelProperty(value = "病例分析")
    private String caseAnalysis;

    @ApiModelProperty(value = "来源")
    private String sourceType;

    @ApiModelProperty(value = "来源标签")
    private String sourceTypeLabel;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @ApiModelProperty(value = "随访状态标签")
    private String followStatusLabel;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DateUtil.DEFAULT_PATTERN)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "引用晨会列表")
    private String meetings;

    @ApiModelProperty(value = "定性匹配")
    private String qualityMatch;

    @ApiModelProperty(value = "定性匹配标签")
    private String qualityMatchLabel;

    @ApiModelProperty(value = "定位匹配")
    private String positionMatch;

    @ApiModelProperty(value = "定位匹配标签")
    private String positionMatchLabel;

    // 关联的疾病信息
    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    // 关联的目录信息
    @ApiModelProperty(value = "目录名称")
    private String catalogName;

    // 关联的检查信息
    @ApiModelProperty(value = "病例报告")
    private List<StudyInfoDto> studyInfoDtoList;

    // 关联的标签信息
    @ApiModelProperty(value = "病例标签列表")
    private List<TagInfoDto> tagInfoDtoList;

    // 关联的随访信息
    @ApiModelProperty(value = "随访信息")
    private List<FollowInfoDto> followInfoDtoList;

    /**
     * 设置字典标签值
     */
    public void setDictLabels() {
        this.difficultyLabel = DictConvertUtil.convertDifficulty(this.difficulty);
        this.caseCategoryLabel = DictConvertUtil.convertCaseCategory(this.caseCategory);
        this.sourceTypeLabel = DictConvertUtil.convertSourceType(this.sourceType);
        this.followStatusLabel = DictConvertUtil.convertFollowStatus(this.followStatus);
        this.qualityMatchLabel = DictConvertUtil.convertQualityMatch(this.qualityMatch);
        this.positionMatchLabel = DictConvertUtil.convertPositionMatch(this.positionMatch);

        // 处理StudyInfoDto中的字典字段
        if (this.studyInfoDtoList != null) {
            this.studyInfoDtoList.forEach(study -> {
                study.setPatientTypeLabel(DictConvertUtil.convertPatientType(study.getPatientType()));
                study.setStudyStateLabel(DictConvertUtil.convertStudyState(study.getStudyState()));
                study.setIsPostiveLabel(DictConvertUtil.convertIsPositive(study.getIsPostive()));
                study.setIsPublicLabel(DictConvertUtil.convertIsPublic(study.getIsPublic()));
            });
        }

        // 处理FollowInfoDto中的字典字段
        if (this.followInfoDtoList != null) {
            this.followInfoDtoList.forEach(follow -> {
                follow.setFollowTypeLabel(DictConvertUtil.convertFollowType(follow.getFollowType()));
            });
        }
    }
}
