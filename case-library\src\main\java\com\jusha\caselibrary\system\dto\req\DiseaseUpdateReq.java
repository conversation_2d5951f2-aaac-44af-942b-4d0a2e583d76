package com.jusha.caselibrary.system.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 疾病分类表
 * <AUTHOR>
 */
@Data
public class DiseaseUpdateReq {

    @ApiModelProperty(value = "疾病id")
    private Long diseaseId;

    @ApiModelProperty(value = "父级id")
    private Long parentId;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    @ApiModelProperty(value = "排序")
    private Integer orderNum;
}
