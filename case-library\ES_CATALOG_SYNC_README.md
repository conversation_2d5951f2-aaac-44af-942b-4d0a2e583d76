# ES目录同步机制说明

## 概述

本文档描述了个人病例库与Elasticsearch之间的目录同步机制，重点说明重新设计后的"目录下操作"理念、移除userId依赖的设计以及批量操作的实现细节。

## 设计理念

### 核心设计原则

#### 1. "每个人的目录ID都是唯一的"
新的设计完全基于catalogId进行管理，移除了对userId的依赖：
- **catalogId唯一性**：每个用户的目录ID在系统中都是唯一的
- **无需userId**：所有操作只需要catalogId，不再需要传递userId
- **简化参数**：API接口和内部方法都简化为只使用catalogId

#### 2. "目录下操作"概念
强调个人病例的创建和删除都是在特定目录下进行的操作：
- **在目录下创建病例**：病例创建时就明确其所属目录
- **从目录删除病例**：删除操作针对特定目录下的病例
- **目录关联智能管理**：系统自动维护病例与目录的关联关系

#### 3. 批量操作支持
支持高效的批量操作，提升系统性能：
- **批量创建**：一次性将多个病例添加到同一目录
- **批量删除**：一次性从目录中删除多个病例
- **批量同步**：ES同步支持批量操作的处理

### 操作类型重新设计

系统现在基于原有的CREATE和DELETE操作类型，移除了CATALOG_ADD和CATALOG_REMOVE操作，并新增了批量操作支持：

1. **CREATE** - 创建操作
   - 用于在指定目录下创建个人病例
   - 触发时机：调用`createPersonalCaseInCatalog`方法
   - ES同步：创建文档并设置catalogIds字段
   - 参数：只需要caseId和catalogId

2. **BATCH_CREATE** - 批量创建操作
   - 用于批量在指定目录下创建多个个人病例
   - 触发时机：调用`batchCreatePersonalCaseInCatalog`方法
   - ES同步：批量创建文档并设置catalogIds字段
   - 参数：只需要caseIds列表和catalogId

3. **UPDATE** - 更新操作
   - 用于更新ES中已存在的个人病例信息
   - 触发时机：修改个人病例的基本信息
   - ES同步：更新文档内容

4. **DELETE** - 删除操作
   - 用于从指定目录删除个人病例
   - 触发时机：调用`deletePersonalCaseFromCatalog`方法
   - ES同步：更新catalogIds字段或删除整个文档
   - 参数：只需要caseId和catalogId

5. **BATCH_DELETE** - 批量删除操作
   - 用于批量从指定目录删除多个个人病例
   - 触发时机：调用`batchDeletePersonalCaseFromCatalog`方法
   - ES同步：批量更新catalogIds字段或删除文档
   - 参数：只需要caseIds列表和catalogId

## 同步机制

### catalogIds字段智能管理

个人病例文档中的`catalogIds`字段通过智能算法自动维护：

```java
// CREATE操作：智能设置catalogIds
public void handlePersonalCaseCreate(Long caseId) {
    // 1. 查询该病例关联的所有目录
    List<Long> catalogIds = userCaseCatalogService.getCatalogIdsByCaseId(caseId);
    
    // 2. 更新ES文档的catalogIds字段
    personalCaseDocument.setCatalogIds(catalogIds);
    elasticsearchTemplate.save(personalCaseDocument);
}

// DELETE操作：智能更新catalogIds
public void handlePersonalCaseDelete(Long caseId) {
    // 1. 查询删除后剩余的目录关联
    List<Long> remainingCatalogIds = userCaseCatalogService.getCatalogIdsByCaseId(caseId);
    
    // 2. 如果没有剩余关联，删除整个文档
    if (remainingCatalogIds.isEmpty()) {
        elasticsearchTemplate.delete(caseId, PersonalCaseDocument.class);
    } else {
        // 3. 否则更新catalogIds字段
        personalCaseDocument.setCatalogIds(remainingCatalogIds);
        elasticsearchTemplate.save(personalCaseDocument);
    }
}
```

### 同步流程

1. **AOP切面拦截**
   - 通过`@ESSync`注解标记需要同步的方法
   - `ESSyncAspect`切面自动拦截并处理同步逻辑

2. **操作类型识别**
   - 基于方法名自动识别为CREATE或DELETE操作
   - `createPersonalCaseInCatalog` → CREATE操作
   - `deletePersonalCaseFromCatalog` → DELETE操作

3. **catalogIds智能计算**
   - 查询`user_case_catalog`关联表获取最新的目录关联
   - 自动计算并设置ES文档的catalogIds字段

4. **ES索引更新**
   - 根据catalogIds是否为空决定创建/更新/删除文档
   - 确保ES数据与数据库关联表保持一致

## 数据库架构

### 关联表设计

系统使用`user_case_catalog`关联表管理个人病例与目录的多对多关系：

```sql
CREATE TABLE user_case_catalog (
    id BIGINT PRIMARY KEY,
    case_id BIGINT NOT NULL COMMENT '病例ID',
    catalog_id BIGINT NOT NULL COMMENT '目录ID',
    create_time DATETIME COMMENT '创建时间',
    update_time DATETIME COMMENT '更新时间',
    UNIQUE KEY uk_case_catalog (case_id, catalog_id)
);
```

### 实体类设计

```java
// UserCase实体不包含catalogIds字段
@Entity
public class UserCase {
    private Long id;
    private String title;
    private String content;
    // 不包含catalogIds字段，通过关联表管理
}

// UserCaseCatalog关联实体
@Entity
public class UserCaseCatalog {
    private Long id;
    private Long caseId;
    private Long catalogId;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
```

## 实现细节

### 核心服务方法

```java
@Service
public class CaseManagementServiceImpl implements CaseManagementService {
    
    /**
     * 在指定目录下创建个人病例
     * 体现"目录下操作"的设计理念
     */
    @ESSync(operation = Constant.ES_OPERATION_CREATE)
    @Override
    public boolean createPersonalCaseInCatalog(Long caseId, Long catalogId) {
        // 1. 参数校验
        if (caseId == null || catalogId == null) {
            throw new IllegalArgumentException("病例ID和目录ID不能为空");
        }
        
        // 2. 检查关联是否已存在
        long existCount = userCaseCatalogService.lambdaQuery()
            .eq(UserCaseCatalog::getCaseId, caseId)
            .eq(UserCaseCatalog::getCatalogId, catalogId)
            .count();
            
        if (existCount > 0) {
            return true; // 关联已存在，返回成功
        }
        
        // 3. 创建新的关联记录
        UserCaseCatalog relation = new UserCaseCatalog();
        relation.setCaseId(caseId);
        relation.setCatalogId(catalogId);
        relation.setCreateTime(LocalDateTime.now());
        relation.setUpdateTime(LocalDateTime.now());
        
        return userCaseCatalogService.save(relation);
    }
    
    /**
     * 从指定目录删除个人病例
     * 体现"目录下操作"的设计理念
     */
    @ESSync(operation = Constant.ES_OPERATION_DELETE)
    @Override
    public boolean deletePersonalCaseFromCatalog(Long caseId, Long catalogId) {
        // 1. 参数校验
        if (caseId == null || catalogId == null) {
            throw new IllegalArgumentException("病例ID和目录ID不能为空");
        }
        
        // 2. 删除关联记录
        return userCaseCatalogService.lambdaUpdate()
            .eq(UserCaseCatalog::getCaseId, caseId)
            .eq(UserCaseCatalog::getCatalogId, catalogId)
            .remove();
    }
}
```

### ES同步服务

```java
@Service
public class ESSyncServiceImpl implements ESSyncService {
    
    /**
     * 处理个人病例CREATE操作的ES同步
     */
    public void handlePersonalCaseCreateOperation(Long caseId) {
        try {
            // 1. 查询病例基本信息
            UserCase userCase = userCaseMapper.selectById(caseId);
            if (userCase == null) {
                log.warn("病例不存在，跳过ES同步: caseId={}", caseId);
                return;
            }
            
            // 2. 智能获取catalogIds
            List<Long> catalogIds = userCaseCatalogService.lambdaQuery()
                .eq(UserCaseCatalog::getCaseId, caseId)
                .list()
                .stream()
                .map(UserCaseCatalog::getCatalogId)
                .collect(Collectors.toList());
            
            // 3. 构建ES文档
            PersonalCaseDocument document = convertToDocument(userCase);
            document.setCatalogIds(catalogIds);
            
            // 4. 保存到ES
            elasticsearchTemplate.save(document);
            
            log.info("个人病例CREATE操作ES同步成功: caseId={}, catalogIds={}", caseId, catalogIds);
            
        } catch (Exception e) {
            log.error("个人病例CREATE操作ES同步失败: caseId={}", caseId, e);
            throw new RuntimeException("ES同步失败", e);
        }
    }
    
    /**
     * 处理个人病例DELETE操作的ES同步
     */
    public void handlePersonalCaseDeleteOperation(Long caseId) {
        try {
            // 1. 查询删除后剩余的目录关联
            List<Long> remainingCatalogIds = userCaseCatalogService.lambdaQuery()
                .eq(UserCaseCatalog::getCaseId, caseId)
                .list()
                .stream()
                .map(UserCaseCatalog::getCatalogId)
                .collect(Collectors.toList());
            
            // 2. 智能决策：删除文档还是更新catalogIds
            if (remainingCatalogIds.isEmpty()) {
                // 没有剩余关联，删除整个ES文档
                elasticsearchTemplate.delete(caseId.toString(), PersonalCaseDocument.class);
                log.info("个人病例完全删除: caseId={}", caseId);
            } else {
                // 有剩余关联，更新ES文档的catalogIds字段
                PersonalCaseDocument document = elasticsearchTemplate.get(caseId.toString(), PersonalCaseDocument.class);
                if (document != null) {
                    document.setCatalogIds(remainingCatalogIds);
                    elasticsearchTemplate.save(document);
                    log.info("个人病例catalogIds更新: caseId={}, remainingCatalogIds={}", caseId, remainingCatalogIds);
                }
            }
            
        } catch (Exception e) {
            log.error("个人病例DELETE操作ES同步失败: caseId={}", caseId, e);
            throw new RuntimeException("ES同步失败", e);
        }
    }
}
```

## API设计

### RESTful接口

```java
@RestController
@RequestMapping("/personal/case")
public class PersonalCaseController {
    
    /**
     * 在目录下创建病例
     * 体现"目录下操作"的设计理念和移除userId依赖
     */
    @PostMapping("/create-in-catalog")
    public Result<Boolean> createInCatalog(@RequestBody @Valid CatalogOperationRequest request) {
        try {
            // 注意：只需要caseId和catalogId，不需要userId
            boolean success = caseManagementService.createPersonalCaseInCatalog(
                request.getCaseId(),
                request.getCatalogId()
            );
            return Result.success(success);
        } catch (Exception e) {
            log.error("在目录下创建病例失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 从目录删除病例
     * 体现"目录下操作"的设计理念和移除userId依赖
     */
    @DeleteMapping("/delete-from-catalog")
    public Result<Boolean> deleteFromCatalog(@RequestBody @Valid CatalogOperationRequest request) {
        try {
            // 注意：只需要caseId和catalogId，不需要userId
            boolean success = caseManagementService.deletePersonalCaseFromCatalog(
                request.getCaseId(),
                request.getCatalogId()
            );
            return Result.success(success);
        } catch (Exception e) {
            log.error("从目录删除病例失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量在目录下创建病例
     * 支持高效的批量操作
     */
    @PostMapping("/batch-create-in-catalog")
    public Result<Boolean> batchCreateInCatalog(@RequestBody @Valid BatchCatalogOperationRequest request) {
        try {
            // 批量操作：多个caseIds + 一个catalogId
            boolean success = caseManagementService.batchCreatePersonalCaseInCatalog(
                request.getCaseIds(),
                request.getCatalogId()
            );
            return Result.success(success);
        } catch (Exception e) {
            log.error("批量在目录下创建病例失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量从目录删除病例
     * 支持高效的批量操作
     */
    @DeleteMapping("/batch-delete-from-catalog")
    public Result<Boolean> batchDeleteFromCatalog(@RequestBody @Valid BatchCatalogOperationRequest request) {
        try {
            // 批量操作：多个caseIds + 一个catalogId
            boolean success = caseManagementService.batchDeletePersonalCaseFromCatalog(
                request.getCaseIds(),
                request.getCatalogId()
            );
            return Result.success(success);
        } catch (Exception e) {
            log.error("批量从目录删除病例失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
}
```

### 请求实体设计

```java
// 单个操作请求实体
@Data
public class CatalogOperationRequest {
    @NotNull(message = "病例ID不能为空")
    private Long caseId;
    
    @NotNull(message = "目录ID不能为空")
    private Long catalogId;
    
    // 注意：移除了userId字段，体现"每个人的目录ID都是唯一的"设计理念
}

// 批量操作请求实体
@Data
public class BatchCatalogOperationRequest {
    @NotEmpty(message = "病例ID列表不能为空")
    private List<Long> caseIds;
    
    @NotNull(message = "目录ID不能为空")
    private Long catalogId;
    
    // 注意：同样移除了userId字段
}
```

## 配置说明

### ES索引配置

- **索引名称**：`personal_case`
- **文档类型**：`_doc`
- **主键字段**：`id`（对应数据库中的case_id）
- **catalogIds字段**：数组类型，存储该病例所属的所有目录ID

### 同步配置

- **智能同步**：基于数据库关联表自动计算catalogIds
- **异步处理**：支持异步同步以提高性能
- **错误重试**：内置重试机制处理同步失败
- **日志记录**：详细记录同步操作和结果

## 优势特点

### 设计优势

1. **概念清晰**：明确"目录下操作"的业务语义
2. **操作简化**：移除复杂的CATALOG操作类型和userId依赖
3. **智能管理**：自动维护catalogIds字段
4. **数据一致**：确保ES与数据库的一致性
5. **参数简化**：基于"每个人的目录ID都是唯一的"原则，简化API参数
6. **批量支持**：提供高效的批量操作能力

### 技术优势

1. **架构简洁**：基于现有CREATE/DELETE操作，移除userId依赖
2. **扩展性好**：易于添加新的目录操作功能
3. **维护性强**：减少了操作类型和参数的复杂性
4. **性能优化**：智能决策减少不必要的ES操作，批量操作提升效率
5. **接口统一**：所有操作都基于catalogId，接口设计更加统一
6. **测试友好**：简化的参数结构使测试更容易编写和维护

### 业务优势

1. **用户体验**：批量操作提升用户操作效率
2. **系统性能**：减少网络请求次数，提升系统响应速度
3. **数据安全**：基于catalogId的唯一性保证数据隔离
4. **运维简化**：移除userId依赖减少了配置和维护复杂度

## 注意事项

1. **数据一致性**：确保数据库操作成功后才进行ES同步
2. **并发控制**：注意多线程环境下的数据一致性，特别是批量操作
3. **异常处理**：妥善处理同步失败的情况，批量操作需要考虑部分失败的场景
4. **性能监控**：监控catalogIds计算和ES同步的性能，关注批量操作的性能表现
5. **catalogId唯一性**：确保catalogId在系统中的唯一性，这是移除userId依赖的基础
6. **批量操作限制**：考虑设置合理的批量操作大小限制，避免单次操作过大影响系统性能
7. **向后兼容**：如果有旧的API仍在使用userId，需要提供兼容性支持或迁移方案

## 故障排查

### 常见问题

1. **catalogIds不准确**：检查关联表数据和查询逻辑
2. **同步延迟**：检查ES集群状态和网络连接
3. **数据不一致**：验证AOP切面是否正确拦截
4. **批量操作失败**：检查批量操作的事务处理和错误处理逻辑
5. **catalogId冲突**：验证catalogId的唯一性约束
6. **参数缺失**：检查是否正确移除了userId相关参数

### 调试方法

1. 启用详细日志记录，特别关注批量操作的日志
2. 使用ES查询验证catalogIds字段
3. 检查`user_case_catalog`关联表的数据完整性
4. 验证"目录下操作"的业务逻辑正确性
5. 测试批量操作的边界情况（空列表、大批量等）
6. 验证移除userId后的API调用是否正常
7. 检查catalogId的唯一性和正确性

### 性能调优

1. **批量大小优化**：根据系统性能调整批量操作的最佳大小
2. **ES批量索引**：使用ES的bulk API提升批量同步性能
3. **数据库批量操作**：优化数据库的批量插入和删除操作
4. **缓存策略**：考虑对catalogId相关数据进行适当缓存
5. **监控指标**：建立批量操作的性能监控指标

## 迁移指南

### 从userId依赖迁移到catalogId

1. **API更新**：更新所有API接口，移除userId参数
2. **客户端更新**：更新前端和其他客户端代码
3. **测试验证**：确保所有功能在移除userId后正常工作
4. **数据验证**：验证catalogId的唯一性和正确性
5. **性能测试**：验证批量操作的性能表现

### 批量操作集成

1. **接口集成**：集成新的批量操作API
2. **前端适配**：更新前端界面支持批量选择和操作
3. **性能测试**：测试批量操作在不同数据量下的性能
4. **错误处理**：完善批量操作的错误处理和用户反馈