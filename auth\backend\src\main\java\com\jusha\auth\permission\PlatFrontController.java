package com.jusha.auth.permission;

import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.mybatisplus.entity.SysPlat;
import com.jusha.auth.system.service.ISysGroupService;
import com.jusha.auth.system.service.ISysMenuService;
import com.jusha.auth.system.service.ISysPlatService;
import com.jusha.auth.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 业务平台信息Controller
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
@RestController
@RequestMapping("/plat")
public class PlatFrontController extends BaseController {
    @Autowired
    private ISysPlatService isysPlatService;

    @Autowired
    private ISysGroupService iSysGroupService;

    @Autowired
    private ISysMenuService iSysMenuService;

    @Autowired
    private ISysRoleService iSysRoleService;

    /**
     * 查询业务平台信息列表
     */
    @GetMapping("/list")
    public ResultBean list(SysPlat sysPlat) {
        sysPlat.setVisible(Constants.NORMAL);
        List<SysPlat> list = isysPlatService.selectSysPlatList(sysPlat);
        return ResultBean.success(list);
    }

}
