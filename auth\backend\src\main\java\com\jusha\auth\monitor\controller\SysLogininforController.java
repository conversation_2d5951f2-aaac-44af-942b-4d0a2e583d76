package com.jusha.auth.monitor.controller;

import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.core.service.SysPasswordService;
import com.jusha.auth.monitor.service.ISysLogininforService;
import com.jusha.auth.mybatisplus.entity.SysLogininfor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统访问记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController extends BaseController {
    @Autowired
    private ISysLogininforService logininforService;

    @Autowired
    private SysPasswordService passwordService;

    @HasPermissions
    @GetMapping("/list")
    public TableDataInfo list(SysLogininfor logininfor) {
        startPage();
        List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
        return getDataTable(list);
    }

    @HasPermissions
    @PostMapping("/remove")
    public ResultBean remove(@RequestParam Long[] infoIds) {
        return resultBean(logininforService.deleteLogininforByIds(infoIds));
    }

    @HasPermissions
    @PostMapping("/clean")
    public ResultBean clean() {
        logininforService.cleanLogininfor();
        return success();
    }

    @HasPermissions
    @PostMapping("/unlock")
    public ResultBean unlock(@RequestParam String userName) {
        passwordService.clearLoginRecordCache(userName);
        return success();
    }

    /**
     * 首页用户登录统计
     *
     * @return
     */
    @GetMapping("/loginStatistics")
    public ResultBean loginStatistics() {
        return ResultBean.success(logininforService.loginStatistics());
    }
}