package com.jusha.caselibrary.common.aop;

import com.alibaba.druid.sql.visitor.functions.Concat;
import com.jusha.caselibrary.common.constant.Constant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName ESSync
 * @Description ES同步注解
 *  * 用于标记需要同步到ES的方法
 * <AUTHOR>
 * @Date 2025/7/7 15:17
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ESSync {

    /**
     * 同步类型
     */
    SyncType type() default SyncType.UPDATE;

    /**
     * 索引类型（department/personal）
     */
    String indexType() default "";

    /**
     * 索引名称
     */
    String index() default "";

    /**
     * 文档ID字段名(caseId|userCaseId)
     */
    String idField() default Constant.ES_INDEX_ID;

    /**
     * 是否异步同步
     */
    boolean async() default true;

    /**
     * 同步延迟时间（毫秒）
     */
    long delay() default 0;

    /**
     * 同步类型枚举
     */
    enum SyncType {
        /**
         * 新增
         */
        CREATE,
        /**
         * 更新
         */
        UPDATE,
        /**
         * 删除
         */
        DELETE,
    }
}