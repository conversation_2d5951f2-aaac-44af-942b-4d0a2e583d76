package com.jusha.caselibrary.sickcase.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.mybatisplus.service.DepCaseService;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.mapper.DeptCaseMapper;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName DeptCaseServiceImpl
 * @Description 科室病例服务实现类
 * <AUTHOR>
 * @Date 2025/7/10 09:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptCaseServiceImpl implements DeptCaseService {

    private final DeptCaseMapper deptCaseMapper;

    private final DepCaseService depCaseService;

    @Override
    public List<DeptCaseDetailResp> getDeptCaseDetailList(DeptCaseSearchReq req) {
        List<DeptCaseDetailResp> list = deptCaseMapper.getDeptCaseDetailList(req);
        // 转换字典值为标签
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(DeptCaseDetailResp::setDictLabels);
        }
        return list;
    }

    @Override
    public PageInfo<DeptCaseDetailResp> getDeptCaseDetailPage(DeptCaseSearchReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<DeptCaseDetailResp> deptCaseDetailList = deptCaseMapper.getDeptCaseDetailList(req);
        if (CollectionUtils.isNotEmpty(deptCaseDetailList)) {
            // 转换字典值为标签
            deptCaseDetailList.forEach(DeptCaseDetailResp::setDictLabels);
            return new PageInfo<>(deptCaseDetailList);
        }
        return new PageInfo<>(Collections.emptyList());
    }

    @Override
    public DeptCaseDetailResp getDeptCaseDetail(Long caseId) {
        DeptCaseSearchReq deptCaseSearchReq = new DeptCaseSearchReq();
        deptCaseSearchReq.setCaseId(caseId);
        List<DeptCaseDetailResp> deptCaseDetailList = deptCaseMapper.getDeptCaseDetailList(deptCaseSearchReq);
        if (CollectionUtils.isNotEmpty(deptCaseDetailList)) {
            DeptCaseDetailResp result = deptCaseDetailList.get(0);
            // 转换字典值为标签
            result.setDictLabels();
            return result;
        }
        return new DeptCaseDetailResp();
    }

    @Override
    public void deleteDeptCaseById(Long caseId) {
        depCaseService.removeById(caseId);
    }
}
