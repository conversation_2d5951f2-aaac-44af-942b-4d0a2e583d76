package com.jusha.ris.docking.mock.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title StudyInfoReq
 * @description
 * @date 2025/3/14
 */
@Data
public class StudyInfoReq {

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDate;

    @ApiModelProperty("患者性别")
    private Integer patientSex;

    @ApiModelProperty("身份证号")
    @NotEmpty(message = "身份证号不能为空")
    private String idCard;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("唯一患者编号")
    private String patientNo;

    @ApiModelProperty("申请单号")
    @NotBlank(message = "申请单号不能为空")
    private String applyNo;

    @ApiModelProperty("患者类型")
    private String patientType;

    @ApiModelProperty("门诊/住院号")
    private String outInPatientNo;

    @ApiModelProperty("患者年龄")
    private String patientAge;

    @ApiModelProperty("检查类型")
    private String studyType;

    @ApiModelProperty("检查部位")
    private String studyParts;

    @ApiModelProperty("检查项目")
    private String studyItems;

    @ApiModelProperty("申请科室")
    private String applyDepartment;

    @ApiModelProperty("申请医生")
    private String applyDoctor;

    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    @ApiModelProperty("检查目的")
    private String studyPurpose;

    @ApiModelProperty("体征")
    private String physicalSign;

    @ApiModelProperty("患者主诉")
    private String selfComplaints;

    @ApiModelProperty("病史")
    private String historyDisease;

    @ApiModelProperty("临床诊断")
    private String clinicalDiagnosis;

    @ApiModelProperty("唯一检查号")
    @NotBlank(message = "唯一检查号不能为空")
    private String studyNo;

    @ApiModelProperty("影像号")
    private String accessNumber;

    @ApiModelProperty("检查uid")
    private String studyUid;

    @ApiModelProperty("检查设备")
    private String studyDevice;

    @ApiModelProperty("检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "检查时间不能为空")
    private LocalDateTime studyTime;

    @ApiModelProperty("检查技师")
    private String artificer;

}
