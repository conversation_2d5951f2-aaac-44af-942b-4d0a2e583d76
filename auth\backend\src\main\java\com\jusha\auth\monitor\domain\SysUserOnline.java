package com.jusha.auth.monitor.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * 当前在线会话
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class SysUserOnline {

    private long userId;

    /** 会话编号 */
    private String tokenId;

    /** 账户名 */
    private String userName;

    /** 登录IP地址 */
    private String ipaddr;

    /** 登录地址 */
    private String loginLocation;

    /** 浏览器类型 */
    private String browser;

    /** 操作系统 */
    private String os;

    /** 登录时间 */
    private Long loginTime;
}
