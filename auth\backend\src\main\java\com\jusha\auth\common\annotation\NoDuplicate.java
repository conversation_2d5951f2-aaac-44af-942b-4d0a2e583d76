package com.jusha.auth.common.annotation;

import java.lang.annotation.*;

/**
 * 用于标识不允许(用相同参数)重复请求的方法
 * ps：1、使用场景示例：1-直接返回业务异常  2-等待、尝试重新获取、直到获取到锁(或者超时)
 *     2、对私有方法不生效
 *     3、因为是切面，所以要通过代理对象调用
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface NoDuplicate {

    /**
     * 锁的key（若多个则用"_"拼接）
     */
    String[] keys() default {};

    /**
     * 锁的超时时间（秒）
     */
    int expireSeconds() default 30;


    /**
     * 当获取不到锁时：1-直接返回业务异常  2-等待、尝试重新获取、直到获取到锁(或者超时)
     * @return
     */
    int waitTag() default 1;

    /**
     * 等待、尝试重新获取时：超时时间（秒）
     */
    int waitExpire() default 30;

}