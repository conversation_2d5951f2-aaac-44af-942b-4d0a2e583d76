package com.jusha.auth.common.utils.aes;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.util.Arrays;

/**
 * AES解密
 */
public class AESDecrypt {
    public static byte[] generateMD5Hash(String key) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        return md.digest(key.getBytes("UTF-8"));
    }

    public static byte[] AESDecryptBytes(byte[] content, String key) throws Exception {
        byte[] keyBytes = generateMD5Hash(key);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        int nonceSize = 12; // GCM Nonce size is 12 bytes
        byte[] nonce = Arrays.copyOfRange(content, 0, nonceSize);
        byte[] cipherText = Arrays.copyOfRange(content, nonceSize, content.length);
        GCMParameterSpec gcmSpec = new GCMParameterSpec(128, nonce);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmSpec);
        return cipher.doFinal(cipherText);
    }
}