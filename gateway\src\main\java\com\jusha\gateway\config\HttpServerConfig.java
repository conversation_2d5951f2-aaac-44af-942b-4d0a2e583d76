package com.jusha.gateway.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory;
import org.springframework.boot.web.server.WebServer;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.server.reactive.HttpHandler;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * http配置
 */
@Configuration
public class HttpServerConfig {

    @Autowired
    private HttpHandler httpHandler;

    private WebServer webServer;

    @Value("${HTTP_PORT}")
    private int httpPort;


    @PostConstruct
    public void start() {
        NettyReactiveWebServerFactory factory = new NettyReactiveWebServerFactory(httpPort);
        webServer = factory.getWebServer(httpHandler);
        webServer.start();
    }

    @PreDestroy
    public void stop() {
        webServer.stop();
    }

}