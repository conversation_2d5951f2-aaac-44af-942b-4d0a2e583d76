import request from '@/utils/request'

// 查询部门列表
export function listGroup(query) {
  return request({
    url: '/system/group/list',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listGroupExcludeChild(groupId,platId) {
  return request({
    url: '/system/group/list/exclude?groupId=' + groupId+'&platId=' + platId,
    method: 'get'
  })
}

// 查询部门详细
export function getGroup(groupId) {
  return request({
    url: '/system/group/query?groupId=' + groupId,
    method: 'get'
  })
}

// 新增部门
export function addGroup(data) {
  return request({
    url: '/system/group/add',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateGroup(data) {
  return request({
    url: '/system/group/edit',
    method: 'post',
    data: data
  })
}

// 删除部门
export function delGroup(groupId) {
  return request({
    url: '/system/group/remove?groupId=' + groupId,
    method: 'post'
  })
}