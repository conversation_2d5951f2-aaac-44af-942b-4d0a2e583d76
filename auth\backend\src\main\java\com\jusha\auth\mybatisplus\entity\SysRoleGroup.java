package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.jeffreyning.mybatisplus.anno.MppMultiId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 角色和部门关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@Getter
@Setter
@TableName("sys_role_group")
@ApiModel(value = "SysRoleGroup对象", description = "角色和部门关联表")
public class SysRoleGroup {

    @ApiModelProperty(value = "角色ID")
    @TableField("role_id")
    @MppMultiId
    private Long roleId;

    @ApiModelProperty(value = "分组ID")
    @TableField("group_id")
    @MppMultiId
    private Long groupId;
}
