package com.jusha.ris.docking.common.util;

/**
 * 正则校验工具类
 */
public class RegUtil {

    /**
     * 身份证校验
     * 规则：身份证号(15位、18位数字)，最后一位是校验位，可能为数字或字符X
     */
    public static Boolean isIdCard(String idCard) {
        String reg = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)";
        return idCard.matches(reg);
    }

    /**
     * 手机号校验
     */
    public static Boolean isPhoneNumber(String phone) {
        String reg = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";
        return phone.matches(reg);
    }

    /**
     * 用户名校验
     * 规则：只包含 中文、英文、数字、·、圆括号
     */
    public static Boolean isUserName(String userName) {
        String reg = "^[a-z0-9A-Z\u4e00-\u9fa5·(（)）]+$";
        return userName.matches(reg);
    }

}
