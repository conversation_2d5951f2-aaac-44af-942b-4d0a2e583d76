package com.jusha.auth.system.service;

import com.jusha.auth.mybatisplus.entity.SysInterface;
import java.util.List;

/**
 * 接口Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-08
 */
public interface ISysInterfaceService {
    /**
     * 查询接口
     * 
     * @param interfaceId 接口主键
     * @return 接口
     */
    public SysInterface selectSysInterfaceByInterfaceId(Long interfaceId);

    /**
     * 查询接口列表
     * 
     * @param sysInterface 接口
     * @return 接口集合
     */
    public List<SysInterface> selectSysInterfaceList(SysInterface sysInterface);

    /**
     * 新增接口
     * 
     * @param sysInterface 接口
     * @return 结果
     */
    public boolean insertSysInterface(SysInterface sysInterface);

    /**
     * 修改接口
     * 
     * @param sysInterface 接口
     * @return 结果
     */
    public boolean updateSysInterface(SysInterface sysInterface);

    /**
     * 删除接口信息
     * 
     * @param interfaceId 接口主键
     * @return 结果
     */
    public boolean deleteSysInterfaceByInterfaceId(Long interfaceId);

    /**
     * 根据名称判断接口是否重复
     * @param sysInterface 接口
     * @return boolean value
     */
    public boolean checkInterfaceNameUnique(SysInterface sysInterface);

    /**
     * 根据路径判断接口是否重复
     * @param sysInterface 接口
     * @return boolean value
     */
    public boolean checkInterfacePathUnique(SysInterface sysInterface);

    /**
     * 判断这个接口是不是已经被绑定了，如果被绑定了，要返回绑定的接口名称
     * @param interfaceId 接口
     * @return String value
     */
    public String checkInterfaceMenu(Long interfaceId);

}
