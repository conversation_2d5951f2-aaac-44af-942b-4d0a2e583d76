package com.jusha.auth.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.page.PageUtils;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.core.service.SysPasswordService;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.exception.HttpStatus;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.RSAUtil;
import com.jusha.auth.common.utils.match.BeanIsNullUtil;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.utils.match.ValidatorUtil;
import com.jusha.auth.common.utils.poi.ImportTemplateUtils;
import com.jusha.auth.common.utils.poi.JeecgController;
import com.jusha.auth.common.utils.spring.SpringUtils;
import com.jusha.auth.monitor.service.ISysConfigService;
import com.jusha.auth.mybatisplus.entity.SysPlat;
import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.mybatisplus.entity.SysUserRole;
import com.jusha.auth.mybatisplus.service.SysPlatService;
import com.jusha.auth.mybatisplus.service.SysRoleService;
import com.jusha.auth.mybatisplus.service.SysUserRoleService;
import com.jusha.auth.mybatisplus.service.SysUserService;
import com.jusha.auth.system.domain.ForgetReset;
import com.jusha.auth.system.domain.ModifyUserPhone;
import com.jusha.auth.system.domain.PasswordReset;
import com.jusha.auth.system.domain.PasswordVerify;
import com.jusha.auth.system.domain.excel.SysUserExcelExportVo;
import com.jusha.auth.system.domain.excel.SysUserExcelImportVo;
import com.jusha.auth.system.mapper.ISysRoleMapper;
import com.jusha.auth.system.mapper.ISysUserMapper;
import com.jusha.auth.system.service.ISysRoleService;
import com.jusha.auth.system.service.ISysUserService;
import org.apache.commons.collections.CollectionUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.PrivateKey;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ISysUserServiceImpl implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(ISysUserServiceImpl.class);

    @Autowired
    private ISysRoleService iSysroleService;

    @Autowired
    private ISysUserMapper userMapper;

    @Autowired
    private ISysRoleMapper roleMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private SysPlatService sysPlatService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisCache redisCache;

    @Value("${token.network:}")
    private String network;

    @Value("${token.msgcode:}")
    private String msgCode;

    /**
     * 根据条件分页查询用户列表
     *
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectUserList(SysUser sysUser) {
        LambdaQueryChainWrapper<SysUser> wrapper = sysUserService.lambdaQuery();
        if (sysUser.getUserName() != null) {
            wrapper.like(SysUser::getUserName, sysUser.getUserName());
        }
        if (sysUser.getNickName() != null) {
            wrapper.like(SysUser::getNickName, sysUser.getNickName());
        }
        if (sysUser.getPhoneNumber() != null) {
            wrapper.like(SysUser::getPhoneNumber, sysUser.getPhoneNumber());
        }
        if (sysUser.getWorkNumber() != null) {
            wrapper.like(SysUser::getWorkNumber, sysUser.getWorkNumber());
        }
        if (sysUser.getStatus() != null) {
            wrapper.eq(SysUser::getStatus, sysUser.getStatus());
        }
        if (sysUser.getRoleId() != null) {
            List<SysUserRole> sysUserRoles = sysUserRoleService.lambdaQuery().eq(SysUserRole::getRoleId, sysUser.getRoleId()).list();
            if (!sysUserRoles.isEmpty()) {
                List<Long> userIds = sysUserRoles.stream().map(SysUserRole::getUserId).collect(Collectors.toList());
                wrapper.in(SysUser::getUserId, userIds);
            }else {
                return new ArrayList<>();
            }
        }
        wrapper.ne(SysUser::getUserId, Constants.ADMIN_ROLE_ID);
        PageUtils.startPage();
        return wrapper.orderByDesc(SysUser::getCreateTime).list();
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过电话号码查询用户
     *
     * @param phoneNumber 电话号码
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByPhoneNumber(String phoneNumber) {
        return userMapper.selectUserByPhoneNumber(phoneNumber);
    }

    /**
     * 通过工号查询用户
     *
     * @param workNumber 工号
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByWorkNumber(String workNumber) {
        return userMapper.selectUserByWorkNumber(workNumber);
    }
    
    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId,Long platId) {
        SysUser sysUser = userMapper.selectUserById(userId);
        if(sysUser == null) return null;

        List<SysRole> roles = sysUser.getRoles();
        if(CollectionUtils.isNotEmpty(roles)){
            List<SysRole> platRoles = roles.stream().filter(sysRole -> sysRole.getPlatId().equals(platId)).collect(Collectors.toList());
            sysUser.setRoles(platRoles);
        }
        return sysUser;
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 校验账户名是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? Constants.ALL_MINUS1L : user.getUserId();
        List<SysUser> userInfos = sysUserService.lambdaQuery().eq(SysUser::getUserName, user.getUserName()).list();
        if (!userInfos.isEmpty() && userInfos.get(0).getUserId().longValue() != userId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? Constants.ALL_MINUS1L : user.getUserId();
        List<SysUser> userInfos = sysUserService.lambdaQuery().eq(SysUser::getPhoneNumber, user.getPhoneNumber()).list();
        if (!userInfos.isEmpty() && userInfos.get(0).getUserId().longValue() != userId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 校验工号是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkWorkNumberUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? Constants.ALL_MINUS1L : user.getUserId();
        List<SysUser> userInfos = sysUserService.lambdaQuery().eq(SysUser::getWorkNumber, user.getWorkNumber()).list();
        if (!userInfos.isEmpty() && userInfos.get(0).getUserId().longValue() != userId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }


    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException(MessageUtils.message("user.dowith.admin.user"));
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public SysUser insertUser(SysUser user) {
        //雪花生成id
        long userId = YitIdHelper.nextId();
        user.setUserId(userId);
        //当前用户id作为创建者
        user.setCreateBy(tokenService.getUserIdCanNull());
        //当前时间作为创建时间
        user.setCreateTime(DateUtils.getNowDate());
        user.setPassword(SysPasswordService.encryptPassword(user.getPassword()));
        // 新增用户信息
        boolean saveRole = sysUserService.save(user);
        if (saveRole) {
            // 新增用户与角色管理
            insertUserRole(user);
        }
        return user;
    }

    /**
     * 修改用户信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateUser(SysUser sysUser) {
        // 删除用户与角色关联(前提是携带platId，携带这个字段才认为会改变所属角色)

        if(sysUser.getRoleIds()!=null){
            List<SysRole> roles = iSysroleService.selectRoleList(new SysRole(sysUser.getPlatId(),"0"));
            List<Long> roleIdList = roles.stream().map(SysRole::getRoleId).collect(Collectors.toList());
            sysUserRoleService.lambdaUpdate()
                    .eq(SysUserRole::getUserId, sysUser.getUserId())
                    .in(SysUserRole::getRoleId, roleIdList)
                    .remove();
            //新增用户与角色关联
            insertUserRole(sysUser);
        }

        // 修改用户信息
        sysUser.setUpdateTime(DateUtils.getNowDate());
        sysUser.setUpdateBy(sysUser.getUserId());
        return sysUserService.updateById(sysUser);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds,Long platId) {
        List<SysRole> roles = iSysroleService.selectRoleList(new SysRole(platId,"0"));
        List<Long> roleIdList = roles.stream().map(SysRole::getRoleId).collect(Collectors.toList());
        sysUserRoleService.lambdaUpdate()
                .eq(SysUserRole::getUserId, userId)
                .in(SysUserRole::getRoleId, roleIdList)
                .remove();
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户基本信息
     *
     * @param sysUser 用户信息
     * @return 结果
     */
    @Override
    public boolean updateUserProfile(SysUser sysUser) {
        // 修改用户信息
        sysUser.setUpdateTime(DateUtils.getNowDate());
        return sysUserService.updateById(sysUser);
    }

    /**
     * 重置用户密码
     *
     * @param passwordReset 密码
     * @return 结果
     */
    @Override
    public ResultBean resetUserPwd(PasswordReset passwordReset) throws Exception {
        String oldPassword = passwordReset.getOldPassword();
        String newPassword = passwordReset.getNewPassword();
        if (oldPassword.isEmpty()) {
            return ResultBean.error(MessageUtils.message("oldpassword.not.null"));
        }
        if (newPassword.isEmpty()){
            return ResultBean.error(MessageUtils.message("newPassword.not.null"));
        }
        PrivateKey privateKey = RSAUtil.loadPrivateKey(redisCache.getCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PRIVATE).toString());
        String password = tokenService.getLoginUser().getPassword();

        oldPassword = RSAUtil.decryptBase64WithPrivate(oldPassword.trim(), privateKey);
        if (!SysPasswordService.matchesPassword(oldPassword, password)) {
            return ResultBean.error(MessageUtils.message("oldPassword.is.wrong"));
        }

        newPassword = RSAUtil.decryptBase64WithPrivate(newPassword.trim(), privateKey);
        if (SysPasswordService.matchesPassword(newPassword, password)) {
            return ResultBean.error(MessageUtils.message("newPassword.oldpassword.cannot.same"));
        }
        newPassword = SysPasswordService.encryptPassword(newPassword);
        //验证一下验证码
//        validateCode(passwordReset.getCode(),null,Constants.SMS_MEG_TYPE_PASSWORD);

        SysUser sysUser = new SysUser();
        sysUser.setUserId(tokenService.getUserId());
        sysUser.setUpdateTime(DateUtils.getNowDate());
        sysUser.setUpdateBy(tokenService.getUserId());
        sysUser.setPassword(newPassword);
        if (sysUserService.updateById(sysUser)) {
            // 更新缓存用户密码
            SysUser user = tokenService.getLoginUser().getSysUser();
            user.setPassword(newPassword);
            LoginUser loginUser = tokenService.getLoginUser();
            loginUser.setSysUser(user);
            tokenService.setLoginUser(loginUser);

            Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + user.getUserId() + "*");
            if(!keys.isEmpty()){
                for (String key : keys) {
                    redisCache.deleteObject(key);
                }
            }
            return ResultBean.success();
        }
        return ResultBean.error(MessageUtils.message("edit.password.exception"));
    }

    @Override
    public ResultBean verifyPwd(PasswordVerify passwordVerify) throws Exception {
        String myPassword = passwordVerify.getMyPassword();
        if (myPassword.isEmpty()) {
            return ResultBean.error(MessageUtils.message("password.not.null"));
        }
        PrivateKey privateKey = RSAUtil.loadPrivateKey(redisCache.getCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PRIVATE).toString());
        String password = tokenService.getLoginUser().getPassword();

        myPassword = RSAUtil.decryptBase64WithPrivate(myPassword.trim(), privateKey);
        if (!SysPasswordService.matchesPassword(myPassword, password)) {
            return ResultBean.error(MessageUtils.message("password.is.wrong"));
        }
        return ResultBean.success();
    }

    @Override
    public ResultBean forgetPwd(ForgetReset forgetReset) throws Exception {
        String newPassword = forgetReset.getNewPassword();
        if (newPassword.isEmpty()){
            return ResultBean.error(MessageUtils.message("newPassword.not.null"));
        }
        PrivateKey privateKey = RSAUtil.loadPrivateKey(redisCache.getCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PRIVATE).toString());
        SysUser user = sysUserService.lambdaQuery().eq(SysUser::getPhoneNumber,forgetReset.getPhoneNumber()).one();
        if(user==null){
            throw new ServiceException(MessageUtils.message("userId.not.exist"));
        }
        newPassword = RSAUtil.decryptBase64WithPrivate(newPassword.trim(), privateKey);
        if (SysPasswordService.matchesPassword(newPassword, user.getPassword())) {
            return ResultBean.error(MessageUtils.message("newPassword.oldpassword.cannot.same"));
        }
        newPassword = SysPasswordService.encryptPassword(newPassword);
        //验证一下验证码
        validateCode(forgetReset.getCode(),forgetReset.getPhoneNumber(),Constants.SMS_MEG_TYPE_PASSWORD);

        SysUser sysUser = new SysUser();
        sysUser.setUserId(user.getUserId());
        sysUser.setUpdateTime(DateUtils.getNowDate());
        sysUser.setUpdateBy(user.getUserId());
        sysUser.setPassword(newPassword);
        if (sysUserService.updateById(sysUser)) {
            redisCache.deleteObject(Constants.SMS_MEG_PHONE + forgetReset.getPhoneNumber() + Constants.SMS_MEG_TYPE_PASSWORD + Constants.CODE);
            return ResultBean.success();
        }
        return ResultBean.error(MessageUtils.message("edit.password.exception"));
    }

    //验证这个验证码到底对不对
    private void validateCode(String code,String phoneNumber,String codeType){
        //如果是外网并且启用了验证码，就要判断一下所传的验证码对不对
        if(network.equals("WAN") && !msgCode.isEmpty()){
            //验证码
            if(phoneNumber == null){
                SysUser myself =  sysUserService.getById(tokenService.getUserId());
                if(myself==null){
                    throw new ServiceException(MessageUtils.message("userId.not.exist"));
                }
                phoneNumber = myself.getPhoneNumber();
            }
            String codeRdis = redisCache.getCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + codeType + Constants.CODE);
            if(codeRdis == null){
                throw new ServiceException(MessageUtils.message("msg.code.expire"));
            }
            if(!codeRdis.equals(code)){
                throw new ServiceException(MessageUtils.message("msg.code.expire"));
            }
        }
    }

    @Override
    public ResultBean editMyPhone(ModifyUserPhone modifyUserPhone, LoginUser loginUser) {
        SysUser currentUser = loginUser.getSysUser();
        currentUser.setPhoneNumber(modifyUserPhone.getPhoneNumber());
        if (StringUtils.isNotEmpty(modifyUserPhone.getPhoneNumber()) && !checkPhoneUnique(currentUser)) {
            return ResultBean.error(MessageUtils.message("user.phone.already.exist.edit"));
        }
        //验证一下验证码
        validateCode(modifyUserPhone.getCode(),modifyUserPhone.getPhoneNumber(),Constants.SMS_MEG_TYPE_PHONE);
        currentUser.setUserName(modifyUserPhone.getPhoneNumber());

        if (sysUserService.updateById(currentUser)) {
            redisCache.deleteObject(Constants.SMS_MEG_PHONE + modifyUserPhone.getPhoneNumber() + Constants.SMS_MEG_TYPE_PHONE + Constants.CODE);
            // 更新缓存用户信息
            loginUser.setSysUser(currentUser);
            tokenService.setLoginUser(loginUser);
            return ResultBean.success();
        }
        return ResultBean.error(MessageUtils.message("user.edit.exception"));
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            sysUserRoleService.saveBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteUserById(Long userId) {
        checkUserAllowed(new SysUser(userId));
        // 删除用户与角色关联
        sysUserRoleService.lambdaUpdate().eq(SysUserRole::getUserId, userId).remove();
        return sysUserService.lambdaUpdate()
                .set(SysUser::getUpdateBy, userId)
                .set(SysUser::getUpdateTime, DateUtils.getNowDate())
                .set(SysUser::getDelFlag, Constants.DELETE_FLAG)
                .eq(SysUser::getUserId, userId).update();
    }

    /**
     * 批量导入用户
     *
     * @param request response
     * @return 结果
     */
    @Override
    public ResultBean importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        int successLines = 0;
        int errorLines = 0;
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象

            String realFileName = file.getOriginalFilename();
            String imgSuffix = StringUtils.substringAfterLast(realFileName, ".");
            if (!StringUtils.equalsIgnoreCase(imgSuffix, "xlsx")) {
                return ResultBean.error(MessageUtils.message("import.teplate.file"));
            }

            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<SysUserExcelImportVo> sysUserExcelImportVoList = ExcelImportUtil.importExcel(file.getInputStream(), SysUserExcelImportVo.class, params);
                for (int i = 0; i < sysUserExcelImportVoList.size(); i++) {
                    boolean isChecked = true;
                    int lineNumber = i + 1;
                    SysUserExcelImportVo sysUserExcelImportVo = sysUserExcelImportVoList.get(i);
                    String indexNo = sysUserExcelImportVo.getIndexNo();
                    //预先判断整个实体类是不是空的，如果是则continue，不是则按照正常逻辑校验
                    if (BeanIsNullUtil.checkObjAllFieldsIsNull(sysUserExcelImportVo)) {
                        continue;
                    }
                    //校验序号
                    if (StringUtils.isBlank(indexNo)) {
                        errorMessage.add(lineNumber + MessageUtils.message("index.not.null"));
                        isChecked = false;
                    } else {
                        try {
                            int indexNoLong = Integer.parseInt(indexNo);
                            if (lineNumber != indexNoLong) {
                                errorMessage.add(lineNumber + MessageUtils.message("index.not.inorder"));
                                isChecked = false;
                            }
                        } catch (Exception e) {
                            errorMessage.add(lineNumber + MessageUtils.message("index.not.legal"));
                            isChecked = false;
                        }
                    }

                    String platName = sysUserExcelImportVo.getPlatName();
                    List<Long> RoleIdList = new ArrayList<>();
                    Long platId;
                    if (StringUtils.isBlank(platName)) {
                        errorMessage.add(lineNumber + MessageUtils.message("plate.not.null"));
                        isChecked = false;
                    } else {
                        SysPlat sysPlat = sysPlatService.lambdaQuery().eq(SysPlat::getPlatName, platName).one();
                        if (sysPlat == null) {
                            errorMessage.add(lineNumber + MessageUtils.message("plate.not.exist"));
                            isChecked = false;
                        } else {
                            platId = sysPlat.getPlatId();
                            String roleNames = sysUserExcelImportVo.getRoleNames();
                            if (StringUtils.isBlank(roleNames)) {
                                errorMessage.add(lineNumber + MessageUtils.message("role.not.null"));
                                isChecked = false;
                            } else {
                                String[] roleNamesArray = roleNames.split(",");
                                for (String roleName : roleNamesArray) {
                                    SysRole sysRole = sysRoleService.lambdaQuery()
                                            .eq(SysRole::getPlatId, platId)
                                            .eq(SysRole::getRoleName, roleName)
                                            .one();
                                    if (sysRole == null) {
                                        errorMessage.add(lineNumber + MessageUtils.message("role.not.exist"));
                                        isChecked = false;
                                    } else {
                                        RoleIdList.add(sysRole.getRoleId());
                                    }
                                }
                            }
                        }
                    }

                    String userName = sysUserExcelImportVo.getUserName();
                    if (StringUtils.isBlank(userName)) {
                        errorMessage.add(lineNumber + MessageUtils.message("userName.not.null"));
                        isChecked = false;
                    } else if (userName.length() > 64) {
                        errorMessage.add(lineNumber + MessageUtils.message("userName.too.long"));
                        isChecked = false;
                    } else if (ValidatorUtil.isIllegal(userName)) {
                        errorMessage.add(lineNumber + MessageUtils.message("userName.with.illegal"));
                        isChecked = false;
                    } else {
                        SysUser user = new SysUser();
                        user.setUserName(userName);
                        if (!checkUserNameUnique(user)) {
                            errorMessage.add(lineNumber + MessageUtils.message("userName.already.exist"));
                            isChecked = false;
                        }
                    }

                    //校验数据
                    String nickName = sysUserExcelImportVo.getNickName();
                    if (StringUtils.isBlank(nickName)) {
                        errorMessage.add(lineNumber + MessageUtils.message("personName.not.null"));
                        isChecked = false;
                    } else {
                        if (nickName.length() > 64) {
                            errorMessage.add(lineNumber + MessageUtils.message("personName.too.long"));
                            isChecked = false;
                        } else if (ValidatorUtil.isIllegal(nickName)) {
                            errorMessage.add(lineNumber + MessageUtils.message("personName.with.illegal"));
                            isChecked = false;
                        }
                    }

                    String phoneNumber = sysUserExcelImportVo.getPhoneNumber();
                    String workNumber = sysUserExcelImportVo.getWorkNumber();
                    if (StringUtils.isBlank(phoneNumber) && StringUtils.isBlank(workNumber)) {
                        errorMessage.add(lineNumber + MessageUtils.message("phone.workNumber.not.null"));
                        isChecked = false;
                    } else if (!ValidatorUtil.isMobile(phoneNumber)) {
                        errorMessage.add(lineNumber + MessageUtils.message("phone.not.right"));
                        isChecked = false;
                    } else if (!ValidatorUtil.isDigit(workNumber)) {
                        errorMessage.add(lineNumber + MessageUtils.message("workNumber.not.right"));
                        isChecked = false;
                    } else {
                        SysUser user = new SysUser();
                        user.setPhoneNumber(phoneNumber);
                        user.setWorkNumber(workNumber);
                        if (!checkPhoneUnique(user)) {
                            errorMessage.add(lineNumber + MessageUtils.message("phone.already.exist"));
                            isChecked = false;
                        }
                        if (!checkWorkNumberUnique(user)) {
                            errorMessage.add(lineNumber + MessageUtils.message("workNumber.already.exist"));
                            isChecked = false;
                        }
                    }

                    try {
                        //入库
                        if (isChecked) {
                            SysUser user = new SysUser();
                            user.setUserId(YitIdHelper.nextId());
                            user.setRoleIds(RoleIdList.toArray(new Long[0]));
                            user.setUserName(sysUserExcelImportVo.getUserName());
                            user.setNickName(sysUserExcelImportVo.getNickName());
                            user.setPhoneNumber(sysUserExcelImportVo.getPhoneNumber());
                            user.setWorkNumber(sysUserExcelImportVo.getWorkNumber());
                            user.setPassword(SysPasswordService.encryptPassword(configService.selectConfigByKey("sys.user.initPassword")));
                            user.setStatus(Constants.NORMAL);
                            user.setCreateBy(tokenService.getUserId());
                            user.setCreateTime(DateUtils.getNowDate());
                            boolean saveRole = sysUserService.save(user);
                            if (saveRole) {
                                // 新增用户与角色管理
                                insertUserRole(user);
                            }
                            successLines++;
                        } else {
                            errorLines++;
                        }
                    } catch (Exception e) {
                        errorLines++;
                        errorMessage.add(lineNumber + MessageUtils.message("unkonwn.error.ignore"));
                        log.error(e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                errorMessage.add(MessageUtils.message("errorMessage.happen") + e.getMessage());
                log.error(e.getMessage(), e);
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }

        if (errorLines == 0) {
            return ResultBean.success(successLines + MessageUtils.message("all.import.success"));
        } else {
            JSONObject result = new JSONObject(5);
            int totalCount = successLines + errorLines;
            result.put(Constants.TOTAL_COUNT, totalCount);
            result.put(Constants.ERROR_COUNT, errorLines);
            result.put(Constants.SUCCESS_COUNT, successLines);
            result.put(Constants.MSG, MessageUtils.message("total.upload.line") + totalCount
                    + MessageUtils.message("already.upload.line") + successLines
                    + MessageUtils.message("upload.error.line") + errorLines);
            result.put(Constants.DETAIL, errorMessage);
            ResultBean res = new ResultBean(false, HttpStatus.CREATED, MessageUtils.message("import.success.having.error"), result);
            return res;
        }
    }

    @Override
    public ModelAndView export(SysUser sysuser) {
        List<SysUser> sysUserList = SpringUtils.getAopProxy(this).selectUserList(sysuser);

        //实体类转换
        List<SysUserExcelExportVo> sysUserExcelExportVoList = new ArrayList<>();
        if (!sysUserList.isEmpty()) {
            SimpleDateFormat bjSdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
            for (SysUser user : sysUserList) {
                SysUserExcelExportVo sysUserExcelExportVo = new SysUserExcelExportVo();
                sysUserExcelExportVo.setUserId(user.getUserId() + "");
                sysUserExcelExportVo.setUserName(user.getUserName());
                sysUserExcelExportVo.setNickName(user.getNickName());
                sysUserExcelExportVo.setPhoneNumber(user.getPhoneNumber());
                sysUserExcelExportVo.setWorkNumber(user.getWorkNumber());
                sysUserExcelExportVo.setCreateTime(bjSdf.format(user.getCreateTime()));
                sysUserExcelExportVoList.add(sysUserExcelExportVo);
            }
        }
        ModelAndView result = new JeecgController().exportXls(sysUserExcelExportVoList,
                SysUserExcelExportVo.class, Constants.USER_EXCEL_TITLE_NAME, Constants.USER_EXCEL_SHEET_NAME, Constants.USER_EXCEL_FILE_NAME);
        return result;
    }

    @Override
    public void importTemplate(HttpServletResponse response) {
        String fileName = this.getClass().getResource(Constants.PATH_DELIMITER).getPath() + Constants.PATH_EXCEL + Constants.USER_EXCEL_NAME;
        ImportTemplateUtils.importTemplateByName(fileName, response);
    }

    /**
     * 根据用户名/手机号/工号来查询用户
     *
     * @param userName
     */
    @Override
    public List<SysUser> selectUserByUserNamePhoneWork(String userName) {
        return userMapper.selectUserByUserNamePhoneWork(userName);
    }

    /**
     * 首页用户数统计
     *
     * @return
     */
    @Override
    public HashMap userStatistics() {
        HashMap<String, Object> map = new HashMap<>();
        map.put(Constants.TOTAL_USER_COUNT, sysUserService.lambdaQuery().count());
        map.put(Constants.TODAY_USER_COUNT, sysUserService.lambdaQuery().ge(SysUser::getCreateTime, DateUtils.getDate()).count());
        return map;
    }

    /**
     * 晨会定制接口，根据用户id列表获取所有用户列表(增加角色)
     */
    @Override
    public List<SysUser> getUserListByIds(Long[] userIds,long platId){
        if(userIds==null || userIds.length==0){
            return new ArrayList<>();
        }
        List<SysUser> userList = sysUserService.lambdaQuery().in(SysUser::getUserId,userIds).list();
        for(SysUser sysUser : userList){
            sysUser.setPlatId(platId);
            List<SysRole> userRoles = roleMapper.selectRolePermissionByUser(sysUser);
            sysUser.setRoles(userRoles);
        }
        return userList;
    }

    @Override
    public void updateUserByUserName(SysUser user) {
        SysUser sysUser = selectUserByUserName(user.getUserName());
        user.setUserId(sysUser.getUserId());
        if(user.getPassword()!=null){
            user.setPassword(SysPasswordService.encryptPassword(user.getPassword()));
        }
        sysUserService.updateById(user);
    }

    @Override
    public void updateUserByPhoneNumber(SysUser user) {
        SysUser sysUser = selectUserByPhoneNumber(user.getPhoneNumber());
        user.setUserId(sysUser.getUserId());
        if(user.getPassword()!=null){
            user.setPassword(SysPasswordService.encryptPassword(user.getPassword()));
        }
        sysUserService.updateById(user);
    }

    @Override
    public void updateUserByWorkNumber(SysUser user) {
        SysUser sysUser = selectUserByWorkNumber(user.getWorkNumber());
        user.setUserId(sysUser.getUserId());
        if(user.getPassword()!=null){
            user.setPassword(SysPasswordService.encryptPassword(user.getPassword()));
        }
        sysUserService.updateById(user);
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return sysUserService.save(user);
    }

    @Override
    public List<SysUser> getAllUsers() {
        return sysUserService.lambdaQuery().list();
    }

    @Override
    public List<SysUser> getChatUsers(String platId) {
        List<SysRole> sysRoleList = sysRoleService.lambdaQuery().eq(SysRole::getPlatId,platId).list();
        //拿到所有roleId
        List<Long> roleIdList = sysRoleList.stream().map(SysRole::getRoleId).collect(Collectors.toList());
        List<SysUserRole> userRoleList = sysUserRoleService.lambdaQuery().in(SysUserRole::getRoleId,roleIdList).list();
        List<Long> userIdList = userRoleList.stream().map(SysUserRole::getUserId).collect(Collectors.toList());
        return sysUserService.lambdaQuery().in(SysUser::getUserId,userIdList).ne(SysUser::getUserId,tokenService.getUserId()).list();
    }
}
