package com.jusha.ris.docking.study.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.jusha.ris.docking.common.constant.Constant;
import com.jusha.ris.docking.common.resp.ResultBean;
import com.jusha.ris.docking.common.util.HttpClientUtil;
import com.jusha.ris.docking.common.util.LoginUtil;
import com.jusha.ris.docking.feign.RisBaseClient;
import com.jusha.ris.docking.feign.StudyClient;
import com.jusha.ris.docking.study.dto.req.StudyReq;
import com.jusha.ris.docking.study.dto.resp.StudyApplyResp;
import com.jusha.ris.docking.study.dto.resp.StudyReportResp;
import com.jusha.ris.docking.study.dto.resp.StudyResp;
import com.jusha.ris.docking.study.service.StudyService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title MockDockingService
 * @description
 * @date 2025/2/24
 */
@Service
@RequiredArgsConstructor
public class StudyImplService implements StudyService{

    private static final Logger log = LoggerFactory.getLogger(StudyImplService.class);

    @Autowired
    private StudyClient studyClient;

    @Autowired
    private RisBaseClient risBaseClient;

    @Value("${ris.studyList.url:}")
    private String studyListUrl;

    @Value("${ris.reportList.url:}")
    private String reportListUrl;

    @Value("${PLAT_ID:}")
    private String PLAT_ID;

    /**
     * 模拟对接数据
     *
     * @param studyReq
     */
    public List<StudyResp> queryStudyList(StudyReq studyReq) {
        Map<String, String> header = new HashMap<>();
        header.put("platId",PLAT_ID);
        header.put("authorization", LoginUtil.getRequestHeader(Constant.TOKEN_KEY));
        if(studyListUrl == null || studyListUrl.isEmpty()){
            return new ArrayList<>();
        }
        List<StudyResp> studyRespList = new ArrayList<>();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(studyReq);
            String studyText = HttpClientUtil.doPostDefaultSecurity(studyListUrl,json,header);
            log.info("studyText================="+studyText);
            ResultBean<PageInfo<StudyApplyResp>> studyApplyResultBean = objectMapper.readValue(studyText, new TypeReference<ResultBean<PageInfo<StudyApplyResp>>>(){});
            if(studyApplyResultBean.getState()!=null && studyApplyResultBean.getState()){
                List<StudyApplyResp> studyApplyRespList = studyApplyResultBean.getData().getList();
                StudyResp studyResp = new StudyResp();
                for(StudyApplyResp studyApplyResp : studyApplyRespList){
                    studyResp.setStudyId(studyApplyResp.getStudyId()).setStudyNo(studyApplyResp.getStudyNo()).setStudyUid(studyApplyResp.getStudyUid())
                            .setAccessNumber(studyApplyResp.getAccessNumber()).setPatientId(studyApplyResp.getPatientId())
                            .setPatientName(studyApplyResp.getPatientName()).setPatientSex(studyApplyResp.getPatientSex())
                            .setPatientNo(studyApplyResp.getPatientNo()).setPatientAge(studyApplyResp.getPatientAge())
                            .setPatientType(studyApplyResp.getPatientType()).setStudyTime(studyApplyResp.getStudyTime())
                            .setStudyDevice(studyApplyResp.getStudyDevice()).setStudyType(studyApplyResp.getStudyType())
                            .setStudyItems(studyApplyResp.getStudyItems()).setStudyParts(studyApplyResp.getStudyParts());
                    String reportText = HttpClientUtil.doGetDefaultSecurity(reportListUrl+studyApplyResp.getStudyId(),header);
                    log.info("reportText================="+reportText);
                    if(reportText!=null){
                        ResultBean<StudyReportResp> studyReportResultBean = objectMapper.readValue(reportText, new TypeReference<ResultBean<StudyReportResp>>(){});
                        if(studyReportResultBean.getState()!=null && studyReportResultBean.getState()){
                            StudyReportResp studyReportResp = studyReportResultBean.getData();
                            studyResp.setStudyPurpose(studyReportResp.getStudyPurpose()).setPhysicalSign(studyReportResp.getPhysicalSign())
                                    .setSelfComplaints(studyReportResp.getSelfComplaints()).setHistoryDisease(studyReportResp.getHistoryDisease())
                                    .setHistoryDisease(studyReportResp.getHistoryDisease()).setClinicalDiagnosis(studyReportResp.getClinicalDiagnosis())
                                    .setReportDoctor(studyReportResp.getReportDoctor()).setReportTime(studyReportResp.getReportTime())
                                    .setCheckDoctor(studyReportResp.getCheckDoctor()).setCheckTime(studyReportResp.getCheckTime())
                                    .setReportId(studyReportResp.getReportId()).setReportDescribe(studyReportResp.getReportDescribe())
                                    .setReportDiagnosis(studyReportResp.getReportDiagnosis()).setPositiveStatus(studyReportResp.getPositiveStatus())
                                    .setImageRating(studyReportResp.getImageRating());
                        }
                    }
                    studyRespList.add(studyResp);
                }
               return studyRespList;
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("出错了"+e);
        }
        return new ArrayList<>();
    }
}
