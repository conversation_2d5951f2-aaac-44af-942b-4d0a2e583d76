package com.jusha.auth.common.utils.sms;

import com.jusha.auth.common.constant.Constants;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class SmsUtils {

    private static final Logger log = LoggerFactory.getLogger(SmsUtils.class);

    @Value("${msg.secretId:}")
    private String msgSecretId;

    @Value("${msg.secretKey:}")
    private String msgSecretKey;

    @Value("${msg.endpoint:}")
    private String msgEndpoint;

    @Value("${msg.region:}")
    private String msgRegion;

    @Value("${msg.sdkAppId:}")
    private String msgSdkAppId;

    @Value("${msg.signName:}")
    private String msgSignName;

    @Value("${msg.templateId-login:}")
    private String templateIdLogin;

    @Value("${msg.templateId-password:}")
    private String templateIdPassword;

    @Value("${msg.templateId-phone:}")
    private String templateIdPhoneNumber;

    public void sendMsg(String phoneNum,String msgType,String msgText) {
        try {
            SmsClient client = creatSmsClient();
            SendSmsRequest req = new SendSmsRequest();
            String[] phoneNumberSet1 = {phoneNum};
            req.setPhoneNumberSet(phoneNumberSet1);
            req.setSmsSdkAppId(msgSdkAppId);
            req.setSignName(msgSignName);
            if(msgType.equals(Constants.SMS_MEG_TYPE_LOGIN)){
                req.setTemplateId(templateIdLogin);
            }
            if(msgType.equals(Constants.SMS_MEG_TYPE_PASSWORD)){
                req.setTemplateId(templateIdPassword);
            }
            if(msgType.equals(Constants.SMS_MEG_TYPE_PHONE)){
                req.setTemplateId(templateIdPhoneNumber);
            }
            String[] templateParamSet1 = {msgText};
            req.setTemplateParamSet(templateParamSet1);
            SendSmsResponse resp = client.SendSms(req);
            log.info("发送短信结果,{}" , AbstractModel.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.error("发送短信出错,{}", e);
        }
    }

    private SmsClient creatSmsClient(){
        Credential cred = new Credential(msgSecretId, msgSecretKey);
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(msgEndpoint);
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        return new SmsClient(cred, msgRegion, clientProfile);
    }
}