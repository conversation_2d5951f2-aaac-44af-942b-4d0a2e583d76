FROM openjdk:8

#容器内创建对应文件夹
RUN mkdir -p /home/<USER>/micro-server/gateway/log
RUN chmod 777 /home/<USER>


#jar和config文件从"宿主机"拷贝到"容器"内
COPY /gateway-1.0.0.jar  /home/<USER>/micro-server/gateway/gateway-1.0.0.jar


#切换容器内工作目录
WORKDIR /home/<USER>/micro-server/gateway

#容器内执行jar
ENV JAVA_OPTS="-server -Xms512M -Xmx1024M -Xmn341M -XX:NewRatio=2 -XX:SurvivorRatio=6 -XX:MetaspaceSize=64M -XX:MaxMetaspaceSize=128M -XX:-UseParallelGC -XX:+PrintCommandLineFlags -XX:+PrintGC -XX:+PrintGCDetails"
ENTRYPOINT exec java ${JAVA_OPTS} -jar gateway-1.0.0.jar
