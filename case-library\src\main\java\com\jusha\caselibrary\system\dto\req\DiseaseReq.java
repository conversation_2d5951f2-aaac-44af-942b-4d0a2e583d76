package com.jusha.caselibrary.system.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 疾病分类查询入参
 * <AUTHOR>
 */
@Data
public class DiseaseReq {

    @ApiModelProperty(value = "疾病id")
    private Long diseaseId;

    @ApiModelProperty(value = "父级id")
    private Long parentId;

    @ApiModelProperty(value = "祖级列表")
    private String ancestors;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;
}
