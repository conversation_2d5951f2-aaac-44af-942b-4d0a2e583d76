package com.jusha.auth.monitor.domain.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 字典数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Getter
@Setter
@ApiModel(value = "字典返回值", description = "字典返回值")
public class SysDictDataResp {

    @ApiModelProperty(value = "字典标签")
    private String dictLabel;

    @ApiModelProperty(value = "字典键值")
    private String dictValue;

    @ApiModelProperty(value = "字典类型")
    private String dictType;

    public SysDictDataResp(String dictLabel, String dictValue, String dictType) {
        this.dictLabel = dictLabel;
        this.dictValue = dictValue;
        this.dictType = dictType;
    }
}
