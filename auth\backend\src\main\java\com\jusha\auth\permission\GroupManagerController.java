package com.jusha.auth.permission;

import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.SysGroup;
import com.jusha.auth.mybatisplus.service.SysGroupService;
import com.jusha.auth.system.service.ISysGroupService;
import io.swagger.annotations.*;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Set;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@Api(tags = "分组管理")
@RestController
@RequestMapping("/forbid/group")
public class GroupManagerController extends BaseController {

    @Autowired
    private ISysGroupService igroupService;

    @Autowired
    private SysGroupService sysGroupService;

    @ApiOperation("分组列表")
    @PostMapping("/list")
    public ResultBean list(@RequestBody SysGroup group) {
        String platId = getPlatId();
        group.setPlatId(Long.parseLong(platId));
        List<SysGroup> groups = igroupService.selectGroupList(group);
        return success(groups);
    }

    /**
     * 查询分组列表（排除节点）
     */
    @ApiOperation("分组列表不含自己")
    @GetMapping("/list/exclude")
    public ResultBean excludeChild(@ApiParam(required = true,name = "分组ID") @RequestParam Long groupId) {
        String platId = getPlatId();
        SysGroup sysGroup = new SysGroup(Long.parseLong(platId));
        List<SysGroup> groups = igroupService.selectGroupList(sysGroup);
        groups.removeIf(d -> d.getGroupId().equals(groupId) ||
                ArrayUtils.contains(StringUtils.split(d.getAncestors(), Constants.SEPARATOR), groupId + Constants.EMPTY_STRING));
        return success(groups);
    }

    /**
     * 根据分组编号获取详细信息
     */
    @ApiOperation("分组查询")
    @GetMapping(value = "/query")
    public ResultBean getInfo(@ApiParam(required = true,name = "分组ID") @RequestParam Long groupId) {
        String platId = getPlatId();
//        igroupService.checkGroupDataScope(groupId,platId);
        return success(igroupService.selectGroupById(groupId));
    }

    /**
     * 新增分组
     */
    @ApiOperation("分组添加")
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysGroup group) {
        String platId = getPlatId();
        //同一分组下，分组名称不可以重复
        group.setPlatId(Long.parseLong(platId));
        if (!igroupService.checkGroupNameUnique(group)) {
            SysGroup sysGroup = sysGroupService.lambdaQuery().eq(SysGroup::getPlatId, group.getPlatId())
                    .eq(SysGroup::getGroupName, group.getGroupName()).eq(SysGroup::getParentId, group.getParentId()).one();
            return success(sysGroup);
        }
//        if(group.getCenter().equals("0")){
//            if(!igroupService.checkGroupCenterUnique(group)){
//                return ResultBean.error("该联盟下已存在中心分组");
//            }
//        }

        return success(igroupService.insertGroup(group));
    }

    /**
     * 修改分组
     */
    @ApiOperation("分组修改")
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysGroup group) {
        String platId = getPlatId();
//        igroupService.checkGroupDataScope(group.getGroupId(),platId);
        group.setPlatId(Long.parseLong(platId));
        return igroupService.updateGroup(group);
    }

    /**
     * 删除分组
     */
    @ApiOperation("分组删除")
    @PostMapping("/remove")
    public ResultBean remove(@RequestBody SysGroup group) {
        String platId = getPlatId();
//        igroupService.checkGroupDataScope(group.getGroupId(),platId);
        if (igroupService.hasChildByGroupId(group.getGroupId())) {
            return error(MessageUtils.message("group.child.exist.delete"));
        }
        return resultBean(igroupService.deleteGroupById(group.getGroupId()));
    }

    @ApiOperation("教学平台互联网版定制接口——获取权限列表")
    @GetMapping("/getGroupIdList")
    public ResultBean getGroupIdList() {
        String platId = getPlatId();
        return igroupService.getGroupIdList(platId);
    }

    @ApiOperation("教学平台互联网版定制接口——获取中心医院id、联盟id和所属医院id")
    @GetMapping("/getAllianceMsg")
    public ResultBean getAllianceMsg() {
        String platId = getPlatId();
        return igroupService.getAllianceMsg(platId);
    }
}
