package com.jusha.caselibrary.sickcase.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.aop.EscapeWildcard;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.common.util.RedisUtil;
import com.jusha.caselibrary.common.aop.ESSync;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseCreateReq;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseUpdateReq;
import com.jusha.caselibrary.sickcase.dto.resp.CaseExportProcessResp;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.FollowCaseExportDataDto;
import com.jusha.caselibrary.sickcase.service.DeptCaseService;
import com.jusha.caselibrary.sickcase.export.task.CaseDetailExportTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName DeptCaseController
 * @Description 科室病例管理
 * <AUTHOR>
 * @Date 2025/7/10 09:27
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/dept/case")
@Api(tags = "病例管理-科室病例库管理")
@Validated
public class DeptCaseController {

    private final DeptCaseService deptCaseService;

    @Value("${TMP-LOCATIONS}")
    private String tmpPath;

    @EscapeWildcard
    @ApiOperation("科室病例库详细列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResultBean<List<DeptCaseDetailResp>> list (@RequestBody DeptCaseSearchReq req) {
        List<DeptCaseDetailResp> resp = deptCaseService.getDeptCaseDetailList(req);
        return ResultBean.success(resp);
    }

    @EscapeWildcard
    @ApiOperation("科室病例库详细列表分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResultBean<PageInfo<DeptCaseDetailResp>> page (@RequestBody DeptCaseSearchReq req) {
        PageInfo<DeptCaseDetailResp> resp = deptCaseService.getDeptCaseDetailPage(req);
        return ResultBean.success(resp);
    }

    @ApiOperation("科室病例库病例详情查询")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public ResultBean<DeptCaseDetailResp> detail (@RequestParam("caseId") Long caseId) {
        DeptCaseDetailResp resp = deptCaseService.getDeptCaseDetail(caseId);
        return ResultBean.success(resp);
    }

    @ApiOperation("科室病例库病例删除")
    @ESSync(type = ESSync.SyncType.DELETE, indexType  = Constant.DEP_CASE_INDEX_NAME, idField = Constant.ES_INDEX_ID)
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultBean<Void> delete (@RequestParam("caseId") Long caseId) {
        deptCaseService.deleteDeptCaseById(caseId);
        return ResultBean.success();
    }

    @ApiOperation("科室病例库病例导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public ResultBean<String> export (@RequestBody DeptCaseSearchReq req) {
        String taskId = String.valueOf(YitIdHelper.nextId());
        //任务进度存redis
        CaseExportProcessResp taskDto = new CaseExportProcessResp();
        taskDto.setTaskId(taskId);
        String taskDtoJson = JSON.toJSONString(taskDto);
        ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                Constant.TASK_OVER_TIME, TimeUnit.MINUTES);
        ContextHolder.getBean("caseExport", ExecutorService.class)
                .submit(new CaseDetailExportTask<CaseExportDataDto, DeptCaseSearchReq>(taskId, req, tmpPath));
        return ResultBean.success(taskId);
    }

    @ApiOperation("科室病例库随访病例导出")
    @RequestMapping(value = "/follow/export", method = RequestMethod.POST)
    public ResultBean<String> followExport (@RequestBody DeptCaseSearchReq req) {
        String taskId = String.valueOf(YitIdHelper.nextId());
        //任务进度存redis
        CaseExportProcessResp taskDto = new CaseExportProcessResp();
        taskDto.setTaskId(taskId);
        String taskDtoJson = JSON.toJSONString(taskDto);
        ContextHolder.stringRedisTemplate().opsForValue().set(RedisUtil.caseExportTaskKey(taskId), taskDtoJson,
                Constant.TASK_OVER_TIME, TimeUnit.MINUTES);
        ContextHolder.getBean("caseExport", ExecutorService.class)
                .submit(new CaseDetailExportTask<FollowCaseExportDataDto, DeptCaseSearchReq>(taskId, req, tmpPath));
        return ResultBean.success(taskId);
    }

    @ApiOperation("获取科室病例库病例导出任务进度")
    @RequestMapping(value = "/export/progress", method = RequestMethod.GET)
    public ResultBean<CaseExportProcessResp> exportProgress (@RequestParam("taskId") String taskId) {
        CaseExportProcessResp resp = null;
        String taskJson = ContextHolder.stringRedisTemplate().opsForValue().get(RedisUtil.caseExportTaskKey(taskId));
        if (StringUtils.isNotBlank(taskJson)) {
            resp = JSON.parseObject(taskJson, CaseExportProcessResp.class);
        }
        return ResultBean.success(resp);
    }

    @ApiOperation("新增科室病例库病例（基本信息）")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultBean<Long> create (@RequestBody DeptCaseCreateReq req) {
        long caseId = deptCaseService.createDeptCase(req);
        return ResultBean.success(caseId);
    }

    @ApiOperation("修改科室病例库病例（基本信息）")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultBean<Void> update (@RequestBody DeptCaseUpdateReq req) {
        deptCaseService.updateDeptCase(req);
        return ResultBean.success();
    }

}
