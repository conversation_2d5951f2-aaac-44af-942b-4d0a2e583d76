package com.jusha.auth.license;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jusha.auth.AuthServerApplication;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.utils.aes.AESDecrypt;
import com.jusha.auth.common.utils.aes.AESEncrypt;
import com.jusha.auth.common.utils.ip.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Base64;

@Component
public class LicenseTask {

    private static final Logger log = LoggerFactory.getLogger(LicenseTask.class);

    private static int faileTime = 0;

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${license.server.url:}")
    private String licenseServerUrl;

    @Autowired
    private RedisCache redisCache;

    @Scheduled(cron = "0 0/1 * * * ?")
    public void licenseTask() throws Exception {
        if(licenseServerUrl.isEmpty()){
            return;
        }
        //生成当前时间戳
        long timesample = System.currentTimeMillis()/1000;
        String key = "jusha1996"; // 加密密钥
        String data = applicationName+"|"+timesample; // 需要加密的数据
        // 将数据转换为字节数组
        byte[] content = data.getBytes("UTF-8");
        // 调用AESEncryptBytes方法进行加密
        byte[] encryptedData = AESEncrypt.AESEncryptBytes(content, key);
        // 将加密后的数据转换为Base64字符串
        String base64EncryptedData = bytesToBase64(encryptedData);
        // 输出Base64编码的加密数据
        JSONObject object  = new JSONObject();
        object.put("feature",base64EncryptedData);

        try {
            String result = HttpUtils.sendPost(licenseServerUrl,object.toJSONString());
            JSONObject resultObj = JSON.parseObject(result);
            if(resultObj.getInteger("code") != 0){
                faileTime = faileTime + 1;
            }else {
                faileTime = 0;
                String base64String = resultObj.getString("data");
                byte[] byteArray = Base64.getDecoder().decode(base64String);
                String decrypt = new String(AESDecrypt.AESDecryptBytes(byteArray,key));
                log.info("解密结果====="+decrypt);
            }
        }catch (Exception e) {
            faileTime = faileTime + 1;
        }

        if(faileTime>3){
            log.info("license通信失败超过3次，程序自动退出=====");
            AuthServerApplication.exit();
        }
    }

    // 将字节数组转换为Base64字符串的方法
    public static String bytesToBase64(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }
}
