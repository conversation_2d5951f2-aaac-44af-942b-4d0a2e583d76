package com.jusha.auth.system.controller;

import com.jusha.auth.common.annotation.EscapeWildcard;
import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.mybatisplus.entity.SysPlat;
import com.jusha.auth.system.service.ISysGroupService;
import com.jusha.auth.system.service.ISysMenuService;
import com.jusha.auth.system.service.ISysPlatService;
import com.jusha.auth.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 业务平台信息Controller
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
@RestController
@RequestMapping("/system/plat")
public class SysPlatController extends BaseController {
    @Autowired
    private ISysPlatService isysPlatService;

    @Autowired
    private ISysGroupService iSysGroupService;

    @Autowired
    private ISysMenuService iSysMenuService;

    @Autowired
    private ISysRoleService iSysRoleService;

    /**
     * 新增业务平台信息
     */
    @HasPermissions
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysPlat sysPlat) {
        return isysPlatService.insertSysPlat(sysPlat);
    }

    /**
     * 查询业务平台信息列表
     */
    @HasPermissions
    @GetMapping("/list")
    @EscapeWildcard
    public TableDataInfo list(SysPlat sysPlat) {
        startPage();
        List<SysPlat> list = isysPlatService.selectSysPlatList(sysPlat);
        return getDataTable(list);
    }

    /**
     * 获取业务平台信息详细信息
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long platId) {
        return success(isysPlatService.selectSysPlatByPlatId(platId));
    }

    /**
     * 修改业务平台信息
     */
    @HasPermissions
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysPlat sysPlat) {
        return isysPlatService.updateSysPlat(sysPlat);
    }

    /**
     * 删除业务平台信息
     */
    @HasPermissions
    @PostMapping("/remove")
    public ResultBean remove(@RequestParam Long platId) {
        return isysPlatService.deleteSysPlatByPlatId(platId);
    }

    /**
     * 修改业务平台对外部接口的可见状态
     */
    @HasPermissions
    @PostMapping("/changeVisible")
    public ResultBean changeVisible(@Validated @RequestBody SysPlat sysPlat) {
        return isysPlatService.updateSysPlat(sysPlat);
    }
}
