package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.jeffreyning.mybatisplus.anno.MppMultiId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 角色和菜单关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-28
 */
@Getter
@Setter
@TableName("sys_role_menu")
@ApiModel(value = "SysRoleMenu对象", description = "角色和菜单关联表")
public class SysRoleMenu {

    @ApiModelProperty(value = "角色ID")
    @MppMultiId
    @TableField("role_id")
    private Long roleId;

    @ApiModelProperty(value = "菜单ID")
    @MppMultiId
    @TableField("menu_id")
    private Long menuId;
}
