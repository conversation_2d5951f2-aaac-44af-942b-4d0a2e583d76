<template>
  <div class="app-container home">
    <el-row :gutter="20">
        <el-card class="update-log" style="width: 24%;float: left;margin-left: 0.5%;border-radius: 2px;background-color: #188df0;color: #ffffff;font-size: 16px;">
          <div slot="header" class="clearfix" style="line-height: 40px;padding-left: 10px;">
            <span>用户总数</span>
          </div>
          <div class="body" style="height: 80px;line-height: 70px;">
            <span style="font-size: 45px;">{{totalUserCount}}</span>
          </div>
          <div style="border-top: 1px solid #e6ebf5;height: 30px;line-height: 30px;font-size: 14px;">
            <div style="margin-top: 5px;">接入权限管理系统的用户总数</div>
          </div>
        </el-card>
        <el-card class="update-log" style="width: 24%;float: left;margin-left: 1%;border-radius: 2px;background-color: #01B53A;color: #ffffff;font-size: 16px;">
          <div slot="header" class="clearfix" style="line-height: 40px;padding-left: 10px;">
            <span>今日新增用户数</span>
          </div>
          <div class="body" style="height: 80px;line-height: 70px;">
            <span style="font-size: 45px;">{{todayUserCount}}</span>
          </div>
          <div style="border-top: 1px solid #e6ebf5;height: 30px;line-height: 30px;font-size: 14px;">
            <div style="margin-top: 5px;">今日新接入权限管理系统的用户数</div>
          </div>
        </el-card>
        <el-card class="update-log" style="width: 24%;float: left;margin-left: 1%;border-radius: 2px;background-color: #FFB401;color: #ffffff;font-size: 16px;">
          <div slot="header" class="clearfix" style="line-height: 40px;padding-left: 10px;">
            <span>今日登录次数</span>
          </div>
          <div class="body" style="height: 80px;line-height: 70px;">
            <span style="font-size: 45px;">{{todayLoginCount}}</span>
          </div>
          <div style="border-top: 1px solid #e6ebf5;height: 30px;line-height: 30px;font-size: 14px;">
            <div style="margin-top: 5px;">今日用户登录权限管理系统的次数</div>
          </div>
        </el-card>
        <el-card class="update-log" style="width: 24%;float: left;margin-left: 1%;border-radius: 2px;background-color: #8855D6;color: #ffffff;font-size: 16px;">
          <div slot="header" class="clearfix" style="line-height: 40px;padding-left: 10px;">
            <span>昨日登录次数</span>
          </div>
          <div class="body" style="height: 80px;line-height: 70px;">
            <span style="font-size: 45px;">{{yestodayLoginCount}}</span>
          </div>
          <div style="border-top: 1px solid #e6ebf5;height: 30px;line-height: 30px;font-size: 14px;">
            <div style="margin-top: 5px;">昨日用户登录权限管理系统的次数</div>
          </div>
        </el-card>
    </el-row>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="8" style="margin-top: 20px;">
        <el-card class="update-log">
          <div slot="header" class="titlebg clearfix">
            <span>当前在线用户</span>
          </div>
          <div class="body" style="height: 470px;">
            <el-table :data="onlineUserList">
              <el-table-column label="账户名称" width="170" align="left" prop="userName" :show-overflow-tooltip="true"/>
              <el-table-column label="登录时间" align="center" prop="loginTime">
               <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.loginTime) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8" style="margin-top: 20px;">
        <el-card class="update-log">
          <div slot="header" class="titlebg clearfix">
            <span>近七日用户登录情况top</span>
          </div>
          <div class="body" style="height: 470px;">
            <el-table :data="recent7UserList">
              <el-table-column label="账户名称" align="left" prop="userName" width="120"/>
            	<el-table-column label="登录次数" align="center" prop="count"/>
              <el-table-column label="最近一次登录时间" align="center" prop="loginTime">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.loginTime) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8" style="margin-top: 20px;">
        <el-card class="update-log">
          <div slot="header" class="titlebg clearfix">
            <span>登录失败用户</span>
          </div>
          <div class="body" style="height: 470px;">
            <el-table :data="failSysLogininfor">
            	<el-table-column label="账户名称" align="left" prop="userName" width="120"/>
            	<el-table-column label="登录时间" align="center" prop="loginTime" width="170" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.loginTime) }}</span>
                </template>
                </el-table-column>
            	<el-table-column label="失败原因" align="left" prop="msg" >
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>

import { listIndex } from "@/api/system/index";
import { listOnline } from "@/api/monitor/online";
import { loginStatistics} from "@/api/monitor/logininfor";
import { userStatistics} from "@/api/system/user";
import * as echarts from 'echarts';

export default {
  name: "Index",
  data() {
    return {
      totalUserCount: 0,
      todayUserCount: 0,
      todayLoginCount: 0,
      yestodayLoginCount: 0,
      onlineUserList: [],
      recent7UserList: [],
      failSysLogininfor: []
    };
  },

  created() {
    this.getList();
  },
  methods: {
    getList() {
      
      this.loading = true;
      listOnline().then(response => {
         this.onlineUserList = response.rows;
      });
      userStatistics().then(response => {
         this.totalUserCount = response.data.totalUserCount;
         this.todayUserCount = response.data.todayUserCount;
      });
      loginStatistics().then(response => {
         this.recent7UserList = response.data.recent7UserList;
         this.failSysLogininfor = response.data.failSysLogininfor;
         this.todayLoginCount = response.data.todayLoginCount;
         this.yestodayLoginCount = response.data.yestodayLoginCount;
      });
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="scss">
  .titlebg{
    padding-left: 10px;font-size: 16px;
    background-image: -webkit-linear-gradient(left,#0084FF,#ffffff);
    color: #ffffff;
    height: 40px;
    line-height: 40px;
    background-size: 100% 100%;
  }
</style>
