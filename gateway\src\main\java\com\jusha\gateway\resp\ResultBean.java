package com.jusha.gateway.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 后端接口统一返回
 * @param <T>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResultBean<T> {

    /**
     * 请求状态
     */
    @JsonProperty(index = 1)
    @JSONField(ordinal = 1)
    private Boolean state;

    /**
     * 错误码
     */
    @JsonProperty(index = 2)
    @JSONField(ordinal = 2)
    private int errorCode;

    /**
     * 错误信息
     */
    @JsonProperty(index = 3)
    @JSONField(ordinal = 3)
    private String message;

    /**
     * 返回数据
     */
    @JsonProperty(index = 4)
    @JSONField(ordinal = 4)
    private T data;


    /**
     * 失败返回
     */
    public static <T> ResultBean<T> error(Integer code, String messages){
        return new ResultBean<>(false, code, messages, null);
    }

}