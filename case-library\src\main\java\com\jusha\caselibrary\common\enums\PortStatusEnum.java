package com.jusha.caselibrary.common.enums;

/**
 * 文件导入、导出状态枚举
 *
 * <AUTHOR>
 * @date 2024/09/24
 **/
public enum PortStatusEnum {
    /**
     * 未开始
     */
    NOTSTART((short)0),
    /**
     * 成功
     */
    SUCCESS((short)1),

    /**
     * 失败
     */
    FAILURE((short)2),

    /**
     * 进行中
     */
    UNDERWAY((short)3);

    private Short value;

    PortStatusEnum(Short value) {
        this.value = value;
    }
    public Short getValue() {
        return value;
    }
}
