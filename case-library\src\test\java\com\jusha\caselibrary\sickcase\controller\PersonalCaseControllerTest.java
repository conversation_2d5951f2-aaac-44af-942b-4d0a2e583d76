package com.jusha.caselibrary.sickcase.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jusha.caselibrary.search.service.CaseManagementService;
import com.jusha.caselibrary.sickcase.dto.req.CatalogOperationRequest;
import com.jusha.caselibrary.sickcase.service.PersonalCaseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * @ClassName PersonalCaseControllerTest
 * @Description 个人病例库控制器测试类
 * <AUTHOR>
 * @Date 2025/7/11
 **/
@WebMvcTest(PersonalCaseController.class)
public class PersonalCaseControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PersonalCaseService personalCaseService;

    @MockBean
    private CaseManagementService caseManagementService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testCreatePersonalCaseInCatalog() throws Exception {
        // 准备测试数据
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法
        when(caseManagementService.createPersonalCaseInCatalog(anyLong(), anyLong())).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/personal/case/create-in-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void testDeletePersonalCaseFromCatalog() throws Exception {
        // 准备测试数据
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法
        when(caseManagementService.deletePersonalCaseFromCatalog(anyLong(), anyLong())).thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/personal/case/delete-from-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void testCreatePersonalCaseInCatalogWithInvalidRequest() throws Exception {
        // 准备无效的测试数据（缺少必填字段）
        CatalogOperationRequest request = new CatalogOperationRequest();
        // 不设置caseId和catalogId，触发参数校验

        // 执行测试，期望返回400错误
        mockMvc.perform(post("/personal/case/create-in-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void testDeletePersonalCaseFromCatalogWithInvalidRequest() throws Exception {
        // 准备无效的测试数据（缺少必填字段）
        CatalogOperationRequest request = new CatalogOperationRequest();
        // 不设置caseId和catalogId，触发参数校验

        // 执行测试，期望返回400错误
        mockMvc.perform(delete("/personal/case/delete-from-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void testCreatePersonalCaseInCatalogWithServiceFailure() throws Exception {
        // 准备测试数据
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法返回失败
        when(caseManagementService.createPersonalCaseInCatalog(anyLong(), anyLong())).thenReturn(false);

        // 执行测试
        mockMvc.perform(post("/personal/case/create-in-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(false));
    }

    @Test
    public void testDeletePersonalCaseFromCatalogWithServiceFailure() throws Exception {
        // 准备测试数据
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法返回失败
        when(caseManagementService.deletePersonalCaseFromCatalog(anyLong(), anyLong())).thenReturn(false);

        // 执行测试
        mockMvc.perform(delete("/personal/case/delete-from-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(false));
    }

    @Test
    public void testCreatePersonalCaseInCatalogWithException() throws Exception {
        // 准备测试数据
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法抛出异常
        when(caseManagementService.createPersonalCaseInCatalog(anyLong(), anyLong()))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试 - 控制器应该捕获异常并返回错误响应
        mockMvc.perform(post("/personal/case/create-in-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    public void testDeletePersonalCaseFromCatalogWithException() throws Exception {
        // 准备测试数据
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法抛出异常
        when(caseManagementService.deletePersonalCaseFromCatalog(anyLong(), anyLong()))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试 - 控制器应该捕获异常并返回错误响应
        mockMvc.perform(delete("/personal/case/delete-from-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500));
    }

    @Test
    public void testCatalogOperationDesignConcept() throws Exception {
        // 测试"目录下操作"的设计理念
        // 验证新的API端点体现了在目录下创建和删除病例的概念
        
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法
        when(caseManagementService.createPersonalCaseInCatalog(anyLong(), anyLong())).thenReturn(true);
        when(caseManagementService.deletePersonalCaseFromCatalog(anyLong(), anyLong())).thenReturn(true);

        // 测试在目录下创建病例
        mockMvc.perform(post("/personal/case/create-in-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));

        // 测试从目录删除病例
        mockMvc.perform(delete("/personal/case/delete-from-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));

        // 验证调用了正确的服务方法
        verify(caseManagementService).createPersonalCaseInCatalog(1L, 100L);
        verify(caseManagementService).deletePersonalCaseFromCatalog(1L, 100L);
    }

}