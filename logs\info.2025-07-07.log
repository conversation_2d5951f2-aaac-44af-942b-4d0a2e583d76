20:59:09.869 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
20:59:14.239 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
20:59:14.240 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
20:59:14.244 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
20:59:14.468 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
20:59:14.468 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
20:59:14.505 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
20:59:18.154 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
20:59:18.164 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 813e1e72-ce53-4a10-b121-5a2890b34208_config-0
20:59:18.401 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:59:18.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/1558460059
20:59:18.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/2139266166
20:59:18.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:59:18.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:59:18.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
20:59:32.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
20:59:35.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
20:59:36.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1751893176232_172.30.192.1_48553
20:59:36.456 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Notify connected event to listeners.
20:59:36.457 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Connected,notify listen context...
20:59:36.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:59:36.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/1125614861
20:59:36.542 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
20:59:37.259 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:59:38.022 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,648] - No active profile set, falling back to default profiles: default
20:59:39.442 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Success to connect a server [*************:8848], connectionId = 1751893179164_172.30.192.1_48561
20:59:39.442 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Abandon prev connection, server is *************:8848, connectionId is 1751893176232_172.30.192.1_48553
20:59:39.443 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,592] - Close current connection 1751893176232_172.30.192.1_48553
20:59:39.444 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Notify disconnected event to listeners
20:59:39.444 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onDisConnect,637] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] DisConnected,clear listen context...
20:59:39.445 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Notify connected event to listeners.
20:59:39.445 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [813e1e72-ce53-4a10-b121-5a2890b34208_config-0] Connected,notify listen context...
20:59:42.821 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
21:00:39.113 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
21:00:41.838 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
21:00:41.839 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
21:00:41.846 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
21:00:42.215 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
21:00:42.215 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
21:00:42.241 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
21:00:45.549 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
21:00:45.555 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0
21:00:45.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
21:00:45.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/513279161
21:00:45.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/88335763
21:00:45.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
21:00:45.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
21:00:45.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
21:00:57.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
21:01:00.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
21:01:03.413 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
21:01:03.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
21:01:03.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$278/640736196
21:01:03.513 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
21:01:04.187 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,648] - No active profile set, falling back to default profiles: default
21:01:09.016 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
21:01:09.585 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bee32c81-0b7c-475c-b07a-e72df7a714cd_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '*************', server main port = 8848}, error = unknown
21:03:40.218 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
21:03:41.874 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,87] - [settings] [req-serv] nacos-server port:8848
21:03:41.875 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,98] - [settings] [http-client] connect timeout:1000
21:03:41.879 [main] INFO  c.a.n.c.u.ParamUtil - [<clinit>,105] - PER_TASK_CONFIG_SIZE: 3000.0
21:03:42.035 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
21:03:42.035 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
21:03:42.049 [main] INFO  c.a.n.c.a.r.i.CredentialWatcher - [loadCredential,164] - null No credential found
21:03:45.041 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
21:03:45.047 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,95] - [RpcClientFactory] create a new rpc client of 38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0
21:03:45.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
21:03:45.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$263/1407428730
21:03:45.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$264/1077316166
21:03:45.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
21:03:45.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
21:03:45.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
21:03:54.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
21:03:56.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Success to connect to server [*************:8848] on start up, connectionId = 1751893436076_172.30.192.1_50390
21:03:56.142 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Notify connected event to listeners.
21:03:56.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,630] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Connected,notify listen context...
21:03:56.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
21:03:56.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38a77f46-6f95-4e0e-99d0-2995443b21f2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$272/49619396
21:03:56.230 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,61] - limitTime:5.0
21:03:56.583 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
21:03:56.649 [main] INFO  c.j.a.AuthServerApplication - [logStartupProfileInfo,652] - The following profiles are active: druid
21:04:03.048 [main] INFO  io.undertow.servlet - [log,371] - Initializing Spring embedded WebApplicationContext
21:04:06.811 [main] INFO  c.j.a.c.c.SnowIdConfig - [afterPropertiesSet,38] - SnowIdConfig workerId:1, shortValue:1
21:04:12.189 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
21:04:12.190 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
21:04:12.190 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysRole not contain MppMultiId anno
21:04:12.267 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
21:04:12.267 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
21:04:12.267 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysUser not contain MppMultiId anno
21:04:12.566 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
21:04:12.567 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
21:04:12.567 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysPlat not contain MppMultiId anno
21:04:12.628 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
21:04:12.628 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
21:04:12.629 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysGroup not contain MppMultiId anno
21:04:12.716 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
21:04:12.717 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
21:04:12.717 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenu not contain MppMultiId anno
21:04:12.778 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
21:04:12.778 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
21:04:12.778 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysMenuInterface not contain MppMultiId anno
21:04:12.876 [main] INFO  c.g.j.m.b.SelectByMultiIdMethod - [createWhere,47] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
21:04:12.877 [main] INFO  c.g.j.m.b.UpdateByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
21:04:12.877 [main] INFO  c.g.j.m.b.DeleteByMultiIdMethod - [createWhere,45] - entity com.jusha.auth.mybatisplus.entity.SysConfig not contain MppMultiId anno
