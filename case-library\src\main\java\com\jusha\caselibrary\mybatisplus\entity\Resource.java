package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 文件资源
 * <AUTHOR>
 */
@TableName("t_resource")
@Data
public class Resource {

    @ApiModelProperty(value = "resource_id")
    @TableId("resource_id")
    private Long resourceId;    

    @ApiModelProperty(value = "原文件名称")
    @TableField("origin_name")
    private String originName;    

    @ApiModelProperty(value = "实际存储文件名称（加入时间）")
    @TableField("actual_name")
    private String actualName;    

    @ApiModelProperty(value = "文件访问相对路径")
    @TableField("file_url")
    private String fileUrl;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;    

    @ApiModelProperty(value = "#联盟分组ID--")
    @TableField("lm_gp_id")
    private Long lmGpId;    

}
