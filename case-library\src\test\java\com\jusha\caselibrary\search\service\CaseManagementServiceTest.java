package com.jusha.caselibrary.search.service;

import com.jusha.caselibrary.mybatisplus.entity.UserCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCaseCatalog;
import com.jusha.caselibrary.mybatisplus.mapper.UserCaseMapper;
import com.jusha.caselibrary.mybatisplus.service.UserCaseCatalogService;
import com.jusha.caselibrary.search.service.impl.CaseManagementServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 病例管理服务测试类
 * 
 * <AUTHOR> Code
 * @date 2025/07/11
 */
@ExtendWith(MockitoExtension.class)
public class CaseManagementServiceTest {

    @Mock
    private UserCaseMapper userCaseMapper;

    @Mock
    private UserCaseCatalogService userCaseCatalogService;

    @InjectMocks
    private CaseManagementServiceImpl caseManagementService;

    /**
     * 测试在指定目录下创建个人病例 - 成功场景
     */
    @Test
    public void testCreatePersonalCaseInCatalogSuccess() {
        // 准备测试数据
        Long caseId = 1L;
        Long catalogId = 100L;

        // Mock 查询现有病例
        UserCase existingCase = new UserCase();
        existingCase.setCaseId(caseId);
        when(userCaseMapper.selectById(caseId)).thenReturn(existingCase);

        // Mock 查询关联关系不存在
        when(userCaseCatalogService.count(any())).thenReturn(0);

        // Mock 保存关联关系
        when(userCaseCatalogService.save(any(UserCaseCatalog.class))).thenReturn(true);

        // 执行测试
        boolean result = caseManagementService.createPersonalCaseInCatalog(caseId, catalogId);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseMapper).selectById(caseId);
        verify(userCaseCatalogService).save(any(UserCaseCatalog.class));
    }

    /**
     * 测试在指定目录下创建个人病例 - 病例不存在
     */
    @Test
    public void testCreatePersonalCaseInCatalogWhenCaseNotExists() {
        // 准备测试数据
        Long caseId = 1L;
        Long catalogId = 100L;

        // Mock 查询返回null
        when(userCaseMapper.selectById(caseId)).thenReturn(null);

        // 执行测试
        boolean result = caseManagementService.createPersonalCaseInCatalog(caseId, catalogId);

        // 验证结果
        assertFalse(result);
        
        // 验证调用
        verify(userCaseMapper).selectById(caseId);
        verify(userCaseCatalogService, never()).save(any(UserCaseCatalog.class));
    }

    /**
     * 测试在指定目录下创建个人病例 - 关联已存在
     */
    @Test
    public void testCreatePersonalCaseInCatalogWhenRelationAlreadyExists() {
        // 准备测试数据
        Long caseId = 1L;
        Long catalogId = 100L;

        // Mock 查询现有病例
        UserCase existingCase = new UserCase();
        existingCase.setCaseId(caseId);
        when(userCaseMapper.selectById(caseId)).thenReturn(existingCase);

        // Mock 查询关联关系已存在
        when(userCaseCatalogService.count(any())).thenReturn(1);

        // 执行测试
        boolean result = caseManagementService.createPersonalCaseInCatalog(caseId, catalogId);

        // 验证结果 - 应该返回true，因为关联已存在
        assertTrue(result);
        
        // 验证调用 - 不应该进行保存操作
        verify(userCaseMapper).selectById(caseId);
        verify(userCaseCatalogService, never()).save(any(UserCaseCatalog.class));
    }

    /**
     * 测试从指定目录删除个人病例 - 成功场景
     */
    @Test
    public void testDeletePersonalCaseFromCatalogSuccess() {
        // 准备测试数据
        Long caseId = 1L;
        Long catalogId = 100L;

        // Mock 查询关联关系存在
        when(userCaseCatalogService.count(any())).thenReturn(1);

        // Mock 删除关联关系成功
        when(userCaseCatalogService.remove(any())).thenReturn(true);

        // Mock 查询剩余关联数量
        when(userCaseCatalogService.count(any())).thenReturn(2); // 还有其他关联

        // 执行测试
        boolean result = caseManagementService.deletePersonalCaseFromCatalog(caseId, catalogId);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseCatalogService).remove(any());
    }

    /**
     * 测试从指定目录删除个人病例 - 关联不存在
     */
    @Test
    public void testDeletePersonalCaseFromCatalogWhenRelationNotExists() {
        // 准备测试数据
        Long caseId = 1L;
        Long catalogId = 100L;

        // Mock 查询关联关系不存在
        when(userCaseCatalogService.count(any())).thenReturn(0);

        // 执行测试
        boolean result = caseManagementService.deletePersonalCaseFromCatalog(caseId, catalogId);

        // 验证结果 - 应该返回true，因为关联已经不存在
        assertTrue(result);
        
        // 验证调用 - 不应该进行删除操作
        verify(userCaseCatalogService, never()).remove(any());
    }

    /**
     * 测试从指定目录删除个人病例 - 删除最后一个关联
     */
    @Test
    public void testDeletePersonalCaseFromCatalogWhenLastRelation() {
        // 准备测试数据
        Long caseId = 1L;
        Long catalogId = 100L;

        // Mock 查询关联关系存在
        when(userCaseCatalogService.count(any())).thenReturn(1).thenReturn(0); // 第一次查询存在，删除后查询为0

        // Mock 删除关联关系成功
        when(userCaseCatalogService.remove(any())).thenReturn(true);

        // 执行测试
        boolean result = caseManagementService.deletePersonalCaseFromCatalog(caseId, catalogId);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseCatalogService).remove(any());
        // 注意：这里会触发ES DELETE同步，但不会删除UserCase记录
    }

    /**
     * 测试批量导入个人病例
     */
    @Test
    public void testBatchImportPersonalCases() {
        // 准备测试数据
        List<UserCase> userCases = new ArrayList<>();
        UserCase case1 = new UserCase();
        case1.setCaseId(1L);
        UserCase case2 = new UserCase();
        case2.setCaseId(2L);
        userCases.add(case1);
        userCases.add(case2);

        // Mock 插入操作
        when(userCaseMapper.insert(any(UserCase.class))).thenReturn(1);

        // 执行测试
        boolean result = caseManagementService.batchImportPersonalCases(userCases);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseMapper, times(2)).insert(any(UserCase.class));
    }

    /**
     * 测试批量导入个人病例 - 空列表
     */
    @Test
    public void testBatchImportPersonalCasesWithEmptyList() {
        // 准备测试数据
        List<UserCase> userCases = new ArrayList<>();

        // 执行测试
        boolean result = caseManagementService.batchImportPersonalCases(userCases);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseMapper, never()).insert(any(UserCase.class));
    }

    /**
     * 测试批量导入个人病例 - null列表
     */
    @Test
    public void testBatchImportPersonalCasesWithNullList() {
        // 执行测试
        boolean result = caseManagementService.batchImportPersonalCases(null);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseMapper, never()).insert(any(UserCase.class));
    }

    /**
     * 测试异常情况处理
     */
    @Test
    public void testExceptionHandling() {
        // 准备测试数据
        Long caseId = 1L;
        Long catalogId = 100L;

        // Mock 抛出异常
        when(userCaseMapper.selectById(caseId)).thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试 - 应该返回false而不是抛出异常
        boolean result = caseManagementService.createPersonalCaseInCatalog(caseId, catalogId);

        // 验证结果
        assertFalse(result);
        
        // 验证调用
        verify(userCaseMapper).selectById(caseId);
        verify(userCaseCatalogService, never()).save(any(UserCaseCatalog.class));
    }

    /**
     * 测试参数校验
     */
    @Test
    public void testParameterValidation() {
        // 测试caseId为null
        boolean result1 = caseManagementService.createPersonalCaseInCatalog(null, 100L);
        assertFalse(result1);

        // 测试catalogId为null
        boolean result2 = caseManagementService.createPersonalCaseInCatalog(1L, null);
        assertFalse(result2);

        // 测试两个参数都为null
        boolean result3 = caseManagementService.deletePersonalCaseFromCatalog(null, null);
        assertFalse(result3);

        // 验证没有调用数据库操作
        verify(userCaseMapper, never()).selectById(any());
        verify(userCaseCatalogService, never()).save(any());
        verify(userCaseCatalogService, never()).remove(any());
    }

    /**
     * 测试兼容旧接口
     */
    @Test
    public void testDeprecatedMethods() {
        // 准备测试数据
        Long caseId = 1L;
        Long catalogId = 100L;

        // Mock 查询现有病例
        UserCase existingCase = new UserCase();
        existingCase.setCaseId(caseId);
        when(userCaseMapper.selectById(caseId)).thenReturn(existingCase);

        // Mock 查询关联关系不存在
        when(userCaseCatalogService.count(any())).thenReturn(0);

        // Mock 保存关联关系
        when(userCaseCatalogService.save(any(UserCaseCatalog.class))).thenReturn(true);

        // 测试兼容旧接口
        boolean result1 = caseManagementService.addPersonalCaseToCatalog(caseId, catalogId);
        assertTrue(result1);

        // Mock 删除关联关系
        when(userCaseCatalogService.count(any())).thenReturn(1);
        when(userCaseCatalogService.remove(any())).thenReturn(true);

        boolean result2 = caseManagementService.removePersonalCaseFromCatalog(caseId, catalogId);
        assertTrue(result2);
    }

    /**
     * 测试创建个人病例
     */
    @Test
    public void testCreatePersonalCase() {
        // 准备测试数据
        UserCase userCase = new UserCase();
        userCase.setCaseId(1L);
        userCase.setCaseName("测试病例");

        // Mock 插入操作
        when(userCaseMapper.insert(any(UserCase.class))).thenReturn(1);

        // 执行测试
        boolean result = caseManagementService.createPersonalCase(userCase);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseMapper).insert(any(UserCase.class));
    }

    /**
     * 测试更新个人病例
     */
    @Test
    public void testUpdatePersonalCase() {
        // 准备测试数据
        UserCase userCase = new UserCase();
        userCase.setCaseId(1L);
        userCase.setCaseName("更新后的病例");

        // Mock 更新操作
        when(userCaseMapper.updateById(any(UserCase.class))).thenReturn(1);

        // 执行测试
        boolean result = caseManagementService.updatePersonalCase(userCase);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseMapper).updateById(any(UserCase.class));
    }

    /**
     * 测试删除个人病例
     */
    @Test
    public void testDeletePersonalCase() {
        // 准备测试数据
        Long caseId = 1L;

        // Mock 删除操作
        when(userCaseMapper.deleteById(caseId)).thenReturn(1);

        // 执行测试
        boolean result = caseManagementService.deletePersonalCase(caseId);

        // 验证结果
        assertTrue(result);
        
        // 验证调用
        verify(userCaseMapper).deleteById(caseId);
    }

    /**
     * 测试目录下操作的设计理念
     */
    @Test
    public void testCatalogOperationDesignConcept() {
        // 这个测试用于验证"目录下操作"的设计理念
        // 即：个人病例的创建和删除都是在特定目录下进行的
        
        Long caseId = 1L;
        Long catalogId = 100L;

        // 测试在目录下创建病例
        UserCase existingCase = new UserCase();
        existingCase.setCaseId(caseId);
        when(userCaseMapper.selectById(caseId)).thenReturn(existingCase);
        when(userCaseCatalogService.count(any())).thenReturn(0);
        when(userCaseCatalogService.save(any(UserCaseCatalog.class))).thenReturn(true);

        boolean createResult = caseManagementService.createPersonalCaseInCatalog(caseId, catalogId);
        assertTrue(createResult, "在目录下创建个人病例应该成功");

        // 测试从目录删除病例
        when(userCaseCatalogService.count(any())).thenReturn(1).thenReturn(0);
        when(userCaseCatalogService.remove(any())).thenReturn(true);

        boolean deleteResult = caseManagementService.deletePersonalCaseFromCatalog(caseId, catalogId);
        assertTrue(deleteResult, "从目录删除个人病例应该成功");

        // 验证这种设计确保了catalogIds的智能管理
        verify(userCaseCatalogService, times(2)).save(any(UserCaseCatalog.class));
        verify(userCaseCatalogService).remove(any());
    }

}