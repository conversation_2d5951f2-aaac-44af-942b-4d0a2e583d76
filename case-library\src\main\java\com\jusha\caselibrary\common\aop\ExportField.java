package com.jusha.caselibrary.common.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName ExportField
 * @Description 导出字段注解
 * <AUTHOR>
 * @Date 2025/7/10 17:08
 **/
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExportField {
    /**
     * 表头名称
     */
    String value();

    /**
     * 列索引
     */
    int index();

    /**
     * 列宽
     */
    int width() default 15;

    /**
     * 是否支持合并
     */
    boolean mergeable() default false;

    /**
     * 合并类型：CASE-病例信息合并，NONE-不合并
     */
    String mergeType() default "NONE";

    /**
     * 是否导出该字段，默认为true
     * 设置为false时，该字段不会被导出到Excel中
     */
    boolean exportable() default true;
}

