package com.jusha.caselibrary.common.acHolder;


import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.LoginUtil;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * application.yml中配置的持有对象
 */
@RefreshScope
@Getter
@Component
public class PropertiesBean {

    @Value("${PLAT_ID}")
    private Long platId;            //平台ID

    @Value("${SERVER_NAME}")
    private String applicationName;    //服务名

    @Value("${ENVIRONMENT:}")
    private String environment;     //环境(dev、test、prod)

    @Value("${TMP-LOCATIONS}")
    private String tmpLocation;     //临时文件存放目录

    @Value("${LICENSE_SERVER:}")
    private String licenseServer;     //license服务地址

    @Value("${MINIO_ENDPOINT:}")
    private String minioPath;       //minio服务地址

    @Value("${SERIES_DOWN_OUT_TIME:2000}")
    private Integer seriesDownOutTime;      //下载序列影像到临时文件夹下的超时时间(s)


    @Value("${minio.accessKey:ZCF39RO66N44LEDU0H96}")
    private String minioAccessKey;

    @Value("${minio.secretKey:0fZi75DXv69RtqxT4cBuD0VOxBPo7c5v2fAmqeJ3}")
    private String minioSecretKey;

    @Value("${minio.bucketName:rieman-bucket}")
    private String minioBucketName;


    @Value("${spring.cloud.nacos.config.server-addr}")
    private String nacosAddr;         //nacos地址

    @Value("${spring.cloud.nacos.config.namespace}")
    private String nacosNamespace;   //nacos命名空间


    @Value("${spring.datasource.db1.url}")
    private String dbUrl;

    @Value("${spring.datasource.db1.username}")
    private String dbUsername;

    @Value("${spring.datasource.db1.password}")
    private String dbPassword;

    @Value("${cos.secretId}")
    private String secretId;

    @Value("${cos.secretKey}")
    private String secretKey;

    @Value("${cos.bucketName}")
    private String bucketName;

    @Value("${cos.region}")
    private String region;

    @Value("${cos.endpoint}")
    private String endpoint;

    @Value("${cos.vodUrl:}")
    private String vodUrl;
    

    public String getMinioPath(){
        return Constant.NETWORK_LAN.equals(LoginUtil.getNetwork())?minioPath:"";
    }

}