package com.jusha.auth.system.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * 路由显示信息
 * 
 * <AUTHOR>
 */

@Getter
@Setter
public class MetaVo {
    /**
     * 设置该路由在侧边栏和面包屑中展示的名字
     */
    private String title;

    /**
     * 设置该路由的图标，对应路径src/assets/icons/svg
     */
    private String icon;

    private String[] keyWords;

    public MetaVo() {
    }

    public MetaVo(String title, String icon) {
        this.title = title;
        this.icon = icon;
        this.keyWords = keyWords;
    }

    public MetaVo(String title, String icon,String[] keyWords) {
        this.title = title;
        this.icon = icon;
        this.keyWords = keyWords;
    }
}
