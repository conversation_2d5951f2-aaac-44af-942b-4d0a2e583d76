package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <p>
 * 菜单权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Getter
@Setter
@TableName("sys_interface")
@ApiModel(value = "SysInterface对象", description = "菜单权限表")
public class SysInterface {

    @ApiModelProperty(value = "接口ID")
    @TableId("interface_id")
    private Long interfaceId;

    @ApiModelProperty(value = "所属平台id")
    @TableField("plat_id")
    private Long platId;

    @ApiModelProperty(value = "接口名称")
    @TableField("interface_name")
    @Size(message = "接口名称长度不合法",max = 20)
    private String interfaceName;

    @ApiModelProperty(value = "接口路径")
    @TableField("interface_path")
    @Size(message = "接口路径长度不合法",max = 100)
    private String interfacePath;

    @ApiModelProperty(value = "删除标志（0代表存在 1代表删除）")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;

    @ApiModelProperty(value = "创建者")
    @TableField("create_by")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField("update_by")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;
}
