package com.jusha.auth.common.utils.poi;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

@Slf4j
public class ImportTemplateUtils {

    public static void importTemplateByName(String fileName,HttpServletResponse response) {
        BufferedInputStream bufferedInputStream = null;
        InputStream fis = null;
        try {
            // path是指欲下载的文件的路径。
            File file = new File(fileName);
            // 取得文件名。
            String filename = file.getName();
            // 以流的形式下载文件。
            bufferedInputStream = new BufferedInputStream(new FileInputStream(fileName));
            fis = bufferedInputStream;
            byte[] buffer = new byte[fis.available()];

            while (true) {
                int read = fis.read(buffer);
                //判断是不是读到了数据流的末尾 ，防止出现死循环。
                if (read == -1) {
                    break;
                }
            }

            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes()));
            response.addHeader("Content-Length", "" + file.length());
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
        } catch (IOException e) {
            log.error("{}", e);
        }finally {
            CloseIoUtils.closeAll(bufferedInputStream);
            CloseIoUtils.closeAll(fis);
        }
    }
}
