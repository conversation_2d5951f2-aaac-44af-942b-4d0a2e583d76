package com.jusha.auth.permission;

import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.SysGroup;
import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.mybatisplus.entity.SysUserRole;
import com.jusha.auth.system.domain.UsersRole;
import com.jusha.auth.system.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/role")
public class RoleFrontController extends BaseController {
    @Autowired
    private ISysRoleService iSysroleService;

    @Autowired
    private ISysPlatService iSysPlatService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysMenuService iSysmenuService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysGroupService iSysGroupService;

    @ApiOperation("获取角色列表")
    @GetMapping("/list")
    public ResultBean list(SysRole role) {
        String platId = getPlatId();
        role.setPlatId(Long.parseLong(platId));
        startPage();
        List<SysRole> list = iSysroleService.selectRoleList(role);
        return ResultBean.success(getDataTableNoCode(list));
    }

    /**
     * 根据角色编号获取详细信息
     */
    @GetMapping(value = "/query")
    @ApiOperation("查询角色详细信息")
    public ResultBean getInfo(@ApiParam(required = true, name = "角色ID") @RequestParam Long roleId) {
        String platId = getPlatId();
        iSysroleService.checkRoleDataScope(platId);
        return success(iSysroleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    @ApiOperation("新增角色")
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysRole role) {
        String platId = getPlatId();
        role.setPlatId(Long.parseLong(platId));
        if (!iSysroleService.checkRoleNameUnique(role)) {
            return error(MessageUtils.message("role.already.exist.add"));
        }
        return success(iSysroleService.insertRole(role));
    }

    /**
     * 修改保存角色
     */
    @ApiOperation("修改角色")
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysRole role) {
        String platId = getPlatId();
        role.setPlatId(Long.parseLong(platId));
        iSysroleService.checkRoleDataScope(platId);
        iSysroleService.checkRoleAllowed(role);
        if (!iSysroleService.checkRoleNameUnique(role)) {
            return error(MessageUtils.message("role.already.exist.edit"));
        }
        if (iSysroleService.updateRole(role)) {
            // 更新缓存用户权限（20240920这部分很奇怪，因为修改某个用户的时候，不应该改变当前登录用户的角色缓存，忘记当年怎么想的了）
//            LoginUser loginUser = getLoginUser();
//            if (StringUtils.isNotNull(loginUser.getSysUser()) && !loginUser.getSysUser().isAdmin()) {
//                loginUser.setPermissions(iSysmenuService.selectIterfacePathsByUserId(loginUser.getSysUser(), role.getPlatId()));
//                loginUser.setSysUser(iSysUserService.selectUserByUserName(loginUser.getSysUser().getUserName()));
//                tokenService.setLoginUser(loginUser);
//            }
            return success();
        }
        return error(MessageUtils.message("role.edit.exception"));
    }

    /**
     * 修改保存数据权限
     */
    @ApiOperation("修改角色数据权限")
    @PostMapping(value = "/dataScope")
    public ResultBean dataScope(@Validated @RequestBody SysRole role) {
        String platId = getPlatId();
        iSysroleService.checkRoleDataScope(platId);
        iSysroleService.checkRoleAllowed(role);
        return resultBean(iSysroleService.authDataScope(role));
    }

    /**
     * 状态修改
     */
    @ApiOperation("修改角色状态")
    @PostMapping(value = "/changeStatus")
    public ResultBean changeStatus(@Validated @RequestBody SysRole role) {
        iSysroleService.checkRoleAllowed(role);
        String platId = getPlatId();
        iSysroleService.checkRoleDataScope(platId);
        return resultBean(iSysroleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @ApiOperation("删除角色")
    @PostMapping("/remove")
    public ResultBean remove(@RequestBody SysRole sysRole) {
        String platId = getPlatId();
        iSysroleService.checkRoleDataScope(platId);
        return resultBean(iSysroleService.deleteRoleById(sysRole.getRoleId()));
    }


    /**
     * 查询已分配用户角色列表
     */
    @ApiOperation("查询已分配用户角色列表")
    @GetMapping("/authUser/allocatedList")
    public TableDataInfo allocatedList(SysUser user) {
        startPage();
        List<SysUser> list = iSysUserService.selectAllocatedList(user);
        return getDataTable(list);
    }

    /**
     * 查询未分配用户角色列表
     */
    @ApiOperation("查询未分配用户角色列表")
    @GetMapping("/authUser/unallocatedList")
    public TableDataInfo unallocatedList(SysUser user) {
        startPage();
        List<SysUser> list = iSysUserService.selectUnallocatedList(user);
        return getDataTable(list);
    }

    /**
     * 取消授权用户
     */
    @ApiOperation("取消授权用户")
    @PostMapping("/authUser/cancel")
    public ResultBean cancelAuthUser(@RequestBody SysUserRole userRole) {
        String platId = getPlatId();
        iSysroleService.checkRoleDataScope(platId);
        return resultBean(iSysroleService.deleteAuthUser(userRole));
    }

    /**
     * 批量取消授权用户
     */
    @ApiOperation("批量取消授权用户")
    @PostMapping("/authUser/cancelAll")
    public ResultBean cancelAuthUserAll(@RequestBody UsersRole usersRole) {
        String platId = getPlatId();
        iSysroleService.checkRoleDataScope(platId);
        return resultBean(iSysroleService.deleteAuthUsers(usersRole.getRoleId(), usersRole.getUserIds()));
    }

    /**
     * 批量选择用户授权
     */
    @ApiOperation("批量选择用户授权")
    @PostMapping("/authUser/selectAll")
    public ResultBean selectAuthUserAll(@RequestBody UsersRole usersRole) {
        String platId = getPlatId();
        iSysroleService.checkRoleDataScope(platId);
        return resultBean(iSysroleService.insertAuthUsers(usersRole.getRoleId(), usersRole.getUserIds()));
    }

    /**
     * 获取对应角色分组树列表
     */
    @ApiOperation("获取对应角色分组树列表")
    @GetMapping(value = "/groupTree/query")
    public ResultBean groupTree(@ApiParam(required = true, name = "角色ID") @RequestParam Long roleId) {
        String platId = getPlatId();
        HashMap map = new HashMap();
        map.put("checkedKeys", iSysGroupService.selectGroupListByRoleId(roleId,Long.parseLong(platId)));
        map.put("groups", iSysGroupService.selectGroupTreeList(new SysGroup(Long.parseLong(platId))));
        return ResultBean.success(map);
    }

    /**
     * VACS定制接口，根据角色id获取下属角色列表
     * 逻辑：如果角色管理某几个分组，那么查询这几个分组关联的所有角色列表，并且这些角色不能有超出这些分组的权限
     */
    @ApiOperation("根据角色id获取下属角色列表")
    @GetMapping(value = "/children/query")
    public ResultBean childrenList(@ApiParam(required = true, name = "角色ID") @RequestParam Long roleId) {
        String platId = getPlatId();
        return ResultBean.success(iSysroleService.selectChildrenListByRoleId(roleId,platId));
    }

    /**
     * 晨会定制接口，根据角色id获取下属用户列表，并且去重
     */
    @ApiOperation("根据角色id获取下属用户列表")
    @GetMapping(value = "/userList")
    public ResultBean userList(@ApiParam(required = true, name = "角色IDS")Long[] roleIds) {
        String platId = getPlatId();
        return ResultBean.success(iSysroleService.selectUserListByRoleIds(roleIds,platId));
    }
}
