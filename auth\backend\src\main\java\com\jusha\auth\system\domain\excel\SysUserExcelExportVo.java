package com.jusha.auth.system.domain.excel;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

@Data
public class SysUserExcelExportVo {
    @Excel(name = "用户编号" ,orderNum = "1", width = 20)
    private String userId;
    @Excel(name = "账户名称" ,orderNum = "2", width = 40)
    private String userName;
    @Excel(name = "用户姓名" ,orderNum = "3", width = 20)
    private String nickName;
    @Excel(name = "手机号码" ,orderNum = "4", width = 20)
    private String phoneNumber;
    @Excel(name = "工号" ,orderNum = "5", width = 20)
    private String workNumber;
    @Excel(name = "创建时间" ,orderNum = "6", width = 40)
    private String createTime;


}
