package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.KeyframePic;
import com.jusha.caselibrary.mybatisplus.mapper.KeyframePicMapper;
import com.jusha.caselibrary.mybatisplus.service.KeyframePicService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 关键帧序列的关联图片 服务实现类
 * <AUTHOR>
 */
@Service
public class KeyframePicServiceImpl extends ServiceImpl<KeyframePicMapper, KeyframePic> implements KeyframePicService {

}
