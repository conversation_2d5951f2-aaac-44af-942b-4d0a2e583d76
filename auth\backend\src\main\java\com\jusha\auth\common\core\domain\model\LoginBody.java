package com.jusha.auth.common.core.domain.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "登录参数", description = "登录参数")
public class LoginBody {
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String username;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码")
    private String password;

    /**
     * 图形验证码
     */
    @ApiModelProperty(value = "图形验证码,0判断是否需要图形验证码，1不判断")
    private String verifyCode;
}
