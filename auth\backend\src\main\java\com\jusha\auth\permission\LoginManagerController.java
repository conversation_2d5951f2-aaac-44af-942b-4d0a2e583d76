package com.jusha.auth.permission;

import com.alibaba.fastjson.JSON;
import com.jusha.auth.common.annotation.NoDuplicate;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginBody;
import com.jusha.auth.common.core.domain.model.LoginCodeBody;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.utils.RSAUtil;
import com.jusha.auth.common.utils.ServletUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.core.service.SysLoginService;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.monitor.factory.RecordFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletResponse;
import java.security.PrivateKey;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "登录登出")
@RestController
@RequestMapping("/open")
public class LoginManagerController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(LoginManagerController.class);
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RecordFactory recordFactory;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取公钥,并将对应的公钥私钥存储到redis
     **/
    @GetMapping("/getKeys")
    @NoDuplicate(keys = "'getKeys'" ,waitTag = 2)
    public ResultBean getKeys(){
        String rsaKeyPublic = redisCache.getCacheObject(Constants.SYS_RSA_KEY+Constants.RSA_KEY_PUBLIC);
        if(StringUtils.isNotEmpty(rsaKeyPublic)){
            redisCache.setCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PUBLIC, rsaKeyPublic, 1, TimeUnit.MINUTES);
            String privateKeyStr = redisCache.getCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PRIVATE).toString();
            redisCache.setCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PRIVATE, privateKeyStr, 1, TimeUnit.MINUTES);
            return ResultBean.success().put(ResultBean.DATA_TAG, rsaKeyPublic);
        }else{
            String publicKey = RSAUtil.generateBase64PublicKey();
            PrivateKey privateKey = RSAUtil.keyPair.getPrivate();
            String privateKeyStr = new String(Base64.encodeBase64(privateKey.getEncoded()));
            redisCache.setCacheObject(Constants.SYS_RSA_KEY+Constants.RSA_KEY_PUBLIC, publicKey, 1, TimeUnit.MINUTES);
            redisCache.setCacheObject(Constants.SYS_RSA_KEY+Constants.RSA_KEY_PRIVATE, privateKeyStr, 1, TimeUnit.MINUTES);
            return ResultBean.success().put(ResultBean.DATA_TAG, publicKey);
        }
    }

    /**
     * 发送验证码，有效期5分钟
     * @param
     * @return 结果
     */
    @ApiOperation("发送验证码，有效期5分钟")
    @GetMapping("/sendMsg")
    public ResultBean sendMsg(String userId, String phoneNumber, @RequestParam(required = true) String msgType){
        return loginService.sendMsg(userId,phoneNumber,msgType);
    }

    /**
     * 登录方法
     * @param loginBody 登录信息
     * @return 结果
     */
    @ApiOperation("登录")
    @PostMapping("/login")
    public ResultBean login(@Validated @RequestBody LoginBody loginBody) throws Exception{
        log.info("用户准备验证码登录，用户名========"+loginBody.getUsername());
        // 生成令牌
        String token = loginService.login(loginBody);
        return ResultBean.success().put(ResultBean.DATA_TAG, token);
    }

    /**
     * 验证码登录
     * @param loginCodeBody 登录信息
     * @return 结果
     */
    @ApiOperation("验证码登录")
    @PostMapping("/loginCode")
    public ResultBean loginCode(@Validated @RequestBody LoginCodeBody loginCodeBody){
        log.info("用户准备验证码登录，手机号========"+loginCodeBody.getPhoneNumber());
        // 生成令牌
        String token = loginService.loginCode(loginCodeBody);
        return ResultBean.success().put(ResultBean.DATA_TAG, token);
    }

    /**
     * 登录失败时，检查登录状态（获取userId和脱敏手机号）
     * @param
     * @return 结果
     */
    @ApiOperation("登录失败时，检查登录状态（获取userId和脱敏手机号）")
    @PostMapping("/checkLoginStatus")
    public ResultBean checkLoginStatus(@Validated @RequestBody LoginBody loginBody){
        return ResultBean.success(loginService.checkLoginStatus(loginBody.getUsername()));
    }

    @ApiOperation("登出")
    @PostMapping("/logout")
    public void logout(HttpServletResponse response) {
        LoginUser loginUser = getLoginUserCanNull();
        if (StringUtils.isNotNull(loginUser)) {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            recordFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功");
            //把菜单缓存一起删除了
            if(!redisCache.keys(Constants.MENU_ROUTER + loginUser.getUserId() + "*").isEmpty()){
                Collection<String> keys = redisCache.keys(Constants.MENU_ROUTER + loginUser.getUserId() + "*");
                if(!keys.isEmpty()){
                    for (String key : keys) {
                        redisCache.deleteObject(key);
                    }
                }
            }
        }
        ServletUtils.renderString(response, JSON.toJSONString(ResultBean.success("退出成功")));
    }

    @PostMapping("/logout/forbid")
    public void logout(@RequestParam("userId") Long userId, @RequestParam("token") String token,
                       @RequestParam("username") String username) {
        if (StringUtils.isNotNull(token)) {
            // 删除用户缓存记录
            tokenService.delLoginUser(token);
            // 记录用户退出日志
            recordFactory.recordLogininfor(username, Constants.LOGOUT, "退出成功");
            //把菜单缓存一起删除了
            if(!redisCache.keys(Constants.MENU_ROUTER + userId + "*").isEmpty()){
                Collection<String> keys = redisCache.keys(Constants.MENU_ROUTER + userId + "*");
                if(!keys.isEmpty()){
                    for (String key : keys) {
                        redisCache.deleteObject(key);
                    }
                }
            }
        }
    }
}
