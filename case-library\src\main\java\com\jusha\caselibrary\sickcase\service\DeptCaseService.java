package com.jusha.caselibrary.sickcase.service;

import com.github.pagehelper.PageInfo;
import com.jusha.caselibrary.sickcase.dto.req.DeptCaseSearchReq;
import com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp;

import java.util.List;

/**
 * @ClassName DeptCaseService
 * @Description 科室病例管理服务接口
 * <AUTHOR>
 * @Date 2025/7/10 09:31
 **/
public interface DeptCaseService {

    /**
     * @description 科室病例库详细列表
     * <AUTHOR>
     * @date 2025/7/10 10:59
     * @param req
     * @return List<DeptCaseDetailResp>
     **/
    List<DeptCaseDetailResp> getDeptCaseDetailList(DeptCaseSearchReq req);

    /**
     * @description 科室病例库详细列表分页
     * <AUTHOR>
     * @date 2025/7/10 11:39
     * @param req
     * @return PageInfo<DeptCaseDetailResp>
     **/
    PageInfo<DeptCaseDetailResp> getDeptCaseDetailPage(DeptCaseSearchReq req);

    /**
     * @description 科室病例库病例详情查询
     * <AUTHOR>
     * @date 2025/7/10 11:39
     * @param caseId
     * @return DeptCaseDetailResp
     **/
    DeptCaseDetailResp getDeptCaseDetail(Long caseId);

    /**
     * @description 科室病例库病例删除
     * <AUTHOR>
     * @date 2025/7/10 11:47
     * @param caseId
     * @return void
     **/
    void deleteDeptCaseById(Long caseId);
}
