package com.jusha.caselibrary.search.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import com.jusha.caselibrary.search.dto.AdvancedSearchResponse;
import com.jusha.caselibrary.search.service.SearchCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName SearchCacheServiceImpl
 * @Description 搜索缓存服务实现类
 * <AUTHOR>
 * @Date 2025/7/9 17:05
 **/
@Slf4j
@Service
public class SearchCacheServiceImpl implements SearchCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SearchConfig searchConfig;

    @Autowired
    private ObjectMapper objectMapper;

    // 缓存统计
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    private final AtomicLong evictionCount = new AtomicLong(0);

    @Override
    public AdvancedSearchResponse<?> getCachedResult(AdvancedSearchRequest request) {
        if (isCacheEnabled(request)) {
            return null;
        }

        String cacheKey = request.getCacheKey();
        if (!StringUtils.hasText(cacheKey)) {
            return null;
        }

        try {
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue != null) {
                hitCount.incrementAndGet();
                log.debug("缓存命中，键: {}", cacheKey);
                
                if (cachedValue instanceof String) {
                    return objectMapper.readValue((String) cachedValue, AdvancedSearchResponse.class);
                } else {
                    return (AdvancedSearchResponse<?>) cachedValue;
                }
            } else {
                missCount.incrementAndGet();
                log.debug("缓存未命中，键: {}", cacheKey);
                return null;
            }
        } catch (Exception e) {
            log.error("获取缓存失败，键: {}", cacheKey, e);
            missCount.incrementAndGet();
            return null;
        }
    }

    @Override
    public void cacheResult(AdvancedSearchRequest request, AdvancedSearchResponse<?> response) {
        if (isCacheEnabled(request) || response == null || !response.getSuccess()) {
            return;
        }

        String cacheKey = request.getCacheKey();
        if (!StringUtils.hasText(cacheKey)) {
            return;
        }

        try {
            int expireSeconds = request.getCacheExpireSeconds() != null ? 
                request.getCacheExpireSeconds() : 
                searchConfig.getCache().getDefaultExpireSeconds();

            // 根据配置选择缓存方式
            if (searchConfig.getCache().getEnableCompression()) {
                // 使用JSON字符串压缩存储
                String jsonValue = objectMapper.writeValueAsString(response);
                redisTemplate.opsForValue().set(cacheKey, jsonValue, expireSeconds, TimeUnit.SECONDS);
            } else {
                // 直接存储对象
                redisTemplate.opsForValue().set(cacheKey, response, expireSeconds, TimeUnit.SECONDS);
            }

            log.debug("缓存搜索结果，键: {}, 过期时间: {}秒", cacheKey, expireSeconds);
        } catch (Exception e) {
            log.error("缓存搜索结果失败，键: {}", cacheKey, e);
        }
    }

    @Override
    public void evictCache(String cacheKey) {
        if (!StringUtils.hasText(cacheKey)) {
            return;
        }

        try {
            Boolean deleted = redisTemplate.delete(cacheKey);
            if (deleted) {
                evictionCount.incrementAndGet();
                log.debug("清除缓存成功，键: {}", cacheKey);
            }
        } catch (Exception e) {
            log.error("清除缓存失败，键: {}", cacheKey, e);
        }
    }

    @Override
    public void evictAllCache() {
        try {
            String pattern = searchConfig.getCache().getKeyPrefix() + "*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.info("清除所有搜索缓存成功");
        } catch (Exception e) {
            log.error("清除所有搜索缓存失败", e);
        }
    }

    @Override
    public CacheStats getCacheStats() {
        long hits = hitCount.get();
        long misses = missCount.get();
        long total = hits + misses;
        double hitRate = total > 0 ? (double) hits / total : 0.0;
        
        // 获取缓存大小（近似值）
        long cacheSize = 0;
        try {
            String pattern = searchConfig.getCache().getKeyPrefix() + "*";
            cacheSize = redisTemplate.keys(pattern).size();
        } catch (Exception e) {
            log.warn("获取缓存大小失败", e);
        }

        return new CacheStats(hits, misses, evictionCount.get(), hitRate, cacheSize);
    }

    /**
     * 判断是否启用缓存
     */
    private boolean isCacheEnabled(AdvancedSearchRequest request) {
        return !searchConfig.getCache().getEnabled() ||
                request.getEnableCache() == null ||
                !request.getEnableCache();
    }

    /**
     * 预热缓存（可以在系统启动时调用）
     */
    public void warmUpCache() {
        log.info("开始预热搜索缓存...");
        
        // 这里可以添加一些常用查询的预热逻辑
        // 例如：热门疾病、常用标签等的搜索结果
        
        log.info("搜索缓存预热完成");
    }

    /**
     * 清理过期缓存（定时任务）
     */
    public void cleanExpiredCache() {
        log.debug("开始清理过期缓存...");
        
        try {
            // Redis会自动清理过期键，这里主要是统计和日志
            CacheStats stats = getCacheStats();
            log.info("缓存统计 - 命中: {}, 未命中: {}, 命中率: {:.2f}%, 缓存大小: {}", 
                stats.getHitCount(), stats.getMissCount(), 
                stats.getHitRate() * 100, stats.getCacheSize());
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
        }
    }

    /**
     * 根据查询复杂度动态调整缓存策略
     */
    public int getOptimalCacheExpireTime(AdvancedSearchRequest request) {
        int baseExpireTime = searchConfig.getCache().getDefaultExpireSeconds();
        
        // 复杂查询缓存时间更长
        if (request.isComplexQuery()) {
            return baseExpireTime * 2;
        }
        
        // 简单查询缓存时间较短
        if (request.isEmpty() || (request.getKeyword() != null && request.getKeyword().length() < 3)) {
            return baseExpireTime / 2;
        }
        
        return baseExpireTime;
    }

    @Override
    public void recordQueryStats(AdvancedSearchRequest request, long took, Long total) {
        try {
            // 记录查询统计信息到Redis
            String statsKey = "search:stats:" + request.getSearchType();
            String timestamp = String.valueOf(System.currentTimeMillis());
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("took", took);
            stats.put("total", total != null ? total : 0L);
            stats.put("timestamp", timestamp);
            stats.put("keyword", request.getKeyword());
            stats.put("complex", request.isComplexQuery());
            
            // 使用Hash存储统计信息
            redisTemplate.opsForHash().putAll(statsKey + ":" + timestamp, stats);
            
            // 设置过期时间（7天）
            redisTemplate.expire(statsKey + ":" + timestamp, Duration.ofDays(7));
            
            log.debug("记录查询统计信息: 耗时={}ms, 结果数={}", took, total);
        } catch (Exception e) {
            log.warn("记录查询统计信息失败", e);
        }
    }
}