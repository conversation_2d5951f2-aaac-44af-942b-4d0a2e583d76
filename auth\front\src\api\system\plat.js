import request from '@/utils/request'

// 查询业务平台信息列表
export function listPlat(query) {
  return request({
    url: '/system/plat/list',
    method: 'get',
    params: query
  })
}

// 查询业务平台信息详细
export function getPlat(platId) {
  return request({
    url: '/system/plat/query?platId=' + platId,
    method: 'get'
  })
}

// 新增业务平台信息
export function addPlat(data) {
  return request({
    url: '/system/plat/add',
    method: 'post',
    data: data
  })
}

// 修改业务平台信息
export function updatePlat(data) {
  return request({
    url: '/system/plat/edit',
    method: 'post',
    data: data
  })
}

// 删除业务平台信息
export function delPlat(platId) {
  return request({
    url: '/system/plat/remove?platId=' + platId,
    method: 'post'
  })
}



//平台可见状态修改
export function changePlatVisible(platId, visible) {
  const data = {
    platId,
    visible
  }
  return request({
    url: '/system/plat/changeVisible',
    method: 'post',
    data: data
  })
}