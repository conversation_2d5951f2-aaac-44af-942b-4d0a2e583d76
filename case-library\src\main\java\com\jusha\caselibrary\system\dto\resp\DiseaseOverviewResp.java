package com.jusha.caselibrary.system.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

/**
 * 疾病概述响应
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DiseaseOverviewResp {

    @ApiModelProperty(value = "疾病id")
    private Long diseaseId;

    @ApiModelProperty(value = "概述")
    private String overview;

    @ApiModelProperty(value = "病理表现")
    private String pathology;

    @ApiModelProperty(value = "临床表现")
    private String clinical;

    @ApiModelProperty(value = "影像学表现")
    private String imaging;

    @ApiModelProperty(value = "诊断要点")
    private String diagnosis;

    @ApiModelProperty(value = "鉴别诊断")
    private String differential;

    @ApiModelProperty(value = "关键帧")
    private List<String> keyframe;
}
