package com.jusha.caselibrary.sickcase.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName CatalogOperationRequest
 * @Description 目录操作请求实体
 * <AUTHOR>
 * @Date 2025/7/11
 **/
@Data
public class CatalogOperationRequest {

    @ApiModelProperty(value = "病例ID", required = true)
    @NotNull(message = "病例ID不能为空")
    private Long caseId;

    @ApiModelProperty(value = "目录ID", required = true)
    @NotNull(message = "目录ID不能为空")
    private Long catalogId;
}