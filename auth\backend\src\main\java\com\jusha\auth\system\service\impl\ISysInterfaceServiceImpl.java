package com.jusha.auth.system.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.SysInterface;
import com.jusha.auth.mybatisplus.entity.SysMenu;
import com.jusha.auth.mybatisplus.entity.SysMenuInterface;
import com.jusha.auth.mybatisplus.service.SysInterfaceService;
import com.jusha.auth.mybatisplus.service.SysMenuInterfaceService;
import com.jusha.auth.mybatisplus.service.SysMenuService;
import com.jusha.auth.system.service.ISysInterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 接口Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-08
 */
@Service
public class ISysInterfaceServiceImpl implements ISysInterfaceService {

    @Autowired
    private SysInterfaceService sysInterfaceService;

    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private SysMenuInterfaceService sysMenuInterfaceService;

    @Autowired
    private TokenService tokenService;

    /**
     * 查询接口
     * 
     * @param interfaceId 接口主键
     * @return 接口
     */
    @Override
    public SysInterface selectSysInterfaceByInterfaceId(Long interfaceId) {
        return sysInterfaceService.getById(interfaceId);
    }

    /**
     * 查询接口列表
     * 
     * @param sysInterface 接口
     * @return 接口
     */
    @Override
    public List<SysInterface> selectSysInterfaceList(SysInterface sysInterface) {
        LambdaQueryChainWrapper<SysInterface> wrapper = sysInterfaceService.lambdaQuery();
        if(sysInterface.getInterfaceName() !=null){
            wrapper.like(SysInterface::getInterfaceName, sysInterface.getInterfaceName());
        }
        if(sysInterface.getInterfacePath() !=null){
            wrapper.like(SysInterface::getInterfacePath, sysInterface.getInterfacePath());
        }
        if(sysInterface.getPlatId() !=null){
            wrapper.eq(SysInterface::getPlatId, sysInterface.getPlatId());
        }
        return wrapper.orderByDesc(SysInterface::getCreateTime).list();
    }

    /**
     * 新增接口
     * 
     * @param sysInterface 接口
     * @return 结果
     */
    @Override
    public boolean insertSysInterface(SysInterface sysInterface) {
        sysInterface.setInterfaceId(YitIdHelper.nextId());
        sysInterface.setCreateTime(DateUtils.getNowDate());
        sysInterface.setCreateBy(tokenService.getUserId());
        return sysInterfaceService.save(sysInterface);
    }

    /**
     * 修改接口
     * 
     * @param sysInterface 接口
     * @return 结果
     */
    @Override
    public boolean updateSysInterface(SysInterface sysInterface) {
        sysInterface.setUpdateBy(tokenService.getUserId());
        sysInterface.setUpdateTime(DateUtils.getNowDate());
        return sysInterfaceService.updateById(sysInterface);
    }

    /**
     * 删除接口信息
     * 
     * @param interfaceId 接口主键
     * @return 结果
     */
    @Override
    public boolean deleteSysInterfaceByInterfaceId(Long interfaceId) {
        return sysInterfaceService.lambdaUpdate()
                .set(SysInterface::getUpdateBy,tokenService.getUserId())
                .set(SysInterface::getUpdateTime,DateUtils.getNowDate())
                .set(SysInterface::getDelFlag, Constants.DELETE_FLAG)
                .eq(SysInterface::getInterfaceId, interfaceId).update();
    }

    @Override
    public boolean checkInterfaceNameUnique(SysInterface sysInterface) {
        Long id = StringUtils.isNull(sysInterface.getInterfaceId()) ? Constants.ALL_MINUS1L : sysInterface.getInterfaceId();
        List<SysInterface> groupInfos = sysInterfaceService.lambdaQuery()
                .eq(SysInterface::getInterfaceName, sysInterface.getInterfaceName())
                .eq(SysInterface::getPlatId, sysInterface.getPlatId()).list();
        if (!groupInfos.isEmpty() && groupInfos.get(0).getInterfaceId().longValue() != id.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    @Override
    public boolean checkInterfacePathUnique(SysInterface sysInterface) {
        Long id = StringUtils.isNull(sysInterface.getInterfaceId()) ? Constants.ALL_MINUS1L : sysInterface.getInterfaceId();
        List<SysInterface> groupInfos = sysInterfaceService.lambdaQuery()
                .eq(SysInterface::getInterfacePath, sysInterface.getInterfacePath())
                .eq(SysInterface::getPlatId, sysInterface.getPlatId()).list();
        if (!groupInfos.isEmpty() && groupInfos.get(0).getInterfaceId().longValue() != id.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    @Override
    public String checkInterfaceMenu(Long interfaceId) {
        List<SysMenuInterface> menuInterfaceList = sysMenuInterfaceService.lambdaQuery().eq(SysMenuInterface::getInterfaceId,interfaceId).list();
        if (menuInterfaceList.isEmpty()) {
            return "";
        }
        //获取menuInterfaceList 中所有的菜单地址
        List<Long> menuList = menuInterfaceList.stream().map(SysMenuInterface::getMenuId).collect(Collectors.toList());
        String menuNames = "";
        List<SysMenu> menus = sysMenuService.lambdaQuery().in(SysMenu::getMenuId, menuList).list();
        for (SysMenu sysMenu : menus) {
            menuNames += sysMenu.getMenuName() + ",";
        }
        return menuNames;
    }
}
