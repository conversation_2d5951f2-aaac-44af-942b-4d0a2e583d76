package com.jusha.auth.common.core.service;

import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.constant.UserStatus;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginBody;
import com.jusha.auth.common.core.domain.model.LoginCodeBody;
import com.jusha.auth.common.core.domain.model.LoginStatus;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.exception.HttpStatus;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.RSAUtil;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.utils.ip.IpUtils;
import com.jusha.auth.common.utils.sms.SmsUtils;
import com.jusha.auth.monitor.factory.RecordFactory;
import com.jusha.auth.monitor.service.ISysConfigService;
import com.jusha.auth.mybatisplus.entity.SysGroup;
import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysRoleGroup;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.mybatisplus.service.SysGroupService;
import com.jusha.auth.mybatisplus.service.SysRoleGroupService;
import com.jusha.auth.mybatisplus.service.SysUserService;
import com.jusha.auth.system.service.ISysGroupService;
import com.jusha.auth.system.service.ISysMenuService;
import com.jusha.auth.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.PrivateKey;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysLoginService {

    private static final Logger log = LoggerFactory.getLogger(SysLoginService.class);

    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RecordFactory recordFactory;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private ISysMenuService iSysmenuService;

    @Autowired
    private ISysGroupService iSysGroupService;

    @Autowired
    private SysGroupService sysGroupService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysRoleGroupService sysRoleGroupService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SmsUtils smsUtils;

    @Value("${token.network:}")
    private String network;

    @Value("${token.msgcode:}")
    private String msgCode;

    @Value("${token.exclusive:}")
    private String exclusive;

    /**
     * 短信验证码登录
     * @param loginCodeBody 用户id
     */
    public String loginCode(LoginCodeBody loginCodeBody){
        SysUser user = null;
        if(loginCodeBody.getUserId() != null){
            user = sysUserService.getById(loginCodeBody.getUserId());
            if(user==null){
                throw new ServiceException(MessageUtils.message("userId.not.exist"));
            }
        }
        String phoneNumber = loginCodeBody.getPhoneNumber()!=null?loginCodeBody.getPhoneNumber():user.getPhoneNumber();
        String codeRedis = redisCache.getCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + Constants.SMS_MEG_TYPE_LOGIN + Constants.CODE);
        if(codeRedis == null){
            recordFactory.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, MessageUtils.message("msg.code.expire"));
            throw new ServiceException(MessageUtils.message("msg.code.expire"));
        }
        if(!codeRedis.equals(loginCodeBody.getCode())){
            recordFactory.recordLogininfor(phoneNumber, Constants.LOGIN_FAIL, MessageUtils.message("msg.code.expire"));
            throw new ServiceException(MessageUtils.message("msg.code.expire"));
        }
        SysUser sysUser = userService.selectUserByPhoneNumber(phoneNumber);
        if(sysUser==null){
            throw new ServiceException(MessageUtils.message("userId.not.exist"));
        }
        recordLoginRightsInfo(sysUser);
        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser, iSysmenuService.selectIterfacePathsByUserId(sysUser,null));
        recordLoginInfo(loginUser.getUserId());
        //登录成功以后，要把验证码给删了
        redisCache.deleteObject(Constants.SMS_MEG_PHONE + phoneNumber + Constants.SMS_MEG_TYPE_LOGIN + Constants.CODE);
        // 生成token
        String token = tokenService.createToken(loginUser);
        exclusiveLogin(token);
        return token;
    }

    private void exclusiveLogin(String token){
        String userId = token.split(":")[1];
        if("0".equals(exclusive)){
            log.info("排他登录========");
            //根据配置看是否要把非本次登录的其他登录全部删除
            Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + userId + ":*");
            if(!keys.isEmpty()){
                for (String key : keys) {
                    if(!key.equals(token)){
                        redisCache.deleteObject(key);
                    }
                }
            }
        }
    }

    /**
     * 发短信
     * @param userId 号码
     * @param msgType 短信类型，login登录用，password重置密码用
     */
    public ResultBean sendMsg(String userId,String phoneNumber,String msgType){
        if(phoneNumber==null){
            SysUser user = sysUserService.getById(userId);
            phoneNumber = user.getPhoneNumber();
        }
        //首先判断一下，这个号码最近10分钟发送几次短信了，超过5次就不让发了
        Integer count10 = redisCache.getCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + msgType + Constants.COUNT_10_MIN);
        if(count10!=null && count10 >= 5){
            return ResultBean.error(MessageUtils.message("msg.code.toomuch"));
        }
        //再判断一下，这个号码最近1分钟是否发送过短信了，发过了就不让发了
        Integer count1 = redisCache.getCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + msgType + Constants.COUNT_1_MIN);
        if(count1!=null && count1 >= 1){
            return ResultBean.error(MessageUtils.message("msg.code.repeat"));
        }
        //首先判断一下，这个号码最近1天发送几次短信了，超过20次就不让发了
        Integer countDay = redisCache.getCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + msgType + Constants.COUNT_1_DAY);
        if(countDay!=null && countDay >= 20){
            return ResultBean.error(MessageUtils.message("msg.code.runoff"));
        }
        //随机生成6位数字，发送给这个手机号，然后记入redis，有效期5分钟
        String code = String.format("%06d", new Random().nextInt(100000));
        //记录最新的验证码，有效期5分钟
        redisCache.setCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + msgType + Constants.CODE, code, 5, TimeUnit.MINUTES);
        //记录最近10分钟的发送次数，超过5次就不让再发送了
        redisCache.setCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + msgType + Constants.COUNT_10_MIN,count10!=null?count10+1:1,10,TimeUnit.MINUTES);
        //记录最近1分钟的发送次数，1分钟内发送过就不让再发送了
        redisCache.setCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + msgType + Constants.COUNT_1_MIN,count1!=null?count1+1:1,1, TimeUnit.MINUTES);
        //记录最近1天的发送次数，超过20次就不让再发送了
        redisCache.setCacheObject(Constants.SMS_MEG_PHONE + phoneNumber + msgType + Constants.COUNT_1_DAY,countDay!=null?countDay+1:1,1, TimeUnit.DAYS);
        smsUtils.sendMsg(phoneNumber,msgType,code);
        return ResultBean.success();
    }

    /**
     * 检查登录状态
     * @param username 用户名
     */
    public LoginStatus checkLoginStatus(String username){
        List<SysUser> sysUsers = userService.selectUserByUserNamePhoneWork(username);
        if(sysUsers.isEmpty()){
            throw new ServiceException(MessageUtils.message("userId.not.exist"));
        }
        if(sysUsers.size()>1){
            throw new ServiceException(MessageUtils.message("userId.too.much"));
        }
        long userId = sysUsers.get(0).getUserId();
        String phone = sysUsers.get(0).getPhoneNumber();
        String phoneNumber = phone.substring(0, 3)+"****"+phone.substring(7, 11);
        return new LoginStatus(userId, phoneNumber);
    }

    /**
     * 登录
     * @param loginBody 用户名，密码
     */
    public String login(LoginBody loginBody) throws Exception{
        String password = loginBody.getPassword();
        String username = loginBody.getUsername();
        PrivateKey privateKey = RSAUtil.loadPrivateKey(redisCache.getCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PRIVATE).toString());
        password = RSAUtil.decryptBase64WithPrivate(password.trim(), privateKey);
        // 登录前置校验
        loginPreCheck(username, password);
        List<SysUser> sysUsers = userService.selectUserByUserNamePhoneWork(username);
        if (sysUsers.isEmpty()) {
            log.info("登录用户：{} 不存在.", username);
            recordFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists"));
            throw new ServiceException(MessageUtils.message("user.password.not.match"));
        }else if (sysUsers.size() > 1) {
            log.info("登录用户：{} 凭证不唯一.", username);
            recordFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("userId.too.much"));
            throw new ServiceException(MessageUtils.message("userId.too.much"));
        } else if (UserStatus.DISABLE.getCode().equals(sysUsers.get(0).getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            recordFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.has.been.stopped"));
            throw new ServiceException(MessageUtils.message("user.password.not.match"));
        }
        SysUser sysUser = sysUsers.get(0);
        passwordService.validate(sysUser,password);
        if(network.equals("WAN") && !msgCode.isEmpty()){
            String ip = IpUtils.getIpAddr();
            if(sysUser.getLoginIp().isEmpty() || sysUser.getLoginDate()==null){
                throw new ServiceException(MessageUtils.message("login.ip.first"), HttpStatus.ENVIRONMENT);
            }
            if(!sysUser.getLoginIp().equals(ip)){
                throw new ServiceException(MessageUtils.message("login.ip.change"), HttpStatus.ENVIRONMENT);
            }
            if(redisCache.getCacheObject(Constants.SYS_CONFIG_KEY+Constants.VERIFY_CODE).equals("0")){
                if(loginBody.getVerifyCode()!=null && loginBody.getVerifyCode().equals("0")){
                    String time = redisCache.getCacheObject(Constants.SYS_CONFIG_KEY + Constants.VERIFY_CODE_TIME);
                    if(DateUtils.miniteDistance(new Date(),sysUser.getLoginDate()) >= Long.parseLong(time)){
                        throw new ServiceException(MessageUtils.message("verify.code.first"), HttpStatus.FORBIDDEN);
                    }
                }
            }
        }

        recordFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        recordLoginRightsInfo(sysUser);
        LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser, iSysmenuService.selectIterfacePathsByUserId(sysUser,null));
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        String token = tokenService.createToken(loginUser);
        exclusiveLogin(token);
        return token;
    }

    /**
     * 记录权限相关数据
     * @param sysUser 用户名
     */
    private void recordLoginRightsInfo(SysUser sysUser){
        //把权限数据一起放到loginUser中
        if(sysUser.getRoles() == null){
            throw new ServiceException(MessageUtils.message("set.permission.first"));
        }
        List<SysRole> roles = sysUser.getRoles();
        for(SysRole role : roles){
            List<Long> groupList = iSysGroupService.selectGroupListByRoleId(role.getRoleId(),role.getPlatId());
            role.setGroupList(groupList);
        }
        for(SysRole role : roles){
            List<Long> groupList = iSysGroupService.selectGroupListByRoleId(role.getRoleId(),role.getPlatId());
            //拿联盟id简单，都一样。
            for(Long groupId : groupList){
                SysGroup group = sysGroupService.getById(groupId);
                if(group==null){
                    continue;
                }
                SysGroup parentGroup = sysGroupService.getById(group.getParentId());
                if(parentGroup == null){
                    continue;
                }
                SysGroup grandPrentGroup = sysGroupService.getById(parentGroup.getParentId());
                if(grandPrentGroup == null){
                    continue;
                }
                if(grandPrentGroup.getParentId()!=null && grandPrentGroup.getParentId()==0L){
                    role.setAllianceId(parentGroup.getGroupId());
                    break;
                }
            }
            //拿医院id要分情况，如果这个角色是中心医院管理员，那么需要拿到所有末级分组中的中心医院的id。
            //如果不是中心医院的管理员，那么拿到末级分组中的一个就可以了（默认一个角色只能属于一个医院，一个用户可以拥有多个角色）
            List<SysRoleGroup> roleGroupList = sysRoleGroupService.lambdaQuery().eq(SysRoleGroup::getRoleId,role.getRoleId()).list();
            List<Long> groupIdList = roleGroupList.stream().map(SysRoleGroup::getGroupId).collect(Collectors.toList());
            List<Long> parentIdList = sysGroupService.lambdaQuery().list().stream().map(SysGroup::getParentId).collect(Collectors.toList());
            if(!groupIdList.isEmpty() && !parentIdList.isEmpty()){
                List<SysGroup> sysGroups = sysGroupService.lambdaQuery().in(SysGroup::getGroupId,groupIdList).notIn(SysGroup::getGroupId,parentIdList).list();
                if(!sysGroups.isEmpty()) {
                    if ("1".equals(role.getRoleType())) {
                        int centerFlag = 0;  //初始0代表非中心，如果经历循环还是0代表虽然是管理员但是是非中心管理员，那就不用管了直接赋值，否则需要找到中心分组给他赋值
                        for (SysGroup group : sysGroups) {
                            if("0".equals(group.getCenter())){
                                centerFlag = 1;
                                role.setSysGroupId(group.getGroupId());
                                break;
                            }
                        }
                        if(centerFlag ==0 ){
                            role.setSysGroupId(sysGroups.get(0).getGroupId());
                        }
                    } else {
                        role.setSysGroupId(sysGroups.get(0).getGroupId());
                    }
                }
            }
        }
        sysUser.setRoles(roles);
    }

    /**
     * 登录前置校验
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            recordFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists"));
            throw new ServiceException(MessageUtils.message("user.password.not.match"));

        }
        // 密码如果不在指定范围内 错误
        if (password.length() < Constants.PASSWORD_MIN_LENGTH || password.length() > Constants.PASSWORD_MAX_LENGTH) {
            recordFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match"));
            throw new ServiceException(MessageUtils.message("user.password.not.match"));
        }
        // 用户名不在指定范围内 错误
        if (username.length() < Constants.USERNAME_MIN_LENGTH || username.length() > Constants.USERNAME_MAX_LENGTH) {
            recordFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match"));
            throw new ServiceException(MessageUtils.message("user.password.not.match"));
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked"));
            throw new ServiceException(MessageUtils.message("login.blocked"));
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }
}
