package com.jusha.auth.permission;

import com.jusha.auth.common.annotation.NoDuplicate;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.core.service.SysPasswordService;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.system.domain.ForgetReset;
import com.jusha.auth.system.domain.ModifyUserPhone;
import com.jusha.auth.system.domain.PasswordReset;
import com.jusha.auth.system.service.ISysRoleService;
import com.jusha.auth.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/forbid/user")
public class UserManagerController extends BaseController {

    @Autowired
    private ISysUserService iSysuserService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @ApiOperation("获取当前登录用户信息")
    @GetMapping("/getUserInfo")
    public ResultBean getUserInfo() {
        SysUser user = tokenService.getLoginUser().getSysUser();
        String platId = getPlatId();
        List<SysRole> myRoles = new ArrayList<>();
        List<SysRole> userRoles = user.getRoles();
        for(SysRole role : userRoles) {
            if((role.getPlatId()+"").equals(platId) || (role.getRoleId()==1L)){
                myRoles.add(role);
            }
        }
        user.setRoles(myRoles);
        return ResultBean.success(user);
    }

    @ApiOperation("修改当前登录用户自己的密码")
    @PostMapping(value = "/updatePwd")
    public ResultBean updatePwd(@RequestBody PasswordReset passwordReset) throws Exception{
        return iSysuserService.resetUserPwd(passwordReset);
    }

    @ApiOperation("忘记密码（修改当前未登录用户自己的密码）")
    @PostMapping(value = "/forgetPwd")
    public ResultBean forgetPwd(@RequestBody ForgetReset forgetReset) throws Exception{
        return iSysuserService.forgetPwd(forgetReset);
    }

    @ApiOperation("修改当前登录用户自己的手机号")
    @PostMapping(value = "/editMyPhone")
    public ResultBean editMyPhone(@RequestBody ModifyUserPhone modifyUserPhone) {
        LoginUser loginUser = getLoginUser();
        return iSysuserService.editMyPhone(modifyUserPhone,loginUser);
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户")
    @PostMapping(value = "/add")
    @NoDuplicate(keys = {"#user.UserName"},waitTag = 2)
    public ResultBean add(@Validated @RequestBody SysUser user) {
        if (StringUtils.isEmpty(user.getUserName())){
            return error(MessageUtils.message("user.can.not.null"));
        }
        if (StringUtils.isEmpty(user.getPassword())){
            return error(MessageUtils.message("password.can.not.null"));
        }
        if (StringUtils.isEmpty(user.getNickName())){
            return error(MessageUtils.message("nickName.can.not.null"));
        }
        if (!iSysUserService.checkUserNameUnique(user)) {
            return error(MessageUtils.message("user.already.exist.add"));
        }
        if (StringUtils.isNotEmpty(user.getPhoneNumber()) && !iSysUserService.checkPhoneUnique(user)) {
            return error(MessageUtils.message("user.phone.already.exist.add"));
        }
        if (StringUtils.isNotEmpty(user.getWorkNumber()) && !iSysUserService.checkWorkNumberUnique(user)) {
            return error(MessageUtils.message("user.worknumber.already.exist.add"));
        }
        if (StringUtils.isEmpty(user.getPhoneNumber()) && StringUtils.isEmpty(user.getWorkNumber())) {
            return error(MessageUtils.message("user.worknumber.phone.both.not.exist"));
        }
        return success(iSysUserService.insertUser(user));
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户，如果存在则返回对象")
    @PostMapping(value = "/addExist")
    @NoDuplicate(keys = {"#user.UserName"},waitTag = 2)
    public ResultBean addExist(@Validated @RequestBody SysUser user) {
        if (!iSysUserService.checkUserNameUnique(user)) {
            iSysUserService.updateUserByUserName(user);
            return ResultBean.success(iSysUserService.selectUserByUserName(user.getUserName()));
        }
        if (StringUtils.isNotEmpty(user.getPhoneNumber()) && !iSysUserService.checkPhoneUnique(user)) {
            iSysUserService.updateUserByPhoneNumber(user);
            return ResultBean.success(iSysUserService.selectUserByPhoneNumber(user.getPhoneNumber()));
        }
        if (StringUtils.isNotEmpty(user.getWorkNumber()) && !iSysUserService.checkWorkNumberUnique(user)) {
            iSysUserService.updateUserByWorkNumber(user);
            return ResultBean.success(iSysUserService.selectUserByWorkNumber(user.getWorkNumber()));
        }
        if (StringUtils.isEmpty(user.getPhoneNumber()) && StringUtils.isEmpty(user.getWorkNumber())) {
            return error(MessageUtils.message("user.worknumber.phone.both.not.exist"));
        }
        return success(iSysUserService.insertUser(user));
    }

    /**
     * 获取用户列表
     */
    @ApiOperation("获取用户列表")
    @GetMapping("/list")
    public ResultBean list(SysUser user) {
        List<SysUser> list = iSysUserService.selectUserList(user);
        return ResultBean.success(list);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @ApiOperation("获取用户详细信息")
    @GetMapping(value = "/query")
    public ResultBean getInfo(@ApiParam(required = true, name = "用户ID") @RequestParam Long userId) {
        String platId = getPlatId();
        SysUser sysUser = iSysUserService.selectUserById(userId,Long.parseLong(platId));
        List<SysRole> roles = roleService.selectRoleAll(Long.parseLong(platId));
        ResultBean resultBean = ResultBean.success();
        resultBean.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        resultBean.put(ResultBean.DATA_TAG, sysUser);
        List<Long> roleIds = null;
        if(sysUser != null && CollectionUtils.isNotEmpty(sysUser.getRoles())){
            roleIds = sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList());
        }
        resultBean.put("roleIds", roleIds);
        return resultBean;
    }

    /**
     * 修改用户
     */
    @ApiOperation("修改用户信息")
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysUser user) {
        String platId = getPlatId();
        user.setPlatId(Long.parseLong(platId));
        if (!iSysUserService.checkUserNameUnique(user)) {
            return error(MessageUtils.message("user.already.exist.edit"));
        }
        if (StringUtils.isNotEmpty(user.getPhoneNumber()) && !iSysUserService.checkPhoneUnique(user)) {
            return error(MessageUtils.message("user.phone.already.exist.edit"));
        }
        if (StringUtils.isNotEmpty(user.getWorkNumber()) && !iSysUserService.checkWorkNumberUnique(user)) {
            return error(MessageUtils.message("user.worknumber.already.exist.edit"));
        }
        return resultBean(iSysUserService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @ApiOperation("删除用户")
    @PostMapping("/remove")
    public ResultBean remove(@RequestBody SysUser user) {
        /*if (getUserId().equals(user.getUserId())) {
            return error(MessageUtils.message("user.myself.delete"));
        }*/
        return resultBean(iSysUserService.deleteUserById(user.getUserId()));
    }

    /**
     * 重置密码
     */
    @ApiOperation("重置密码")
    @PostMapping("/updatePassword")
    public ResultBean updatePwd(@Validated @RequestBody SysUser user) {
        iSysUserService.checkUserAllowed(user);
        user.setPassword(SysPasswordService.encryptPassword(user.getPassword()));
        return resultBean(iSysUserService.updateUser(user));
    }

    /**
     * 状态修改
     */
    @ApiOperation("修改用户状态")
    @PostMapping("/changeStatus")
    public ResultBean changeStatus(@Validated @RequestBody SysUser user) {
        iSysUserService.checkUserAllowed(user);
        return resultBean(iSysUserService.updateUser(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @ApiOperation("根据用户编号获取授权角色")
    @GetMapping("/authRole/query")
    public ResultBean authRole(@ApiParam(required = true, name = "用户ID") @RequestParam Long userId) {
        String platId = getPlatId();
        SysUser user = iSysUserService.selectUserById(userId,Long.parseLong(platId));
        user.setPlatId(Long.parseLong(platId));
        List<SysRole> roles = roleService.selectRolesByUser(user);
        return success(SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
    }

    @ApiOperation("根据用户编号获取授权角色——ris")
    @GetMapping("/authRole/list")
    public ResultBean authRoleListByUserId(@ApiParam(required = true, name = "用户ID") @RequestParam Long userId) {
        String platId = getPlatId();
        SysUser user = iSysUserService.selectUserById(userId,Long.parseLong(platId));
        user.setPlatId(Long.parseLong(platId));
        List<SysRole> roles = roleService.authRoleListByUserId(user);
        return success(SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
    }

    /**
     * 用户授权角色
     */
    @ApiOperation("用户授权角色")
    @PostMapping("/authRole")
    public ResultBean insertAuthRole(@RequestBody SysUser user) {
        String platId = getPlatId();
        iSysUserService.insertUserAuth(user.getUserId(), user.getRoleIds(), Long.parseLong(platId));
        return success();
    }

    @ApiOperation("导入用户数据")
    @PostMapping("/importData")
    public ResultBean importExcel(HttpServletRequest request, HttpServletResponse response) {
        return iSysUserService.importExcel(request, response);
    }

    @ApiOperation("导出用户数据")
    @PostMapping("/export")
    public ModelAndView export(SysUser user) {
        return iSysUserService.export(user);
    }

    @ApiOperation("下载表格")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        iSysUserService.importTemplate(response);
    }

    @ApiOperation("强制下线")
    @PostMapping("/forceOut")
    public ResultBean forceLogout(@RequestBody  SysUser user) {
        Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + user.getUserId() + ":" + "*");
        if(!keys.isEmpty()){
            for (String key : keys) {
                redisCache.deleteObject(key);
            }
        }
        return success();
    }

    /**
     * 晨会定制接口，根据用户id列表获取所有用户列表(增加角色)
     */
    @ApiOperation("根据角色id获取下属用户列表")
    @PostMapping(value = "/getUserListByIds")
    public ResultBean getUserListByIds(@RequestBody Long[] userIds) {
        String platId = getPlatId();
        return ResultBean.success(iSysUserService.getUserListByIds(userIds,Long.parseLong(platId)));
    }

    /**
     * pacs定制接口，用于操作日志根据用户id反查用户名或用户姓名等其他信息
     * @return
     */
    @GetMapping("/getAllUsers")
    public ResultBean getAllUsers() {
        return ResultBean.success(iSysUserService.getAllUsers());
    }

    /**
     * 聊天服务临时使用接口，获取除了我在内的，某个平台下的相关的所有人
     */
    @ApiOperation("除了我在内的，某个平台下的相关的所有人")
    @GetMapping("/getChatUsers")
    public ResultBean getChatUsers() {
        String platId = getPlatId();
        return ResultBean.success(iSysUserService.getChatUsers(platId));
    }
}
