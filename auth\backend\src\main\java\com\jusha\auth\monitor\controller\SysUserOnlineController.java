package com.jusha.auth.monitor.controller;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.monitor.domain.SysUserOnline;
import com.jusha.auth.monitor.service.ISysUserOnlineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 在线用户监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/online")
public class SysUserOnlineController extends BaseController {
    @Autowired
    private ISysUserOnlineService userOnlineService;

    @Autowired
    private RedisCache redisCache;

    @GetMapping("/list")
    public TableDataInfo list(String ipaddr, String userName) throws JsonProcessingException {
        Collection<String> keys = redisCache.keys(Constants.LOGIN_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<SysUserOnline>();
        for (String key : keys) {
            ObjectMapper objectMapper = new ObjectMapper();
            HashMap loginMap = redisCache.getCacheObject(key);
            LoginUser user = JSONObject.parseObject(objectMapper.writeValueAsString(loginMap), LoginUser.class);
            if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName)) {
                userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, user));
            } else if (StringUtils.isNotEmpty(ipaddr)) {
                userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user));
            } else if (StringUtils.isNotEmpty(userName) && StringUtils.isNotNull(user.getSysUser())) {
                userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
            } else {
                userOnlineList.add(userOnlineService.loginUserToUserOnline(user));
            }
        }
        Collections.reverse(userOnlineList);
        userOnlineList.removeAll(Collections.singleton(null));
        // 使用 Comparator 接口按照 count 排序
        Collections.sort(userOnlineList, new Comparator<SysUserOnline>() {
            @Override
            public int compare(SysUserOnline o1, SysUserOnline o2) {
                return o2.getLoginTime().compareTo(o1.getLoginTime());
            }
        });
        return getDataTable(userOnlineList);
    }

    /**
     * 强退用户
     */
    @HasPermissions
    @PostMapping("/forceOut")
    public ResultBean forceLogout(@RequestParam String tokenId) {
        redisCache.deleteObject(tokenId);
        return success();
    }
}
