<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="所属平台" prop="platId">
        <el-select :default-first-option="false" v-model="queryParams.platId" placeholder="所属平台">
          <el-option
            v-for="item in this.platList"
            :key="item.platId"
            :label="item.platName"
            :value="item.platId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分组名称" prop="groupName">
        <el-input
          maxlength="50"
          v-model="queryParams.groupName"
          placeholder="请输入分组名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="分组状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-if="keyWords.includes('add')"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="groupList"
      row-key="groupId"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="groupName" label="分组名称" width="450"></el-table-column>
      <el-table-column prop="orderNum" label="排序" width="200"></el-table-column>
      <!-- <el-table-column prop="status" label="状态" width="100"> -->
        <!-- <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column> -->
      <el-table-column prop="center" label="中心分组" width="200">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_center" :value="scope.row.center"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-if="keyWords.includes('edit')"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-if="keyWords.includes('add')"
          >新增</el-button>
          <el-button
            v-if="scope.row.parentId != 0 && keyWords.includes('remove')"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改分组对话框  -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属平台" prop="platId">
              <el-select v-model="form.platId" placeholder="所属平台" @change="changeSelectedPlat">
                <el-option
                  v-for="item in this.platList"
                  :key="item.platId"
                  :label="item.platName"
                  :value="item.platId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.parentId !== 0">
            <el-form-item label="上级分组" prop="parentId">
              <treeselect v-model="form.parentId" :options="groupOptions" :normalizer="normalizer" placeholder="选择上级分组" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分组名称" prop="groupName">
              <el-input v-model="form.groupName" placeholder="请输入分组名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        <el-col :span="12">
            <el-form-item label="中心分组">
              <el-radio-group v-model="form.center">
                <el-radio
                  v-for="dict in dict.type.is_center"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="分组状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row> -->
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPlat} from "@/api/system/plat";
import { listGroup, getGroup, delGroup, addGroup, updateGroup, listGroupExcludeChild } from "@/api/system/group";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  keyWords : [],
  name: "Group",
  dicts: ['sys_normal_disable','is_center'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      groupList: [],
      groupListDialog: [],
      // 分组树选项
      groupOptions: [],
      platList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        groupName: undefined,
        // status: undefined,
        platId:undefined
      },
      queryParamsDialog: {
        groupName: undefined,
        // status: undefined,
        platId:undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        platId: [
          { required: true, message: "所属平台不能为空", trigger: "blur" }
        ],
        parentId: [
          { required: true, message: "上级分组不能为空", trigger: "blur" }
        ],
        groupName: [
          { required: true, message: "分组名称不能为空", trigger: "blur" },
          { min: 2, max: 50, message: '分组名称长度必须介于 2 和 50 之间', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,50}$/,
            message: "仅支持中文、英文、数字及下划线",
            trigger: "blur"
          }
        ],
        // leader:[
        // { min: 2, max: 20, message: '负责人长度必须介于 2 和 20 之间', trigger: 'blur' }
        // ],
        orderNum: [
          { required: true, message: "显示排序不能为空", trigger: "blur" }
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getButton();
    this.getPlatList();
  },
  methods: {
    getButton(){
      this.keyWords = this.$route.meta.keyWords
    },
    getPlatList(){
      listPlat(this.queryParams).then(response => {
        this.platList = response.rows;
        if(response.total!=0){
          this.queryParams.platId = this.platList[0].platId
          this.getList();
        }else{
          this.queryParams.platId = undefined
          this.getList();
        }
      }); 
    },
    /** 查询分组列表 */
    getList() {
      this.loading = true;
      listGroup(this.queryParams).then(response => {
        this.groupList = this.handleTree(response.data, "groupId");
        this.loading = false;
      });
    },
    /** 转换分组数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.groupId,
        label: node.groupName,
        children: node.children
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        groupId: undefined,
        parentId: undefined,
        groupName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        // status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.getPlatList();
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row != undefined) {
        this.form.parentId = row.groupId;
      }
      this.open = true;
      this.title = "添加分组";
      this.form.platId = this.queryParams.platId
      this.queryParamsDialog.platId = this.form.platId
      listGroup(this.queryParamsDialog).then(response => {
        this.groupOptions = this.handleTree(response.data, "groupId");
      });
    },

    changeSelectedPlat(){
      this.groupOptions = []
      this.form.parentId = undefined,
      
      this.queryParamsDialog.platId = this.form.platId
      listGroup(this.queryParamsDialog).then(response => {
        this.groupOptions = this.handleTree(response.data, "groupId");
      });
    },

    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getGroup(row.groupId).then(response => {
        this.form = response.data;
        this.title = "修改分组";
        listGroupExcludeChild(row.groupId,this.queryParams.platId).then(response => {
          this.groupOptions = this.handleTree(response.data, "groupId");
          if (this.groupOptions.length == 0) {
            const noResultsOptions = { groupId: this.form.parentId, groupName: this.form.parentName, children: [] };
            this.groupOptions.push(noResultsOptions);
          }
        });
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.groupId != undefined) {
            updateGroup(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGroup(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除"' + row.groupName + '"？').then(function() {
        return delGroup(row.groupId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
