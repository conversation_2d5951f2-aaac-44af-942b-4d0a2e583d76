package com.jusha.caselibrary.file.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

@Data
public class FileUploadResp {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "原文件名称")
    private String originName;

    @ApiModelProperty(value = "实际存储文件名称（加入时间）")
    private String actualName;

    @ApiModelProperty(value = "文件访问相对路径")
    private String fileUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty("文件访问全路径")
    private String fullUrl;


    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private MultipartFile multipartFile;

    @ApiModelProperty("顺序")
    @JsonProperty("seq")
    private Integer seq;

}