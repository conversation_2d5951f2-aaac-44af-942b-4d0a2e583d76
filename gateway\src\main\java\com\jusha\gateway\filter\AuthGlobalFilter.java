package com.jusha.gateway.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.jusha.gateway.config.properties.IgnoreWhiteProperties;
import com.jusha.gateway.constant.Constant;
import com.jusha.gateway.resp.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 网关过滤器
 */
@Slf4j
@RefreshScope
@Component
public class AuthGlobalFilter implements GlobalFilter, Ordered {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    @Value("${LOGIN_HOLD_HOUR}")
    private Long loginHoldHour;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest serverRequest = exchange.getRequest();
        handleHeader(serverRequest);

        String pathStr = serverRequest.getURI().getPath();
        log.info("Request gateway begin, path:{}", pathStr);

        //是否禁止访问
        if(isForbidPath(pathStr)){
            ResultBean resp = ResultBean.error(Constant.FORBIDDEN, "禁止访问");
            return handleResponse(exchange.getResponse(), resp);
        }
        //是否不用token登录校验
        if(noVerifyToken(pathStr)){
            return chain.filter(exchange);
        }
        String uri = serverRequest.getURI().getPath();
        log.info("当前请求接口路径是{}", uri);
        // 跳过不需要验证的路径
        List<String> whites = ignoreWhite.getWhites();
        if ((whites.contains(uri))) {
            return chain.filter(exchange);
        }
        //token登录校验
        String token = serverRequest.getHeaders().getFirst(Constant.TOKEN_KEY);
        log.info("Request gateway needAuth, path:{} token:{}", pathStr, token);
        if (StringUtils.isNotBlank(token)) {
            String redisUserJson = redisTemplate.opsForValue().get(token);
            if (StringUtils.isNotBlank(redisUserJson)) {
                RedisUser redisUser = JSON.parseObject(redisUserJson, RedisUser.class);
                if(redisUser != null && redisUser.getUserId() != null){
                    redisTemplate.opsForValue().set(token, redisUserJson, loginHoldHour, TimeUnit.HOURS);  //刷新有效时间

                    //接口权限：外网版需要，pacs不需要
                    String network = redisTemplate.opsForValue().get(Constant.NETWORK_ENVIRONMENT);
                    if(StringUtils.isNotBlank(network)){
                        network =  network.trim().replace("\"","");
                    }
                    if(Constant.NETWORK_WAN.equals(network) && isVerifyInterface(uri)){ //需要校验接口权限
                        Set<String> permissions = redisUser.getPermissions();
                        if (!permissions.contains("/*/*/*") && !permissions.contains(uri)) {
                            ResultBean resp = ResultBean.error(Constant.FORBIDDEN, "无权访问");
                            return handleResponse(exchange.getResponse(), resp);
                        }
                    }

                    return chain.filter(exchange);
                }
            }
        }

        //未通过token登录校验
        ResultBean resp = ResultBean.error(Constant.UNAUTHORIZED, "无效登录");
        return handleResponse(exchange.getResponse(), resp);
    }

    /** 数值越小优先级越高 */
    @Override
    public int getOrder() {
        return 0;
    }


    //处理 Response
    private <T> Mono<Void> handleResponse(ServerHttpResponse serverResponse, T result) {
        String resultStr = JSONArray.toJSONString(result);
        byte[] bits = resultStr.getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = serverResponse.bufferFactory().wrap(bits);
        //指定编码，否则在浏览器中会中文乱码
        serverResponse.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        return serverResponse.writeWith(Mono.just(buffer));
    }

    //处理 Request Header
    private void handleHeader(ServerHttpRequest serverRequest){
        HttpHeaders headers = serverRequest.getHeaders();
        headers= HttpHeaders.writableHttpHeaders(headers);

        //traceId写入MDC
        String traceId = headers.getFirst(Constant.TRACEID);
        if(StringUtils.isBlank(traceId)){
            traceId = StringUtils.join(RandomStringUtils.randomAlphanumeric(4), RandomStringUtils.randomNumeric(16));
            headers.add(Constant.TRACEID, traceId);
        }
        MDC.put(Constant.TRACEID, traceId);
    }

    //匹配"禁止"的path
    private boolean isForbidPath(String path){
        String forbid = "^(/.*)+/forbid(/.*)*$";
        return path.matches(forbid);
    }

    //匹配"不需要校验登录"的path
    private boolean noVerifyToken(String path){
        String pacs = "^/pacs(/.*)*$";
        String swagger = "^(/.*)+/(swagger-resources|webjars|v2|csrf|swagger-ui.html)(/.*)*$";
        String error = "^(/.*)*/error$";
        String open = "^(/.*)+/open(/.*)*$";
        return path.matches(pacs) || path.matches(swagger) || path.matches(error) || path.matches(open);
    }

    //匹配"需要校验接口权限"的path
    private boolean isVerifyInterface(String path){
        String pacs = "^/(jusha-pacs|jusha-dimse)(/.*)*$";
        return !path.matches(pacs);
    }

}