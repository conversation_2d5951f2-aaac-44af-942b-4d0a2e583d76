package com.jusha.ris.docking.feign;

import com.jusha.ris.docking.common.resp.ResultBean;
import com.jusha.ris.docking.mock.dto.StudyInfoDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @title StudyClient
 * @description
 * @date 2025/2/24
 */

@Component
@FeignClient(name = "ris-report", path = "/ris-report")
public interface StudyClient {

    /**
     * 对接添加检查、申请单信息
     *
     * @param studyInfoDto
     * @return
     */
    @PostMapping("/forbid/study/apply/add/docking")
    ResultBean<Void> addStudyApplyByDocking(@RequestBody StudyInfoDto studyInfoDto);

}
