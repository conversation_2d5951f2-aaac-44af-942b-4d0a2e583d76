package com.jusha.gateway.filter;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Set;

@Data
public class RedisUser {

    /**
     * 权限列表
     */
    @JSONField
    private Set<String> permissions;

    /**
     * sysUserId
     */
    private Long userId;

    /**
     *  SysUser
     */
    private SysUser sysUser;


    @Data
    public static class SysUser {
        
        /**
         * sysUserId
         */
        private Long userId;

        private String userName;

        private String nickName;

        private String phoneNumber;

        private String workNumber;

        private String status;

    }

}