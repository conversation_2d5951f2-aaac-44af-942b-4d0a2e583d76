package com.jusha.caselibrary.file.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class StreamUploadReq {

    /**
     * 任务ID（异步流式上传完后，回调用）
     */
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    /**
     * 文件名(包含后缀，必须在临时文件目录下)
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;

}