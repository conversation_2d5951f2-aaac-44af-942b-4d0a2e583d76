package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 科室病例分类表
 * <AUTHOR>
 */
@TableName("t_dep_case_type")
@Data
public class DepCaseType {

    @ApiModelProperty(value = "病例类型id")
    @TableId("case_type_id")
    private Long caseTypeId;    

    @ApiModelProperty(value = "病例类型名称")
    @TableField("case_type_name")
    private String caseTypeName;    

    @ApiModelProperty(value = "是否需要审核0不需要1需要")
    @TableField("audit")
    private String audit;    

    @ApiModelProperty(value = "病例库地址")
    @TableField("address")
    private String address;    

    @ApiModelProperty(value = "菜单Id")
    @TableField("menu_id")
    private Long menuId;    

    @ApiModelProperty(value = "删除标志0正常1删除")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;    

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private Long createBy;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "修改人")
    @TableField("update_by")
    private Long updateBy;    

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;    

}
