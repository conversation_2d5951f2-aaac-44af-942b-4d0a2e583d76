package com.jusha.auth.common.core.domain.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class LoginStatus {
    /**
     * 用户id
     */
    private long userId;

    /**
     * 脱敏手机号
     */
    private String phone;

    public LoginStatus(long userId, String phone) {
        this.userId = userId;
        this.phone = phone;
    }
}
