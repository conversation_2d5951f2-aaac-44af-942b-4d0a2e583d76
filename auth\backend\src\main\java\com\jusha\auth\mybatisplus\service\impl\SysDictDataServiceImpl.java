package com.jusha.auth.mybatisplus.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jusha.auth.mybatisplus.entity.SysDictData;
import com.jusha.auth.mybatisplus.mapper.SysDictDataMapper;
import com.jusha.auth.mybatisplus.service.SysDictDataService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 字典数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Service
public class SysDictDataServiceImpl extends ServiceImpl<SysDictDataMapper, SysDictData> implements SysDictDataService {

}
