package com.jusha.caselibrary.file.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.acHolder.PropertiesBean;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.file.dto.FileReportDto;
import com.jusha.caselibrary.file.dto.PostPolicyCOS;
import com.jusha.caselibrary.file.dto.req.StreamUploadReq;
import com.jusha.caselibrary.file.mapper.FileMapper;
import com.jusha.caselibrary.file.resp.FileUploadResp;
import com.jusha.caselibrary.file.resp.SignatureBucketResponse;
import com.jusha.caselibrary.file.service.FileService;
import com.jusha.caselibrary.file.task.CosStreamUploadTask;
import com.jusha.caselibrary.mybatisplus.entity.Resource;
import com.jusha.caselibrary.mybatisplus.service.ResourceService;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.StorageClass;
import com.qcloud.cos.model.UploadResult;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.TransferManager;
import com.qcloud.cos.transfer.Upload;
import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Response;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.DeleteMediaRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@RequiredArgsConstructor
@Service("fileService_WAN")
public class FileCOSServiceImpl implements FileService {

    private final ResourceService resourceService;
    private final FileMapper fileMapper;
    private final PropertiesBean propertiesBean;

    private COSClient cosClient;
    private VodClient vodClient;

    @Value("${cos.bucketName:}")
    private String bucketName;

    @Value("${cos.endpoint:}")
    private String endpointCOS;

    @PostConstruct
    public void init() {
        COSCredentials cosCred = new BasicCOSCredentials(propertiesBean.getSecretId(), propertiesBean.getSecretKey());
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setRegion(new Region(propertiesBean.getRegion()));
        cosClient = new COSClient(cosCred, clientConfig);

        Credential vodCred = new Credential(propertiesBean.getSecretId(), propertiesBean.getSecretKey());
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(propertiesBean.getVodUrl());
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        vodClient = new VodClient(vodCred, propertiesBean.getRegion(), clientProfile);
    }

    public COSClient getCosClient(){
        return cosClient;
    }


    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void upload(FileUploadResp uploadResp){
        MultipartFile multipartFile = uploadResp.getMultipartFile();

        //原始文件名称(包含后缀)
        String originName = multipartFile.getOriginalFilename();
        //实际存储文件名称
        String tempName = StringUtils.join(FilenameUtils.getBaseName(originName), "_", DateUtil.convertDateToStr(new Date(), "yyyyMMddHHmmss"));
        String actualName = StringUtils.join(DigestUtils.md5Hex(tempName), ".", FilenameUtils.getExtension(originName));
        String key = StringUtils.join( "/", DateUtil.convertDateToStr(new Date(), "yyyyMM"), "/", DateUtil.convertDateToStr(new Date(), "dd"), "/", actualName);

        log.info("upload-1-originName:{}", originName);

        TransferManager transferManager = null;
        try {
            ExecutorService threadPool = Executors.newFixedThreadPool(8);
            transferManager = new TransferManager(cosClient, threadPool);

            InputStream inputStream = multipartFile.getInputStream();
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(inputStream.available());
            objectMetadata.setContentDisposition("inline");
            PutObjectRequest putObjectRequest = new PutObjectRequest(propertiesBean.getBucketName(), key, inputStream, objectMetadata);
            putObjectRequest.setStorageClass(StorageClass.Standard);

            Upload upload = transferManager.upload(putObjectRequest);
            UploadResult uploadResult = upload.waitForUploadResult();
            log.info("upload success, result:{}", JSON.toJSONString(uploadResult));
        } catch (Exception e) {
            log.error("upload fail, result:{}",e);
        }finally {
            transferManager.shutdownNow(false);
        }

        log.info("upload-2-key:{}", key);

        //入库
        Resource resource = new Resource();
        resource.setResourceId(YitIdHelper.nextId());
        resource.setOriginName(originName);
        resource.setActualName(key);
        String fileUrl = StringUtils.join(propertiesBean.getEndpoint(), key);
        resource.setFileUrl(fileUrl);
        resource.setLmGpId(LoginUtil.isLogin()? LoginUtil.lmGroupId():null);
        resourceService.save(resource);
        //填充返回数据
        BeanUtils.copyProperties(resource ,uploadResp);
        uploadResp.setFullUrl(fileUrl);
        uploadResp.setMultipartFile(null);
        log.info("upload-3-uploadResp:{}", JSON.toJSONString(uploadResp));
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public List<FileUploadResp> uploadMulti(MultipartFile[] multipartFiles){
        List<FileUploadResp> result = new ArrayList<>();

        if(multipartFiles != null && multipartFiles.length > 0){
            for(int i = 0; i < multipartFiles.length; i++){
                FileUploadResp uploadResp = new FileUploadResp();
                uploadResp.setMultipartFile(multipartFiles[i]);
                uploadResp.setSeq(i+1);
                result.add(uploadResp);
            }
        }
        result.parallelStream().forEach(uploadResp -> this.upload(uploadResp));

        return result;
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void deleteFile(Long resourceId) {
        //删除文件
        Resource resource = resourceService.getById(resourceId);
        if(resource == null) {
            return;
        }
        if(resource.getOriginName().endsWith(".mp4")){
            try {
                DeleteMediaRequest req = new DeleteMediaRequest();
                req.setFileId(resource.getActualName());
                vodClient.DeleteMedia(req);
            }catch (TencentCloudSDKException e) {
                log.error("delete fail, result"+e);
            }
        }
        else{
            try {
                cosClient.deleteObject(propertiesBean.getBucketName(), resource.getActualName());
            } catch (Exception e) {
                log.error("delete fail, result"+e);
            }
        }
        //删除关联数据
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void deleteFileBatch(List<Long> resourceIds){
        if(CollectionUtils.isEmpty(resourceIds)) {
            return;
        }
        //删除文件
        resourceIds.parallelStream().forEach(resourceId -> {
            Resource resource = resourceService.getById(resourceId);
            if(resource == null) {
                return;
            }
            if(resource.getOriginName().endsWith(".mp4")){
                try {
                    DeleteMediaRequest req = new DeleteMediaRequest();
                    req.setFileId(resource.getActualName());
                    vodClient.DeleteMedia(req);
                }catch (TencentCloudSDKException e) {
                    log.error("delete fail, result"+e);
                }
            }else{
                try {
                    cosClient.deleteObject(propertiesBean.getBucketName(), resource.getActualName());
                } catch (Exception e) {
                    log.error("delete fail, result"+e);
                }
            }
        });
        //删除关联数据
    }

    @Override
    public void streamUpload(StreamUploadReq req){
        ContextHolder.getBean("cachedPool", ExecutorService.class).submit(new CosStreamUploadTask(req.getTaskId(), req.getFileName()));
    }

    @Override
    public Resource report(FileReportDto fileReportDto) {
        //入库
        Resource resource = new Resource();
        resource.setResourceId(YitIdHelper.nextId());
        resource.setOriginName(fileReportDto.getFileName());
        String key = fileReportDto.getFullUrl().replace(propertiesBean.getEndpoint(), "");  //文件key,对应: /202411/12/4a917608d85438eed58fa6741ec28b07.zip
        resource.setActualName(key);
        resource.setFileUrl(fileReportDto.getFullUrl());
        resource.setLmGpId(LoginUtil.lmGroupId());
        resourceService.save(resource);
        return resource;
    }

    /**
     * 获取上传凭证，有效期只有十分钟
     * @param fileName
     * @return
     */
    @Override
    public SignatureBucketResponse getPostPolicy(String fileName,String bucket){
        SignatureBucketResponse signatureBucketResponse = new SignatureBucketResponse();
        PostPolicyCOS postPolicyCOS = createMinioPostCOS(fileName);
        signatureBucketResponse.setPostPolicyCOS(postPolicyCOS);
        signatureBucketResponse.setPostPolicyMinio(null);
        return signatureBucketResponse;
    }

    private PostPolicyCOS createMinioPostCOS(String fileName){
        PostPolicyCOS postPolicyCOS = new PostPolicyCOS();
        TreeMap<String, Object> config = new TreeMap<String, Object>();
        try {
            config.put("secretId", propertiesBean.getSecretId());
            config.put("secretKey", propertiesBean.getSecretKey());
            // 设置域名,可通过此方式设置内网域名
            //config.put("host", "sts.internal.tencentcloudapi.com");
            // 临时密钥有效时长，单位是秒
            config.put("durationSeconds", 600);
            config.put("bucket", bucketName);
            // bucket 所在地区
            config.put("region", propertiesBean.getRegion());
            config.put("allowPrefixes", new String[]{"*"});
            // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
            String[] allowActions = new String[] {
                    // 简单上传
                    "name/cos:PutObject",
                    "name/cos:PostObject",
                    // 分片上传
                    "name/cos:InitiateMultipartUpload",
                    "name/cos:ListMultipartUploads",
                    "name/cos:ListParts",
                    "name/cos:UploadPart",
                    "name/cos:CompleteMultipartUpload"
            };
            config.put("allowActions", allowActions);
            Response response = CosStsClient.getCredential(config);
            postPolicyCOS.setBucketName(bucketName);
            postPolicyCOS.setKey(bucketName+ Constant.FILE_SEPARATOR+fileName);
            postPolicyCOS.setRegion(propertiesBean.getRegion());
            postPolicyCOS.setEndpoint(endpointCOS);
            postPolicyCOS.setResponse(response);
        } catch (Exception e) {
            log.error("===========获取COS上传凭证有误====="+e);
        }
        return postPolicyCOS;
    }

}