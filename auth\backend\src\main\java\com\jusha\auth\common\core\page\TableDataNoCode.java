package com.jusha.auth.common.core.page;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 *
 * <AUTHOR>
 */
@Data
public class TableDataNoCode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 列表数据
     */
    private List<?> rows;

    /**
     * 表格数据对象
     */
    public TableDataNoCode() {
    }

    /**
     * 分页
     *
     * @param list  列表数据
     * @param total 总记录数
     */
    public TableDataNoCode(List<?> list, int total) {
        this.rows = list;
        this.total = total;
    }
}
