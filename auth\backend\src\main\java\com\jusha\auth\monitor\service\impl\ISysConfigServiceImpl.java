package com.jusha.auth.monitor.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.core.text.Convert;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.monitor.service.ISysConfigService;
import com.jusha.auth.mybatisplus.entity.SysConfig;
import com.jusha.auth.mybatisplus.service.SysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;

/**
 * 参数配置 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class ISysConfigServiceImpl implements ISysConfigService {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private TokenService tokenService;

    @Value("${token.network:}")
    private String network;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        redisCache.setCacheObject(Constants.NETWORK_ENVIRONMENT,network);
        loadingConfigCache();
    }

    /**
     * 查询参数配置信息
     * 
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    public SysConfig selectConfigById(Long configId) {
        return sysConfigService.getById(configId);
    }

    /**
     * 根据键名查询参数配置信息
     * 
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue)) {
            return configValue;
        }
        SysConfig retConfig = sysConfigService.lambdaQuery().eq(SysConfig::getConfigKey,configKey).one();
        if (StringUtils.isNotNull(retConfig)) {
            redisCache.setCacheObject(getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 查询参数配置列表
     * 
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfig> selectConfigList(SysConfig config) {
        LambdaQueryChainWrapper<SysConfig> wrapper = sysConfigService.lambdaQuery();
        if(config.getConfigName() != null){
            wrapper.like(SysConfig::getConfigName, config.getConfigName());
        }
        if(config.getConfigType() != null){
            wrapper.eq(SysConfig::getConfigType, config.getConfigType());
        }
        if(config.getConfigKey() != null){
            wrapper.like(SysConfig::getConfigKey, config.getConfigKey());
        }
        return wrapper.list();
    }

    /**
     * 新增参数配置
     * 
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean insertConfig(SysConfig config) {
        config.setCreateTime(DateUtils.getNowDate());
        config.setCreateBy(tokenService.getUserId());
        boolean flag = sysConfigService.save(config);
        if (flag) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return flag;
    }

    /**
     * 修改参数配置
     * 
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean updateConfig(SysConfig config) {
        SysConfig temp = sysConfigService.getById(config.getConfigId());
        if (!StringUtils.equals(temp.getConfigKey(), config.getConfigKey())) {
            redisCache.deleteObject(getCacheKey(temp.getConfigKey()));
        }
        config.setUpdateBy(tokenService.getUserId());
        config.setUpdateTime(DateUtils.getNowDate());
        return sysConfigService.updateById(config);
    }

    /**
     * 批量删除参数信息
     * 
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(Long[] configIds) {
        for (Long configId : configIds) {
            SysConfig config =  selectConfigById(configId);
            if (StringUtils.equals(Constants.YES, config.getConfigType())) {
                throw new ServiceException(MessageUtils.message("inner.parameter.delete"));
            }
            sysConfigService.removeById(configId);
            redisCache.deleteObject(getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache() {
        List<SysConfig> configsList = sysConfigService.lambdaQuery().list();
        for (SysConfig config : configsList) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache() {
        Collection<String> keys = redisCache.keys(Constants.SYS_CONFIG_KEY + "*");
        redisCache.deleteObject(keys);
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     * 
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean checkConfigKeyUnique(SysConfig config) {
        Long configId = StringUtils.isNull(config.getConfigId()) ? Constants.ALL_MINUS1L : config.getConfigId();
        List<SysConfig> configInfos = sysConfigService.lambdaQuery()
                .eq(SysConfig::getConfigKey, config.getConfigKey()).list();
        if (!configInfos.isEmpty() && configInfos.get(0).getConfigId().longValue() != configId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 设置cache key
     * 
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return Constants.SYS_CONFIG_KEY + configKey;
    }
}
