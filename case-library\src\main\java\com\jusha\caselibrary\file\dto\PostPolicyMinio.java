package com.jusha.caselibrary.file.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * minio上传凭证
 *
 * <AUTHOR>
 * @date 2025/02/07
 **/
@Data
public class PostPolicyMinio {

    @ApiModelProperty(value = "minio目标桶")
    private String bucket;

    @ApiModelProperty(value = "minio端点")
    private String endpoint;

    @ApiModelProperty(value = "minio时间戳")
    private String xAmzDate;

    @ApiModelProperty(value = "minio签名")
    private String xAmzSignature;

    @ApiModelProperty(value = "minio文件全路径")
    private String key;

    @ApiModelProperty(value = "minio签名算法")
    private String xAmzAlgorithm;

    @ApiModelProperty(value = "minio认证授权")
    private String xAmzCredential;

    @ApiModelProperty(value = "minio凭证token")
    private String policy;
}
