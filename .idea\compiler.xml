<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="im-server" />
        <module name="case-library" />
        <module name="gateway" />
        <module name="auth-server" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="auth-server" options="-parameters" />
      <module name="case-library" options="-parameters" />
      <module name="gateway" options="-parameters" />
      <module name="im-server" options="-parameters" />
    </option>
  </component>
</project>