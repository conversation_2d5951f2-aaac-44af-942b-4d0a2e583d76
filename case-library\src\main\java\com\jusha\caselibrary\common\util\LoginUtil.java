package com.jusha.caselibrary.common.util;

import com.alibaba.fastjson.JSON;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessCode;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.feign.api.AuthServerApi;
import com.jusha.caselibrary.feign.dto.GroupMsgDto;
import com.jusha.caselibrary.system.dto.RedisUser;
import com.jusha.caselibrary.system.dto.SysRole;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 登录、请求相关
 */
@Slf4j
public class LoginUtil {

    private final static Map<String, RedisUser> LOGIN_USER = new HashMap<>();       //key为traceId, 缓存: 登录用户


    /**
     * 获取 HttpServletRequest
     * @return
     */
    public static HttpServletRequest getRequest(){
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (attributes==null)?null:attributes.getRequest();
        return request;
    }

    /**
     * 获取 header
     * @param name
     * @return
     */
    public static String getRequestHeader(String name){
        HttpServletRequest request = getRequest();

        String result = null;
        if(request != null && StringUtils.isNotBlank(request.getHeader(name)))  result = request.getHeader(name);
        return result;
    }

    /**
     * 获取 HttpServletResponse
     * @return
     */
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = attributes.getResponse();
        return response;
    }


    /**
     * 获取当前登录用户信息
     * @return
     */
    public static RedisUser getLoginUser(){
        String traceId = MDC.get(Constant.TRACEID);

        RedisUser loginUser = null;
        //先用traceId从缓存中取
        if(StringUtils.isNotBlank(traceId)) {
            loginUser = LOGIN_USER.get(traceId);
        }

        //取不到再去Redis中
        if(loginUser == null){
            String token = getRequestHeader(Constant.TOKEN_KEY);
            if (StringUtils.isNotBlank(token)) {
                String redisUserJson = ContextHolder.stringRedisTemplate().opsForValue().get(token);
                if (StringUtils.isNotBlank(redisUserJson))  loginUser = JSON.parseObject(redisUserJson, RedisUser.class);
            }
            //拿不到--无效登录--返回401
            if(loginUser == null){
                throw new BusinessException(BusinessCode.UNAUTHORIZED, LocaleUtil.getLocale("common.login.out"));
            }

            //填充: 角色类型列表
            List<SysRole> roles = loginUser.getSysUser().getRoles();
            if (CollectionUtils.isNotEmpty(roles)) {
                Set<String> roleTypes = roles.stream().map(role -> role.getRoleType()).collect(Collectors.toSet());
                loginUser.setRoleTypes(roleTypes);
            }

            //缓存
            LOGIN_USER.put(traceId, loginUser);
        }
        return loginUser;
    }

    /**
     * 获取当前登录用户ID
     * @return
     */
    public static Long getLoginUserId(){
        return getLoginUser().getUserId();
    }

    /**
     * 获取当前网络环境 WAN 外网 LAN 内网
     * @return
     */
    public static String getNetwork(){
        String network = ContextHolder.stringRedisTemplate().opsForValue().get(Constant.NETWORK_ENVIRONMENT);
        if(StringUtils.isNotBlank(network)){
            return network.replace("\"","");
        }
        return Constant.NETWORK_LAN;
    }

    /**
     * 是否登录
     * @return
     */
    public static boolean isLogin(){
        String token = getRequestHeader(Constant.TOKEN_KEY);

        RedisUser redisUser = null;
        if (StringUtils.isNotBlank(token)) {
            String redisUserJson = ContextHolder.stringRedisTemplate().opsForValue().get(token);
            if (StringUtils.isNotBlank(redisUserJson))  redisUser = JSON.parseObject(redisUserJson, RedisUser.class);
        }
        return (redisUser == null) ? false : true;
    }

    /**
     * 获取当前用户的所属联盟ID
     */
    public static Long lmGroupId(){
        RedisUser loginUser = getLoginUser();
        if(loginUser.getLmGroupId() == null){
            fillAllianceMsg2UserBean();
        }
        return loginUser.getLmGroupId();
    }

    /**
     * 获取当前用户的所属联盟的中心医院ID
     */
    public static Long centerGroupId(){
        RedisUser loginUser = getLoginUser();
        if(loginUser.getCenterGroupId() == null){
            fillAllianceMsg2UserBean();
        }
        return loginUser.getCenterGroupId();
    }

    //填充getAllianceMsg接口返回的数据到UserBean
    private static void fillAllianceMsg2UserBean(){
        RedisUser loginUser = getLoginUser();

        GroupMsgDto groupMsgDto = ContextHolder.getContext().getBean(AuthServerApi.class).getAllianceMsg().getData();
        loginUser.setSysGroupId(groupMsgDto.getSysGroupId());
        loginUser.setLmGroupId(groupMsgDto.getLmGroupId());
        loginUser.setCenterGroupId(groupMsgDto.getCenterGroupId());
    }

    /**
     * 清除缓存
     */
    public static void clearCache(){
        String traceId = MDC.get(Constant.TRACEID);
        if(StringUtils.isNotBlank(traceId)){
            LOGIN_USER.remove(traceId);
        }
    }

    /**
     * 获取当前用户的所属医院ID
     */
    public static Long sysGroupId(){
        return getLoginUser().getSysGroupId();
    }

}
