package com.jusha.auth.system.service;

import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.mybatisplus.entity.SysUserRole;
import java.util.List;

/**
 * 角色业务层
 *
 * <AUTHOR>
 */
public interface ISysRoleService {
    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    public List<SysRole> selectRoleList(SysRole role);

    /**
     * @description 根据角色类型查询角色数据
     * <AUTHOR>
     * @date 2025/3/8 10:51
     * @return List<SysRole>
     **/
    List<SysRole> selectRoleListByType(Long platId, String roleType);

    /**
     * 根据用户查询角色列表
     *
     * @param user 用户
     * @return 角色列表
     */
    public List<SysRole> selectRolesByUser(SysUser user);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    public List<SysRole> selectRoleAll(long platId);

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    public List<Long> selectRoleListByUserId(Long userId);

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    public SysRole selectRoleById(Long roleId);

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    public boolean checkRoleNameUnique(SysRole role);

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    public void checkRoleAllowed(SysRole role);

    /**
     * 校验角色是否有数据权限
     *
     * @param platId 平台id
     */
    public void checkRoleDataScope(String platId);

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public int countUserRoleByRoleId(Long roleId);

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    public SysRole insertRole(SysRole role);

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    public boolean updateRole(SysRole role);

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    public boolean updateRoleStatus(SysRole role);

    /**
     * 修改数据权限信息
     *
     * @param role 角色信息
     * @return 结果
     */
    public boolean authDataScope(SysRole role);

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public boolean deleteRoleById(Long roleId);

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    public boolean deleteAuthUser(SysUserRole userRole);

    /**
     * 批量取消授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    public boolean deleteAuthUsers(Long roleId, Long[] userIds);

    /**
     * 批量选择授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要删除的用户数据ID
     * @return 结果
     */
    public boolean insertAuthUsers(Long roleId, Long[] userIds);

    /**
     * 批量选择授权用户角色
     *
     * @param roleType  角色类型
     * @param userIds 需要删除的用户数据ID
     * @return 结果
     */
    public boolean insertAuthUsersForTeach(String roleType, Long[] userIds,long sysGroupId,long platId);

    /**
     * 知识库-保存用户关联角色、角色关联分组
     * @param roleType
     * @param userIds
     * @param sysGroupId
     * @param platId
     */
    void insertAuthUsersForPlato(String roleType, Long[] userIds,long sysGroupId,long platId);

    /**
     * 根据platId查询角色
     *
     * @param platId
     * @return
     */
    public boolean hasChildByPlatId(Long platId);

    /**
     * 根据角色id获取下属角色列表
     *
     * @param platId 所属平台ID
     * @return 结果
     */
    public List<SysRole> selectChildrenListByRoleId(Long roleId,String platId);

    /**
     * 根据角色id获取下属用户列表
     *
     * @param platId 所属平台ID
     * @return 结果
     */
    public List<SysUser> selectUserListByRoleIds(Long[] roleIds,String platId);


    /**
     * 根据角色名称获取角色列表
     * @param role
     * @return
     */
    public List<SysRole> getRoleByName(SysRole role);

    /**
     * @param platId
     * @param keyWord
     * @return List<SysRole>
     * @description 根据关键字查询角色列表
     * <AUTHOR>
     * @date 2025/2/24 10:47
     **/
     List<SysRole> getRoleByKeyWord(String platId, String keyWord);

    /**
     * 教学平台互联网定制接口，根据角色id列表删除所有角色和其相关联的用户。
     * 注意，如果这个用户不止绑定一个角色，那么只解绑这个关系，再删除角色，不要删除用户
     * @param platId
     * @param roleIds
     * @return
     */
    ResultBean removeRolesAndUsers(String platId, Long[] roleIds);

    /**
     * @description 根据用户id查询用户所拥有的角色信息
     * <AUTHOR>
     * @date 2025/2/26 9:22
     * @param user
     * @return List<SysRole>
     **/
    List<SysRole> authRoleListByUserId(SysUser user);
}
