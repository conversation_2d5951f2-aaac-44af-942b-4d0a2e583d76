package com.jusha.caselibrary.search.controller;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import com.jusha.caselibrary.search.dto.AdvancedSearchResponse;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高级检索控制器
 * <AUTHOR>
 * @date 2025/07/09
 */
@Slf4j
@Api(tags = "高级检索API")
@RestController
@RequestMapping("/api/search")
@Validated
public class AdvancedSearchController {

    @Autowired
    private AdvancedSearchService advancedSearchService;

    /**
     * 高级检索 - 主要搜索接口
     */
    @ApiOperation(value = "执行高级检索", notes = "支持多种搜索条件的组合查询，包括关键字、患者信息、时间范围等")
    @PostMapping("/advanced")
    public ResultBean<AdvancedSearchResponse<?>> advancedSearch(
            @ApiParam(value = "搜索请求参数", required = true)
            @Valid @RequestBody AdvancedSearchRequest request) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 参数预处理和优化
            preprocessRequest(request);
            // 2. 执行搜索
            AdvancedSearchResponse<?> response = advancedSearchService.search(request);
            // 3. 记录成功日志
            long took = System.currentTimeMillis() - startTime;
            log.info("搜索完成 - 关键字: {}, 类型: {}, 耗时: {}ms, 结果数: {}", 
                request.getKeyword(), request.getSearchType(), took, response.getTotal());
            return ResultBean.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("搜索参数错误, 错误: {}", e.getMessage());
            return ResultBean.error( e.getMessage());
        } catch (Exception e) {
            long took = System.currentTimeMillis() - startTime;
            log.error("搜索异常, 耗时: {}ms", took, e);
            return ResultBean.error("搜索服务异常，请稍后重试");
        }
    }

    /**
     * 批量搜索接口
     */
    @ApiOperation(value = "批量搜索", notes = "支持一次提交多个搜索请求")
    @PostMapping("/batch")
    public ResultBean<Map<String, Object>> batchSearch(
            @ApiParam(value = "批量搜索请求", required = true)
            @Valid @RequestBody List<AdvancedSearchRequest> requests) {
        
        try {
            // 批量请求限制
            if (requests.size() > 10) {
                return ResultBean.error("批量请求数量不能超过10个");
            }
            List<AdvancedSearchResponse<?>> results = new ArrayList<>();
            int successCount = 0;
            int failureCount = 0;

            for (AdvancedSearchRequest request : requests) {
                try {
                    preprocessRequest(request);
                    AdvancedSearchResponse<?> response = advancedSearchService.search(request);
                    results.add(response);
                    if (response.getSuccess()) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (Exception e) {
                    log.error("批量搜索中单个请求失败", e);
                    results.add(AdvancedSearchResponse.failure("搜索失败：" + e.getMessage()));
                    failureCount++;
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("results", results);
            response.put("total", requests.size());
            response.put("successCount", successCount);
            response.put("failureCount", failureCount);
            
            return ResultBean.success(response);
            
        } catch (Exception e) {
            log.error("批量搜索异常", e);
            return ResultBean.error("批量搜索失败");
        }
    }
    /**
     * 请求预处理
     */
    private void preprocessRequest(AdvancedSearchRequest request) {
        // 设置默认值
        if (request.getPageNum() == null || request.getPageNum() < 1) {
            request.setPageNum(1);
        }
        if (request.getPageSize() == null || request.getPageSize() < 1) {
            request.setPageSize(20);
        }
        if (request.getPageSize() > 100) {
            request.setPageSize(100);
        }
        
        // 关键字预处理
        if (request.getKeyword() != null) {
            request.setKeyword(request.getKeyword().trim());
        }
        
        // 设置默认搜索类型
        if (request.getSearchType() == null) {
            request.setSearchType("department");
        }
        
        // 启用智能优化
        if (request.getEnableSmartOptimization() == null) {
            request.setEnableSmartOptimization(true);
        }
        
        // 默认启用缓存
        if (request.getEnableCache() == null) {
            request.setEnableCache(true);
        }
    }
}