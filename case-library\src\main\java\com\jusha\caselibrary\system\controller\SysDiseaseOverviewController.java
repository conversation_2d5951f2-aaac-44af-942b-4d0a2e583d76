package com.jusha.caselibrary.system.controller;

import com.jusha.caselibrary.common.resp.ResultBean;
import com.jusha.caselibrary.mybatisplus.system.entity.DiseaseOverview;
import com.jusha.caselibrary.system.dto.req.DiseaseOverviewSaveOrUpdate;
import com.jusha.caselibrary.system.service.SysDiseaseOverviewService;
import com.jusha.caselibrary.system.service.SysDiseaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 疾病概述信息
 * <AUTHOR>
 * @Date 2025/7/7 14:12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/diseaseOverview")
public class SysDiseaseOverviewController {
    
    private final SysDiseaseOverviewService sysDiseaseOverviewService;
    private final SysDiseaseService sysDiseaseService;

    /**
     * 根据疾病编号获取详细信息
     */
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long diseaseId) {
        return ResultBean.success(sysDiseaseOverviewService.selectDiseaseById(diseaseId));
    }

    /**
     * 新增或修改疾病概述
     */
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody DiseaseOverviewSaveOrUpdate diseaseOverviewSaveOrUpdate) {
        if(diseaseOverviewSaveOrUpdate.getDiseaseId()==null){
            return ResultBean.error();
        }
        if(sysDiseaseService.selectDiseaseById(diseaseOverviewSaveOrUpdate.getDiseaseId())==null){
            return ResultBean.error();
        }
        return ResultBean.success(sysDiseaseOverviewService.editDiseaseOverview(diseaseOverviewSaveOrUpdate));
    }

    /**
     * 删除疾病概述
     */
    @PostMapping("/remove")
    public ResultBean remove(@Validated @RequestBody DiseaseOverview diseaseOverview) {
        if(diseaseOverview.getDiseaseId()==null){
            return ResultBean.error();
        }
        return ResultBean.success(sysDiseaseOverviewService.deleteDiseaseById(diseaseOverview.getDiseaseId()));
    }
}
