package com.jusha.auth.mybatisplus.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jusha.auth.mybatisplus.entity.SysConfig;
import com.jusha.auth.mybatisplus.mapper.SysConfigMapper;
import com.jusha.auth.mybatisplus.service.SysConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 参数配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {

}
