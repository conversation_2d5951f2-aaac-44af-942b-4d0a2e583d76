package com.jusha.auth.system.service;

import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.mybatisplus.entity.SysPlat;
import java.util.List;

/**
 * 业务平台信息Service接口
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
public interface ISysPlatService {

    /**
     * 新增业务平台信息
     *
     * @param sysPlat 业务平台信息
     * @return 结果
     */
    public ResultBean insertSysPlat(SysPlat sysPlat);

    /**
     * 查询业务平台信息列表
     *
     * @param sysPlat 业务平台信息
     * @return 业务平台信息集合
     */
    public List<SysPlat> selectSysPlatList(SysPlat sysPlat);

    /**
     * 查询业务平台信息
     *
     * @param platId 业务平台信息主键
     * @return 业务平台信息
     */
    public SysPlat selectSysPlatByPlatId(Long platId);


    /**
     * 修改业务平台信息
     *
     * @param sysPlat 业务平台信息
     * @return 结果
     */
    public ResultBean updateSysPlat(SysPlat sysPlat);

    /**
     * 批量删除业务平台信息
     *
     * @param platId 需要删除的业务平台信息主键集合
     * @return 结果
     */
    public ResultBean deleteSysPlatByPlatId(Long platId);


    /**
     * 校验平台名称是否唯一
     *
     * @param sysPlat 平台信息
     * @return 结果
     */
    public boolean checkPlatNameUnique(SysPlat sysPlat);
}
