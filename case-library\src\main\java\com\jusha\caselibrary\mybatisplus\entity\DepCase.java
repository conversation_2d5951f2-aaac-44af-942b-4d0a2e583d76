package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 科室病例表
 * <AUTHOR>
 */
@TableName("t_dep_case")
@Data
public class DepCase {

    @ApiModelProperty(value = "病例id")
    @TableId("case_id")
    private Long caseId;    

    @ApiModelProperty(value = "病例名称")
    @TableField("case_name")
    private String caseName;    

    @ApiModelProperty(value = "病例编号")
    @TableField("case_no")
    private String caseNo;    

    @ApiModelProperty(value = "疾病id")
    @TableField("disease_id")
    private Long diseaseId;    

    @ApiModelProperty(value = "最终诊断(疾病名称)")
    @TableField("diagnosis")
    private String diagnosis;    

    @ApiModelProperty(value = "患者id")
    @TableField("patient_id")
    private String patientId;    

    @ApiModelProperty(value = "患者姓名")
    @TableField("patient_name")
    private String patientName;    

    @ApiModelProperty(value = "性别")
    @TableField("patient_sex")
    private String patientSex;    

    @ApiModelProperty(value = "出生日期")
    @TableField("patient_birth_date")
    private String patientBirthDate;    

    @ApiModelProperty(value = "年龄")
    @TableField("patient_age")
    private String patientAge;    

    @ApiModelProperty(value = "病史")
    @TableField("medical_history")
    private String medicalHistory;    

    @ApiModelProperty(value = "难度等级：字典")
    @TableField("difficulty")
    private String difficulty;    

    @ApiModelProperty(value = "1典型2非典型")
    @TableField("case_category")
    private String caseCategory;    

    @ApiModelProperty(value = "征象")
    @TableField("sign")
    private String sign;    

    @ApiModelProperty(value = "病例分析")
    @TableField("case_analysis")
    private String caseAnalysis;    

    @ApiModelProperty(value = "是否已导出: 0否 1是")
    @TableField("is_export")
    private String isExport;    

    @ApiModelProperty(value = "联盟分组ID")
    @TableField("lm_gp_id")
    private Long lmGpId;    

    @ApiModelProperty(value = "来源")
    @TableField("source_type")
    private String sourceType;    

    @ApiModelProperty(value = "随访状态")
    @TableField("follow_status")
    private String followStatus;    

    @ApiModelProperty(value = "定性匹配，字典")
    @TableField("quality_match")
    private String qualityMatch;    

    @ApiModelProperty(value = "定位匹配，字典")
    @TableField("position_match")
    private String positionMatch;    

    @ApiModelProperty(value = "删除标志0正常1删除")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;    

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private Long createBy;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "修改人")
    @TableField("update_by")
    private Long updateBy;    

    @ApiModelProperty(value = "修改时间")
    @TableField("update_time")
    private Date updateTime;    

    @ApiModelProperty(value = "历史修改人")
    @TableField("modify_users")
    private String modifyUsers;    

    @ApiModelProperty(value = "引用晨会列表")
    @TableField("meetings")
    private String meetings;    

}
