package com.jusha.auth.system.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.TreeSelect;
import com.jusha.auth.common.core.page.PageUtils;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.core.text.Convert;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.SysGroup;
import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.mybatisplus.service.SysGroupService;
import com.jusha.auth.mybatisplus.service.SysRoleGroupService;
import com.jusha.auth.system.domain.GroupDto;
import com.jusha.auth.system.domain.GroupMsgDto;
import com.jusha.auth.system.mapper.ISysGroupMapper;
import com.jusha.auth.system.mapper.ISysRoleMapper;
import com.jusha.auth.system.service.ISysGroupService;
import com.jusha.auth.system.service.ISysPlatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分组管理 服务实现
 * <AUTHOR>
 */
@Service
public class ISysGroupServiceImpl implements ISysGroupService {
    private static final Logger log = LoggerFactory.getLogger(ISysGroupServiceImpl.class);

    @Autowired
    private ISysGroupMapper groupMapper;

    @Autowired
    private ISysRoleMapper roleMapper;

    @Autowired
    private SysGroupService sysGroupService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysRoleGroupService sysRoleGroupService;

    @Autowired
    private ISysPlatService iSysPlatService;

    /**
     * 查询分组管理数据
     *
     * @param group 分组信息
     * @return 分组信息集合
     */
    @Override
    public List<SysGroup> selectGroupList(SysGroup group) {
        LambdaQueryChainWrapper<SysGroup> wrapper = sysGroupService.lambdaQuery();
        if (group.getGroupName() != null) {
            wrapper.like(SysGroup::getGroupName, group.getGroupName());
        }
//        if (group.getStatus() != null) {
//            wrapper.eq(SysGroup::getStatus, group.getStatus());
//        }
        if (group.getPlatId() != null) {
            wrapper.eq(SysGroup::getPlatId, group.getPlatId());
        }
        if (group.getGroupId() != null) {
            wrapper.eq(SysGroup::getGroupId, group.getGroupId());
        }
        if (group.getParentId() != null) {
            wrapper.eq(SysGroup::getParentId, group.getParentId());
        }
        if (group.getCenter() != null) {
            wrapper.eq(SysGroup::getCenter, group.getCenter());
        }
        if(group.getAncestors()!=null){
            //根据顶层分组来查询树，加上顶层
            wrapper.apply( "FIND_IN_SET ( '"+ group.getAncestors() +"',ancestors )");
            wrapper.or().eq(SysGroup::getGroupId,group.getAncestors());
        }
//        //这里要根据用户过滤一下分组的数据权限才可以
//        if(!TokenService.isAdmin(tokenService.getUserId())){
//            List<SysRole> SysRoles = tokenService.getLoginUser().getSysUser().getRoles();
//            if(!SysRoles.isEmpty()){
//                List<String> roleIds = new ArrayList<>();
//                for(SysRole sysRole : SysRoles){
//                    roleIds.add(sysRole.getRoleId()+"");
//                }
//                List<String> groupIds = new ArrayList<>();
//                List<SysRoleGroup> sysRoleGroups = sysRoleGroupService.lambdaQuery().in(SysRoleGroup::getRoleId, roleIds).list();
//                log.info("sysRoleGroups.size()=========="+sysRoleGroups.size());
//                for(SysRoleGroup sysRoleGroup : sysRoleGroups){
//                    groupIds.add(sysRoleGroup.getGroupId()+"");
//                }
//                if(groupIds.isEmpty()){
//                    return new ArrayList<>();
//                }else {
//                    wrapper.in(SysGroup::getGroupId, groupIds);
//                }
//            }
//        }
        PageUtils.startPage();
        return wrapper.orderByAsc(SysGroup::getOrderNum).orderByDesc(SysGroup::getCreateTime).list();
    }

    @Override
    public List<SysGroup> groupListInPlat(Long platId){
        return sysGroupService.lambdaQuery().eq(SysGroup::getPlatId, platId).list();
    }

    /**
     * 根据分组ID查询信息
     *
     * @param groupId 分组ID
     * @return 分组信息
     */
    @Override
    public SysGroup selectGroupById(Long groupId) {
        return sysGroupService.getById(groupId);
    }

    /**
     * 新增保存分组信息
     *
     * @param group 分组信息
     * @return 结果
     */
    @Override
    public SysGroup insertGroup(SysGroup group) {
        long groupId = YitIdHelper.nextId();
        group.setGroupId(groupId);
        group.setCreateTime(DateUtils.getNowDate());
        group.setCreateBy(tokenService.getUserIdCanNull());
        SysGroup info = sysGroupService.getById(group.getParentId());
        if (info == null) {
            group.setAncestors("0");
            sysGroupService.save(group);
            return group;
        }
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!Constants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new ServiceException(MessageUtils.message("group.stop.message"));
        }
        group.setAncestors(info.getAncestors() + Constants.SEPARATOR + group.getParentId());
        sysGroupService.save(group);
        return group;
    }

    /**
     * 新增平台的时候需要对应新建一个根分组
     *
     * @param group
     * @return
     */
    @Override
    public boolean insertRootGroup(SysGroup group) {
        group.setGroupId(YitIdHelper.nextId());
        group.setCreateBy(tokenService.getUserId());
        group.setCreateTime(DateUtils.getNowDate());
        group.setAncestors(Constants.DEPT_ROOT);
        return sysGroupService.save(group);
    }

    /**
     * 查询分组树结构信息
     *
     * @param group 分组信息
     * @return 分组树信息集合
     */
    @Override
    public List<TreeSelect> selectGroupTreeList(SysGroup group) {
        List<SysGroup> groups = selectGroupList(group);
        return buildGroupTreeSelect(groups);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param groups 分组列表
     * @return 树结构列表
     */
    @Override
    public List<SysGroup> buildGroupTree(List<SysGroup> groups) {
        List<SysGroup> returnList = new ArrayList<SysGroup>();
        List<Long> tempList = groups.stream().map(SysGroup::getGroupId).collect(Collectors.toList());
        for (SysGroup group : groups) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(group.getParentId())) {
                recursionFn(groups, group);
                returnList.add(group);
            }
        }
        if (returnList.isEmpty()) {
            returnList = groups;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param groups 分组列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildGroupTreeSelect(List<SysGroup> groups) {
        List<SysGroup> groupTrees = buildGroupTree(groups);
        return groupTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询分组树信息
     *
     * @param roleId 角色ID
     * @return 选中分组列表
     */
    @Override
    public List<Long> selectGroupListByRoleId(Long roleId,Long platId) {
        return groupMapper.selectGroupListByRoleId(roleId,platId);
    }

    /**
     * 根据ID查询所有子分组（正常状态）
     *
     * @param groupId 分组ID
     * @return 子分组数
     */
    @Override
    public int selectNormalChildrenGroupById(Long groupId) {
        return groupMapper.selectNormalChildrenGroupById(groupId);
    }

    /**
     * 是否存在子节点
     *
     * @param groupId 分组ID
     * @return 结果
     */
    @Override
    public boolean hasChildByGroupId(Long groupId) {
        long result = sysGroupService.lambdaQuery().eq(SysGroup::getParentId, groupId).count();
        return result > Constants.ZERO_LONG;
    }

    /**
     * 校验分组名称是否唯一
     *
     * @param group 分组信息
     * @return 结果
     */
    @Override
    public boolean checkGroupNameUnique(SysGroup group) {
        Long groupId = StringUtils.isNull(group.getGroupId()) ? Constants.ALL_MINUS1L : group.getGroupId();
        List<SysGroup> groupInfos = sysGroupService.lambdaQuery()
                .eq(SysGroup::getGroupName, group.getGroupName())
                .eq(SysGroup::getParentId, group.getParentId())
                .eq(SysGroup::getPlatId, group.getPlatId()).list();
        if (!groupInfos.isEmpty() && groupInfos.get(0).getGroupId().longValue() != groupId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 校验中心分组是否唯一
     *
     * @param group 分组信息
     * @return 结果
     */
    @Override
    public boolean checkGroupCenterUnique(SysGroup group) {
        Long groupId = StringUtils.isNull(group.getGroupId()) ? Constants.ALL_MINUS1L : group.getGroupId();
        List<SysGroup> groupInfos = sysGroupService.lambdaQuery()
                .eq(SysGroup::getCenter, "0")
                .eq(SysGroup::getParentId, group.getParentId())
                .eq(SysGroup::getPlatId, group.getPlatId()).list();
        if (!groupInfos.isEmpty() && groupInfos.get(0).getGroupId().longValue() != groupId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }


    /**
     * 校验分组是否有数据权限
     *
     * @param groupId 分组id
     */
    @Override
    public void checkGroupDataScope(Long groupId,String platId) {
        if (platId == null) {
            throw new ServiceException(MessageUtils.message("take.plate.parameters"));
        }
        if (iSysPlatService.selectSysPlatByPlatId(Long.parseLong(platId)) == null) {
            throw new ServiceException(MessageUtils.message("this.plate.not.exist"));
        }
        Long userId = tokenService.getUserId();
        if (!SysUser.isAdmin(userId)) {
            SysGroup group = new SysGroup();
            group.setGroupId(groupId);
            group.setPlatId(Long.parseLong(platId));
            List<SysGroup> groups = selectGroupList(group);
            if (StringUtils.isEmpty(groups)) {
                throw new ServiceException(MessageUtils.message("no.permission.visit.group"));
            }
        }
    }

    /**
     * 修改保存分组信息
     *
     * @param group 分组信息
     * @return 结果
     */
    @Override
    public ResultBean updateGroup(SysGroup group) {
        Long groupId = group.getGroupId();
        if (!checkGroupNameUnique(group)) {
            //验证名称唯一性
            return ResultBean.error(MessageUtils.message("group.already.exist.edit"));
        } else if (group.getParentId().equals(groupId)) {
            //不可以把自己作为父分組
            return ResultBean.error(MessageUtils.message("group.parent.self.edit"));
        } else if (StringUtils.equals(Constants.DEPT_DISABLE, group.getStatus()) && selectNormalChildrenGroupById(groupId) > 0) {
            //下级分组全部停用时，该分组才可以停用
            return ResultBean.error(MessageUtils.message("group.child.not.stop.edit"));
        }
        group.setUpdateBy(tokenService.getUserId());
        group.setUpdateTime(DateUtils.getNowDate());
        SysGroup newParentGroup = sysGroupService.lambdaQuery().eq(SysGroup::getGroupId, group.getParentId()).one();
        SysGroup oldGroup = sysGroupService.lambdaQuery().eq(SysGroup::getGroupId, group.getGroupId()).one();
        if (StringUtils.isNotNull(newParentGroup) && StringUtils.isNotNull(oldGroup)) {
            String newAncestors = newParentGroup.getAncestors() + "," + newParentGroup.getGroupId();
            String oldAncestors = oldGroup.getAncestors();
            group.setAncestors(newAncestors);
            updateGroupChildren(group.getGroupId(), newAncestors, oldAncestors);
        }
        boolean result = sysGroupService.updateById(group);
        if (Constants.DEPT_NORMAL.equals(group.getStatus())
                && StringUtils.isNotEmpty(group.getAncestors())
                && !StringUtils.equals(Constants.NORMAL, group.getAncestors())) {
            // 如果该分组是启用状态，则启用该分组的所有上级分组
            updateParentGroupStatusNormal(group);
        }
        if (!result) {
            return ResultBean.error();
        }
        return ResultBean.success();
    }

    /**
     * 修改该分组的父级分组状态
     *
     * @param group 当前分组
     */
    private void updateParentGroupStatusNormal(SysGroup group) {
        String ancestors = group.getAncestors();
        Long[] groupIds = Convert.toLongArray(ancestors);
        sysGroupService.lambdaUpdate().set(SysGroup::getStatus, Constants.DEPT_NORMAL)
                .in(SysGroup::getGroupId, groupIds).update();
    }

    /**
     * 修改子元素关系
     *
     * @param groupId      被修改的分组ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateGroupChildren(Long groupId, String newAncestors, String oldAncestors) {
        List<SysGroup> children = groupMapper.selectChildrenGroupById(groupId);
        for (SysGroup child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (!children.isEmpty()) {
            groupMapper.updateGroupChildren(children);
        }
    }

    /**
     * 删除分组管理信息
     *
     * @param groupId 分组ID
     * @return 结果
     */
    @Override
    public boolean deleteGroupById(Long groupId) {
        return sysGroupService.lambdaUpdate()
                .set(SysGroup::getUpdateBy, tokenService.getUserId())
                .set(SysGroup::getUpdateTime, DateUtils.getNowDate())
                .set(SysGroup::getDelFlag, Constants.DELETE_FLAG)
                .eq(SysGroup::getGroupId, groupId).update();
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysGroup> list, SysGroup t) {
        // 得到子节点列表
        List<SysGroup> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysGroup tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysGroup> getChildList(List<SysGroup> list, SysGroup t) {
        List<SysGroup> tlist = new ArrayList<SysGroup>();
        Iterator<SysGroup> it = list.iterator();
        while (it.hasNext()) {
            SysGroup n = (SysGroup) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getGroupId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysGroup> list, SysGroup t) {
        return getChildList(list, t).size() > 0;
    }

    @Override
    public boolean hasChildByPlatId(Long platId) {
        List<SysGroup> groupList = sysGroupService.lambdaQuery()
                .eq(SysGroup::getPlatId, platId)
                .ne(SysGroup::getParentId, 0L).list();
        return !groupList.isEmpty();
    }

    /**
     * 删除分组管理信息
     *
     * @param platId 所属平台ID
     * @return 结果
     */
    @Override
    public boolean deleteGroupByPlatId(Long platId) {
        return sysGroupService.lambdaUpdate()
                .set(SysGroup::getUpdateBy, tokenService.getUserId())
                .set(SysGroup::getUpdateTime, DateUtils.getNowDate())
                .set(SysGroup::getDelFlag, Constants.DELETE_FLAG)
                .eq(SysGroup::getPlatId, platId).update();
    }

    @Override
    public ResultBean getGroupIdList(String platId) {
        //第一步，获取自身所属角色id
        SysUser user = tokenService.getLoginUser().getSysUser();
        List<SysRole> myRoles = new ArrayList<>();
        List<SysRole> userRoles = user.getRoles();
        for(SysRole role : userRoles) {
            if((role.getPlatId()+"").equals(platId) || (role.getRoleId()==1L)){
                myRoles.add(role);
            }
        }
        //第二步，合并，返回groupID的集合
        Set<Long> groupIds = new HashSet<>();
        for(SysRole sysRole : myRoles){
            groupIds.addAll(sysRole.getGroupList());
        }
        return ResultBean.success(groupIds);
    }

    @Override
    public ResultBean getAllianceMsg(String platId) {
        GroupMsgDto groupMsgDto = new GroupMsgDto();
        //第一步，获取自身所属角色id
        SysUser user = tokenService.getLoginUser().getSysUser();
        List<SysRole> myRoles = new ArrayList<>();
        List<SysRole> userRoles = user.getRoles();
        for(SysRole role : userRoles) {
            if((role.getPlatId()+"").equals(platId) || (role.getRoleId()==1L)){
                myRoles.add(role);
            }
        }
        //如果只有一个角色，那么直接返回，如果有多个角色，那么，就取第一个，一般来说不存在这种情况
        if(myRoles.isEmpty()){
            return ResultBean.error("未分配角色","");
        }else{
            long sysGroupId = myRoles.get(0).getSysGroupId();
            //医院id直接取
            groupMsgDto.setSysGroupId(sysGroupId);
            //联盟id直接取
            groupMsgDto.setLmGroupId(myRoles.get(0).getAllianceId());
            //中心医院id要判断一下
            SysGroup sysGroup = sysGroupService.getById(sysGroupId);
            if(sysGroup.getCenter().equals("0")){
                groupMsgDto.setCenterGroupId(sysGroupId);
            }else {
                List<SysGroup> sysGroups = sysGroupService.lambdaQuery().eq(SysGroup::getParentId,sysGroup.getParentId()).list();
                List<SysGroup> filteredGroups = sysGroups.stream().filter(group -> group.getCenter() .equals("0")).collect(Collectors.toList());
                if(!filteredGroups.isEmpty()){
                    groupMsgDto.setCenterGroupId(filteredGroups.get(0).getGroupId());
                }
            }
        }
        return ResultBean.success(groupMsgDto);
    }

    @Override
    public ResultBean getAllianceList(String platId) {
        List<GroupDto> alliances = new ArrayList<>();
        List<SysGroup> rootGroupList = sysGroupService.lambdaQuery().eq(SysGroup::getParentId,0).eq(SysGroup::getPlatId,platId).list();
        for(SysGroup rootGroup : rootGroupList){
            List<SysGroup> allianceGroupList = sysGroupService.lambdaQuery().eq(SysGroup::getParentId,rootGroup.getGroupId()).eq(SysGroup::getPlatId,platId).list();
            if(!allianceGroupList.isEmpty()){
                for(SysGroup allianceGroup: allianceGroupList){
                    GroupDto groupDto = new GroupDto();
                    groupDto.setGroupName(allianceGroup.getGroupName());
                    groupDto.setGroupId(allianceGroup.getGroupId());
                    alliances.add(groupDto);
                }
            }
        }
        return ResultBean.success(alliances);
    }
}