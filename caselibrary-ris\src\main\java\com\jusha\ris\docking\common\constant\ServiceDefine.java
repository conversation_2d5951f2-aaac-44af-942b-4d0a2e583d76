package com.jusha.ris.docking.common.constant;

/**
 * <AUTHOR>
 * @title: ServiceEnum
 * @description 平台服务类型
 * @date 2021/12/14
 */


public class ServiceDefine {
    /**
     * 请求前缀
     */
    /**
     * 公开请求，不需要登录校验
     */
    public final static String OPEN = "/open";

    /**
     * 需要登录校验的请求
     */
    public final static String VERIFY = "/verify";

    /**
     * 机制外部请求
     */
    public final static String FORBID = "/forbid";

}
