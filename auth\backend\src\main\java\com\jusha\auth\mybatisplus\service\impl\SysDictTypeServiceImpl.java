package com.jusha.auth.mybatisplus.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jusha.auth.mybatisplus.entity.SysDictType;
import com.jusha.auth.mybatisplus.mapper.SysDictTypeMapper;
import com.jusha.auth.mybatisplus.service.SysDictTypeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 字典类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Service
public class SysDictTypeServiceImpl extends ServiceImpl<SysDictTypeMapper, SysDictType> implements SysDictTypeService {

}
