package com.jusha.caselibrary.common.aop;

import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.search.dto.SyncMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

/**
 * @ClassName ESSyncAspect
 * @Description ES同步切面
 *  * 拦截标记了@ESSync注解的方法，异步同步数据到ES
 *  *
 * <AUTHOR>
 * @Date 2025/7/7 15:18
 **/
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ESSyncAspect {

    private final RedisTemplate<String, Object> redisTemplate;

    private final SearchConfig searchConfig;

    /**
     * 定义切点：所有标记了@ESSync注解的方法
     */
    @Pointcut("@annotation(com.jusha.caselibrary.common.aop.ESSync)")
    public void esSyncPointcut() {
    }

    /**
     * 方法执行成功后处理
     */
    @AfterReturning(pointcut = "esSyncPointcut()", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            ESSync esSync = method.getAnnotation(ESSync.class);

            if (esSync == null) {
                return;
            }

            // 构建同步消息
            SyncMessage syncMessage = buildSyncMessage(joinPoint, esSync, result);

            if (syncMessage == null) {
                log.warn("构建ES同步消息失败，方法：{}", method.getName());
                return;
            }

            // 发送到Redis队列
            sendToQueue(syncMessage, esSync);

            log.info("ES同步消息已发送到队列，消息ID：{}，操作类型：{}，病例类型：{}",
                    syncMessage.getMessageId(), syncMessage.getOperation(), syncMessage.getCaseType());

        } catch (Exception e) {
            log.error("ES同步切面处理异常", e);
        }
    }

    /**
     * 构建同步消息
     */
    private SyncMessage buildSyncMessage(JoinPoint joinPoint, ESSync esSync, Object result) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Object[] args = joinPoint.getArgs();

            String caseType = determineCaseType(esSync, args);
            String operation = mapSyncTypeToOperation(esSync.type());
            if (caseType == null || operation == null) {
                log.warn("无法确定病例类型或操作类型，方法：{}", method.getName());
                return null;
            }
            // 单个操作
            Long caseId = extractCaseId(args, result, esSync);
            SyncMessage message = new SyncMessage(caseId, caseType, operation);

            // 个人病例库不再需要提取catalogId，简化逻辑
            // catalogId的处理将在ES同步服务中通过查询数据库来获取

            message.setDelayTime(esSync.delay() > 0 ? esSync.delay() : null);

            return message;
        } catch (Exception e) {
            log.error("构建ES同步消息异常", e);
            return null;
        }
    }


    /**
     * 确定病例类型
     */
    private String determineCaseType(ESSync esSync, Object[] args) {
        // 根据indexType确定病例类型
        String indexType = esSync.indexType();
        if (Constant.DEP_CASE_INDEX_NAME.equals(indexType)) {
            return Constant.DEP_CASE_INDEX_NAME;
        } else if (Constant.PERSON_CASE_INDEX_NAME.equals(indexType)) {
            return Constant.PERSON_CASE_INDEX_NAME;
        } else {
            log.warn("未知的病例类型：{}", indexType);
            return null;
        }
    }


    /**
     * 提取病例ID
     * 支持从多个来源获取ID：参数、返回值、对象属性等
     */
    private Long extractCaseId(Object[] args, Object result, ESSync esSync) {
        Long caseId;

        try {
            // 1. 优先从返回值中提取ID（适用于创建操作，返回新创建的对象）
            if ((result instanceof Number || result instanceof String)) {
                caseId = extractIdFromObject(result, esSync.idField());
                if (caseId != null) {
                    log.debug("从返回值中提取到病例ID: {}", caseId);
                    return caseId;
                }
            }

            // 2. 从参数中提取ID
            // 遍历所有参数，寻找包含ID的对象
            for (Object arg : args) {
                if (arg != null) {
                    caseId = extractIdFromObject(arg, esSync.idField());
                    if (caseId != null) {
                        log.debug("从参数列表中提取到病例ID: {}", caseId);
                        return caseId;
                    }
                }
            }

            // 3. 如果指定了特定的ID字段名，尝试从不同来源提取
            if (StringUtils.hasText(esSync.idField()) && !Constant.ES_INDEX_ID.equals(esSync.idField())) {
                // 从返回值中尝试其他字段
                if (result != null) {
                    caseId = extractIdFromObject(result, esSync.idField());
                    if (caseId != null) {
                        log.debug("从返回值的{}字段中提取到病例ID: {}", esSync.idField(), caseId);
                        return caseId;
                    }
                }

                // 从参数中尝试其他字段
                for (Object arg : args) {
                    if (arg != null) {
                        caseId = extractIdFromObject(arg, esSync.idField());
                        if (caseId != null) {
                            log.debug("从参数的{}字段中提取到病例ID: {}", esSync.idField(), caseId);
                            return caseId;
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("提取病例ID失败", e);
        }

        log.warn("无法提取病例ID，参数数量: {}, 返回值类型: {}",
                args.length, result != null ? result.getClass().getSimpleName() : "null");
        return null;
    }


    /**
     * 从对象中提取ID
     */
    private Long extractIdFromObject(Object obj, String idField) {
        if (obj == null) {
            return null;
        }

        try {
            Object id = extractId(obj, idField);
            return convertToLong(id);
        } catch (Exception e) {
            log.debug("从对象{}中提取ID失败: {}", obj.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 将对象转换为Long类型
     */
    private Long convertToLong(Object id) {
        if (id == null) {
            return null;
        }

        if (id instanceof Long) {
            return (Long) id;
        } else if (id instanceof Number) {
            return ((Number) id).longValue();
        } else if (id instanceof String) {
            String strId = (String) id;
            if (StringUtils.hasText(strId)) {
                try {
                    return Long.parseLong(strId);
                } catch (NumberFormatException e) {
                    log.debug("无法将字符串'{}'转换为Long", strId);
                }
            }
        }

        return null;
    }

    /**
     * 将同步类型映射为操作类型
     */
    private String mapSyncTypeToOperation(ESSync.SyncType syncType) {
        switch (syncType) {
            case CREATE:
                return Constant.OPERATION_TYPE_CREATE;
            case UPDATE:
                return Constant.OPERATION_TYPE_UPDATE;
            case DELETE:
                return Constant.OPERATION_TYPE_DELETE;
            default:
                return null;
        }
    }


    /**
     * 确定索引名称
     */
    private String determineIndexName(ESSync esSync, Object[] args) {
        if (StringUtils.hasText(esSync.index())) {
            return esSync.index();
        }

        // 根据indexType确定索引名称
        String indexType = esSync.indexType();
        if (Constant.DEP_CASE_INDEX_NAME.equals(indexType)) {
            return searchConfig.getIndexes().getDepartmentCases();
        } else if (Constant.PERSON_CASE_INDEX_NAME.equals(indexType)) {
            return searchConfig.getIndexes().getPersonalCases();
        } else {
            log.warn("未知的索引类型：{}", indexType);
            throw new IllegalArgumentException("未知的索引类型：" + indexType);
        }
    }


    /**
     * 提取文档ID
     */
    private Object extractId(Object data, String idField) {
        try {
            if (data == null) {
                return null;
            }
            // 如果传入的是基本类型（Number或String），直接返回该值
            if (data instanceof Number || data instanceof String) {
                return data;
            }
            // 使用反射获取ID字段值
            String fieldName = StringUtils.hasText(idField) ? idField : "caseId";
            java.lang.reflect.Field field = data.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(data);
        } catch (Exception e) {
            log.warn("提取文档ID失败，字段：{}，数据：{}", idField, data);
            return null;
        }
    }

    /**
     * 发送消息到队列
     */
    private void sendToQueue(SyncMessage message, ESSync esSync) {
        try {
            String queueName = esSync.delay() > 0 ? searchConfig.getMq().getQueues().getDelayQueue()
                    : searchConfig.getMq().getQueues().getSyncQueue();

            if (esSync.async()) {
                // 异步发送
                redisTemplate.opsForList().leftPush(queueName, message);
            } else {
                // 同步发送（立即处理）
                redisTemplate.opsForList().leftPush(searchConfig.getMq().getQueues().getDelayQueue(), message);
            }
        } catch (Exception e) {
            log.error("发送ES同步消息到Redis队列失败", e);
        }
    }
}