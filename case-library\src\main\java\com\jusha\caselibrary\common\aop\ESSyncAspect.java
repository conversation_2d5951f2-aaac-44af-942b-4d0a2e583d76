package com.jusha.caselibrary.common.aop;

import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.search.dto.SyncMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;

/**
 * @ClassName ESSyncAspect
 * @Description ES同步切面
 *  * 拦截标记了@ESSync注解的方法，异步同步数据到ES
 *  *
 * <AUTHOR>
 * @Date 2025/7/7 15:18
 **/
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ESSyncAspect {

    private final RedisTemplate<String, Object> redisTemplate;

    private final SearchConfig searchConfig;

    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    /**
     * 定义切点：所有标记了@ESSync注解的方法
     */
    @Pointcut("@annotation(com.jusha.caselibrary.common.aop.ESSync)")
    public void esSyncPointcut() {
    }

    /**
     * 方法执行成功后处理
     */
    @AfterReturning(pointcut = "esSyncPointcut()", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            ESSync esSync = method.getAnnotation(ESSync.class);

            if (esSync == null) {
                return;
            }

            // 构建同步消息
            SyncMessage syncMessage = buildSyncMessage(joinPoint, esSync, result);

            if (syncMessage == null) {
                log.warn("构建ES同步消息失败，方法：{}", method.getName());
                return;
            }

            // 发送到Redis队列
            sendToQueue(syncMessage, esSync);

            log.info("ES同步消息已发送到队列，消息ID：{}，操作类型：{}，索引类型：{}",
                    syncMessage.getMessageId(), syncMessage.getOperation(), syncMessage.getIndexType());

        } catch (Exception e) {
            log.error("ES同步切面处理异常", e);
        }
    }

    /**
     * 构建同步消息
     */
    private SyncMessage buildSyncMessage(JoinPoint joinPoint, ESSync esSync, Object result) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Object[] args = joinPoint.getArgs();

            String caseType = determineIndexType(esSync, args);
            String operation = mapSyncTypeToOperation(esSync.type());
            if (caseType == null || operation == null) {
                log.warn("无法确定索引类型或操作类型，方法：{}", method.getName());
                return null;
            }

            // 检查是否为批量操作
            if (isBatchOperation(esSync.type())) {
                return buildBatchSyncMessage(args, result, esSync, caseType, operation);
            } else {
                // 单个操作
                Long caseId = extractIndexId(args, result, esSync, method);
                SyncMessage message = new SyncMessage(caseId, caseType, operation);

                // 个人病例库不再需要提取catalogId，简化逻辑
                // catalogId的处理将在ES同步服务中通过查询数据库来获取

                message.setDelayTime(esSync.delay() > 0 ? esSync.delay() : null);

                return message;
            }
        } catch (Exception e) {
            log.error("构建ES同步消息异常", e);
            return null;
        }
    }

    /**
     * 判断是否为批量操作
     */
    private boolean isBatchOperation(ESSync.SyncType syncType) {
        return syncType == ESSync.SyncType.BATCH_CREATE ||
               syncType == ESSync.SyncType.BATCH_UPDATE ||
               syncType == ESSync.SyncType.BATCH_DELETE;
    }

    /**
     * 构建批量同步消息
     */
    private SyncMessage buildBatchSyncMessage(Object[] args, Object result, ESSync esSync, String caseType, String operation) {
        try {
            // 提取完整的ID集合用于批量操作
            List<Long> indexIdList = extractIndexIdList(args, result, esSync);
            
            if (indexIdList == null || indexIdList.isEmpty()) {
                log.warn("批量操作未能提取到有效的ID集合");
                return null;
            }
            
            // 使用新的批量构造方法创建SyncMessage
            SyncMessage message = new SyncMessage(indexIdList, caseType, operation);
            message.setDelayTime(esSync.delay() > 0 ? esSync.delay() : null);

            log.info("构建批量同步消息成功，ID数量: {}, 操作类型: {}", indexIdList.size(), operation);
            return message;
        } catch (Exception e) {
            log.error("构建批量ES同步消息异常", e);
            return null;
        }
    }


    /**
     * 确定索引类型
     */
    private String determineIndexType(ESSync esSync, Object[] args) {
        // 根据indexType确定索引类型
        String indexType = esSync.indexType();
        if (Constant.DEP_CASE_INDEX_NAME.equals(indexType)) {
            return Constant.DEP_CASE_INDEX_NAME;
        } else if (Constant.PERSON_CASE_INDEX_NAME.equals(indexType)) {
            return Constant.PERSON_CASE_INDEX_NAME;
        } else {
            log.warn("未知的索引类型：{}", indexType);
            return null;
        }
    }

    /**
     * 提取索引ID（带方法信息）
     * 支持从多个来源获取ID：参数、返回值、对象属性等
     */
    private Long extractIndexId(Object[] args, Object result, ESSync esSync, Method method) {
        Long indexId;

        try {
            // 1. 优先从返回值中提取ID（适用于创建操作，返回新创建的对象）
            if (result != null) {
                indexId = extractIdFromObject(result, esSync.idField());
                if (indexId != null) {
                    log.debug("从返回值中提取到索引ID: {}", indexId);
                    return indexId;
                }
            }

            // 2. 从参数中提取ID（支持参数名匹配）
            if (method != null) {
                indexId = extractIdFromParameters(args, esSync.idField(), method);
                if (indexId != null) {
                    log.debug("通过参数名匹配提取到病例ID: {}", indexId);
                    return indexId;
                }
            }

            // 3. 从参数中提取ID（遍历所有参数，寻找包含ID的对象）
            for (Object arg : args) {
                if (arg != null) {
                    indexId = extractIdFromObject(arg, esSync.idField());
                    if (indexId != null) {
                        log.debug("从参数列表中提取到病例ID: {}", indexId);
                        return indexId;
                    }
                }
            }

        } catch (Exception e) {
            log.warn("提取病例ID失败", e);
        }

        log.warn("无法提取病例ID，参数数量: {}, 返回值类型: {}",
                args.length, result != null ? result.getClass().getSimpleName() : "null");
        return null;
    }

    /**
     * 通过参数名匹配从参数中提取ID
     */
    private Long extractIdFromParameters(Object[] args, String idField, Method method) {
        try {
            String[] paramNames = parameterNameDiscoverer.getParameterNames(method);
            if (paramNames == null || paramNames.length != args.length) {
                log.debug("无法获取参数名或参数数量不匹配");
                return null;
            }

            String targetFieldName = StringUtils.hasText(idField) ? idField : "caseId";

            // 遍历参数，查找参数名匹配的参数
            for (int i = 0; i < paramNames.length; i++) {
                String paramName = paramNames[i];
                Object paramValue = args[i];

                // 检查参数名是否匹配目标字段名
                if (targetFieldName.equals(paramName) && paramValue != null) {
                    if (paramValue instanceof Number || paramValue instanceof String) {
                        Object id = extractId(paramValue, idField);
                        Long result = convertToLong(id);
                        if (result != null) {
                            log.debug("通过参数名{}匹配到ID: {}", paramName, result);
                            return result;
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.debug("通过参数名提取ID失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从对象中提取ID
     * @param obj 数据对象
     * @param idField ID字段名
     */
    private Long extractIdFromObject(Object obj, String idField) {
        if (obj == null) {
            return null;
        }

        try {
            Object id = extractId(obj, idField);
            return convertToLong(id);
        } catch (Exception e) {
            log.debug("从对象{}中提取ID失败: {}", obj.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 将对象转换为Long类型
     */
    private Long convertToLong(Object id) {
        if (id == null) {
            return null;
        }

        if (id instanceof Long) {
            return (Long) id;
        } else if (id instanceof Number) {
            return ((Number) id).longValue();
        } else if (id instanceof String) {
            String strId = (String) id;
            if (StringUtils.hasText(strId)) {
                try {
                    return Long.parseLong(strId);
                } catch (NumberFormatException e) {
                    log.debug("无法将字符串'{}'转换为Long", strId);
                }
            }
        }

        return null;
    }

    /**
     * 将同步类型映射为操作类型
     */
    private String mapSyncTypeToOperation(ESSync.SyncType syncType) {
        switch (syncType) {
            case CREATE:
                return Constant.OPERATION_TYPE_CREATE;
            case UPDATE:
                return Constant.OPERATION_TYPE_UPDATE;
            case DELETE:
                return Constant.OPERATION_TYPE_DELETE;
            case BATCH_CREATE:
                return Constant.OPERATION_TYPE_BATCH_CREATE;
            case BATCH_UPDATE:
                return Constant.OPERATION_TYPE_BATCH_UPDATE;
            case BATCH_DELETE:
                return Constant.OPERATION_TYPE_BATCH_DELETE;
            default:
                return null;
        }
    }

    /**
     * 提取文档ID
     * 支持从以下类型的数据中提取ID：
     * 1. Number类型：根据调用上下文判断处理方式
     * 2. String类型：根据调用上下文判断处理方式
     * 3. 包含属性的对象：通过反射获取指定字段的值
     *
     * @param data 数据对象
     * @param idField 期望的ID字段名
     */
    private Object extractId(Object data, String idField) {
        if (data == null) {
            return null;
        }

        try {
            // 1. 如果传入的是基本数值类型
            if (data instanceof Number) {
                log.debug("从返回值中提取到Number类型的ID: {}", data);
                return data;
            }

            // 2. 如果传入的是字符串类型
            if (data instanceof String) {
                String strData = (String) data;
                if (StringUtils.hasText(strData)) {
                    log.debug("从返回值中提取到Number类型的ID: {}", data);
                    return strData;
                }
                return null;
            }

            // 3. 如果是包含属性的对象，使用反射获取指定字段的值
            String fieldName = StringUtils.hasText(idField) ? idField : "caseId";

            try {
                java.lang.reflect.Field field = data.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                Object fieldValue = field.get(data);

                if (fieldValue != null) {
                    log.debug("从对象{}的字段{}中提取到ID: {}",
                            data.getClass().getSimpleName(), fieldName, fieldValue);
                    return fieldValue;
                }

            } catch (NoSuchFieldException e) {
                log.debug("对象{}中不存在字段: {}", data.getClass().getSimpleName(), fieldName);

                // 尝试通过getter方法获取
                try {
                    String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    java.lang.reflect.Method getter = data.getClass().getMethod(getterName);
                    Object fieldValue = getter.invoke(data);

                    if (fieldValue != null) {
                        log.debug("通过getter方法{}从对象{}中提取到ID: {}",
                                getterName, data.getClass().getSimpleName(), fieldValue);
                        return fieldValue;
                    }

                } catch (Exception getterException) {
                    log.debug("通过getter方法获取字段{}失败: {}", fieldName, getterException.getMessage());
                }
            }

        } catch (IllegalAccessException e) {
            log.warn("访问字段{}时权限不足: {}", idField, e.getMessage());
        } catch (Exception e) {
            log.warn("提取文档ID时发生未预期的异常，字段：{}，数据类型：{}, 错误：{}",
                    idField, data.getClass().getSimpleName(), e.getMessage());
        }

        log.debug("无法从数据中提取ID，字段：{}，数据类型：{}", idField, data.getClass().getSimpleName());
        return null;
    }

    /**
     * 提取批量操作的ID集合
     * 支持从多个来源获取ID集合：参数、返回值、对象属性等
     */
    private List<Long> extractIndexIdList(Object[] args, Object result, ESSync esSync) {
        List<Long> indexIdList = new ArrayList<>();
        
        try {
            // 1. 优先从返回值中提取ID集合（适用于批量创建操作，返回新创建的对象集合）
            List<Long> resultIds = extractIdListFromObject(result, esSync.idField());
            if (resultIds != null && !resultIds.isEmpty()) {
                indexIdList.addAll(resultIds);
                log.debug("从返回值中提取到批量病例ID: {}", resultIds.size());
                return indexIdList;
            }

            // 2. 从参数中提取ID集合
            // 遍历所有参数，寻找包含ID集合的对象
            for (Object arg : args) {
                if (arg != null) {
                    List<Long> argIds = extractIdListFromObject(arg, esSync.idField());
                    if (argIds != null && !argIds.isEmpty()) {
                        indexIdList.addAll(argIds);
                        log.debug("从参数列表中提取到批量病例ID: {}", argIds.size());
                    }
                }
            }

            // 3. 如果指定了特定的ID字段名，尝试从不同来源提取
            if (StringUtils.hasText(esSync.idField()) && !Constant.ES_INDEX_ID.equals(esSync.idField())) {
                // 从返回值中尝试其他字段
                if (result != null) {
                    List<Long> otherIds = extractIdListFromObject(result, esSync.idField());
                    if (otherIds != null && !otherIds.isEmpty()) {
                        indexIdList.addAll(otherIds);
                        log.debug("从返回值的{}字段中提取到批量病例ID: {}", esSync.idField(), otherIds.size());
                    }
                }

                // 从参数中尝试其他字段
                for (Object arg : args) {
                    if (arg != null) {
                        List<Long> otherIds = extractIdListFromObject(arg, esSync.idField());
                        if (otherIds != null && !otherIds.isEmpty()) {
                            indexIdList.addAll(otherIds);
                            log.debug("从参数的{}字段中提取到批量病例ID: {}", esSync.idField(), otherIds.size());
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("提取批量病例ID失败", e);
        }

        log.debug("最终提取到的批量病例ID数量: {}", indexIdList.size());
        return indexIdList.isEmpty() ? null : indexIdList;
    }

    /**
     * 从对象中提取ID集合
     */
    private List<Long> extractIdListFromObject(Object obj, String idField) {
        if (obj == null) {
            return null;
        }

        List<Long> idList = new ArrayList<>();

        try {
            // 1. 如果对象本身就是Collection类型
            if (obj instanceof Collection) {
                Collection<?> collection = (Collection<?>) obj;
                for (Object item : collection) {
                    Long id = extractIdFromObject(item, idField);
                    if (id != null) {
                        idList.add(id);
                    }
                }
                return idList.isEmpty() ? null : idList;
            }

            // 2. 如果对象是数组类型
            if (obj.getClass().isArray()) {
                Object[] array = (Object[]) obj;
                for (Object item : array) {
                    Long id = extractIdFromObject(item, idField);
                    if (id != null) {
                        idList.add(id);
                    }
                }
                return idList.isEmpty() ? null : idList;
            }

            // 3. 如果对象是单个ID（Number或String）
            Long singleId = extractIdFromObject(obj, idField);
            if (singleId != null) {
                idList.add(singleId);
                return idList;
            }

        } catch (Exception e) {
            log.debug("从对象{}中提取ID集合失败: {}", obj.getClass().getSimpleName(), e.getMessage());
        }

        return null;
    }

    /**
     * 发送消息到队列
     */
    private void sendToQueue(SyncMessage message, ESSync esSync) {
        try {
            String queueName = esSync.delay() > 0 ? searchConfig.getMq().getQueues().getDelayQueue()
                    : searchConfig.getMq().getQueues().getSyncQueue();

            if (esSync.async()) {
                // 异步发送
                redisTemplate.opsForList().leftPush(queueName, message);
            } else {
                // 同步发送（立即处理）
                redisTemplate.opsForList().leftPush(searchConfig.getMq().getQueues().getDelayQueue(), message);
            }
        } catch (Exception e) {
            log.error("发送ES同步消息到Redis队列失败", e);
        }
    }
}