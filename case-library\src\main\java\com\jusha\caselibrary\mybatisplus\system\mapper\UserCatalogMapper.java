package com.jusha.caselibrary.mybatisplus.system.mapper;

import com.jusha.caselibrary.mybatisplus.system.entity.UserCatalog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 个人目录表 Mapper 接口
 * <AUTHOR>
 */
public interface UserCatalogMapper extends BaseMapper<UserCatalog> {

    /**
     * 根据ID查询所有下级目录列表
     * @param catalogId ID
     * @return 目录列表
     */
    public List<UserCatalog> selectChildrenUserCatalogById(@Param("catalogId") Long catalogId);


    /**
     * 修改子元素关系
     * @param catalogs 子元素
     * @return 结果
     */
    public int updateUserCatalogChildren(@Param("diseases") List<UserCatalog> catalogs);

}
