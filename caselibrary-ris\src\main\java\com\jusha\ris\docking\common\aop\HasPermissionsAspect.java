package com.jusha.ris.docking.common.aop;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
import com.jusha.ris.docking.common.beans.RedisUser;
import com.jusha.ris.docking.common.constant.Constant;
import com.jusha.ris.docking.common.util.LocaleUtil;
import com.jusha.ris.docking.common.util.LoginUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Set;

/**
 * 权限处理切面处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class HasPermissionsAspect {
    private static final Logger log = LoggerFactory.getLogger(HasPermissionsAspect.class);


    @Value(value = "${server.servlet.context-path}")
    private String contextPath;

    @Before("@annotation(hasPermissions)")
    public void doBefore(JoinPoint point, HasPermissions hasPermissions) throws Throwable {
        HttpServletRequest request = LoginUtil.getRequest();
        RedisUser loginUser = LoginUtil.getLoginUser();
        if (loginUser == null || CollectionUtils.isEmpty(loginUser.getPermissions())) {
            throw new ServiceException(LocaleUtil.getLocale("set.permission.first"));
        }
        String uri = request.getRequestURI();
        Set<String> permissions = loginUser.getPermissions();
        log.info("当前请求接口路径是{}", uri);
        if (!permissions.contains(Constant.ALL_PERMISSION) && !permissions.contains(StringUtils.trim(uri).replace(contextPath,""))) {
            throw new ServiceException(LocaleUtil.getLocale("interface.permission.set"));
        }
    }

}
