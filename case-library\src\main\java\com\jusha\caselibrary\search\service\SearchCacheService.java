package com.jusha.caselibrary.search.service;

import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import com.jusha.caselibrary.search.dto.AdvancedSearchResponse;

/**
 * @ClassName SearchCacheService
 * @Description 搜索缓存服务接口
 * <AUTHOR>
 * @Date 2025/7/9 17:05
 **/
public interface SearchCacheService {

    /**
     * 获取缓存的搜索结果
     *
     * @param request 搜索请求
     * @return 缓存的搜索结果，如果不存在则返回null
     */
    AdvancedSearchResponse<?> getCachedResult(AdvancedSearchRequest request);

    /**
     * 缓存搜索结果
     *
     * @param request 搜索请求
     * @param response 搜索响应
     */
    void cacheResult(AdvancedSearchRequest request, AdvancedSearchResponse<?> response);

    /**
     * 清除指定的缓存
     *
     * @param cacheKey 缓存键
     */
    void evictCache(String cacheKey);

    /**
     * 清除所有搜索缓存
     */
    void evictAllCache();

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    CacheStats getCacheStats();

    /**
     * 记录查询统计信息
     *
     * @param request 搜索请求
     * @param took 查询耗时
     * @param total 结果总数
     */
    void recordQueryStats(AdvancedSearchRequest request, long took, Long total);

    /**
     * 缓存统计信息
     */
    class CacheStats {
        private long hitCount;
        private long missCount;
        private long evictionCount;
        private double hitRate;
        private long cacheSize;

        public CacheStats(long hitCount, long missCount, long evictionCount, double hitRate, long cacheSize) {
            this.hitCount = hitCount;
            this.missCount = missCount;
            this.evictionCount = evictionCount;
            this.hitRate = hitRate;
            this.cacheSize = cacheSize;
        }

        // Getters
        public long getHitCount() { return hitCount; }
        public long getMissCount() { return missCount; }
        public long getEvictionCount() { return evictionCount; }
        public double getHitRate() { return hitRate; }
        public long getCacheSize() { return cacheSize; }
    }
}