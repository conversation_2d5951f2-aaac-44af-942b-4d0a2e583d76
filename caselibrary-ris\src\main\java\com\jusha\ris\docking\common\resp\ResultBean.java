package com.jusha.ris.docking.common.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jusha.ris.docking.common.exception.BusinessCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 后端接口统一返回
 * @param <T>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResultBean<T> {

    @ApiModelProperty(value = "请求状态", position = 1)
    @JsonProperty(index = 1)
    @JSONField(ordinal = 1)
    private Boolean state;

    @ApiModelProperty(value = "错误码", position = 2)
    @JsonProperty(index = 2)
    @JSONField(ordinal = 2)
    private int errorCode;

    @ApiModelProperty(value = "错误信息", position = 3)
    @JsonProperty(index = 3)
    @JSONField(ordinal = 3)
    private String message;

    @ApiModelProperty(value = "返回数据", position = 4)
    @JsonProperty(index = 4)
    @JSONField(ordinal = 4)
    private T data;


    /**
     * 成功返回(无返回数据)
     */
    public static <T> ResultBean<T> success(){
        return success(null);
    }

    /**
     * 成功返回(有返回数据)
     */
    public static <T> ResultBean<T> success(T data){
        return new ResultBean<>(true, BusinessCode.SUCCESS, "success", data);
    }


    /**
     * 失败返回(通用错误码)
     */
    public static <T> ResultBean<T> error(String messages){
        return error(BusinessCode.COMMON_ERROR, messages);
    }

    /**
     * 失败返回(其它错误码)
     */
    public static <T> ResultBean<T> error(Integer code, String messages){
        return new ResultBean<>(false, code, messages, null);
    }

}