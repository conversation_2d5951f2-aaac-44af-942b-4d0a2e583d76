package com.jusha.caselibrary.common.exception;

/**
 * 错误码定义
 *
 * <AUTHOR>
 */
public class ErrorCodeDefine {

    /**
     * 全局错误
     */
    public final static int COMMON__ERROR = -0x800000;

    /**
     * 数据库插入操作失败
     */
    public final static int COMMON_ERROR_DATA_INSERT_FAIL = COMMON__ERROR - 3;

    /**
     * 数据库保存（先插入新信息，后删除旧重复信息）操作失败
     */
    public final static int COMMON_ERROR_DATA_SAVE_FAIL = COMMON__ERROR - 4;

    /**
     * 数据库删除操作失败
     */
    public final static int COMMON_ERROR_DATA_DELETE_FAIL = COMMON__ERROR - 5;

    /**
     * 数据库更新操作失败
     */
    public final static int COMMON_ERROR_DATA_UPDATE_FAIL = COMMON__ERROR - 6;

}
