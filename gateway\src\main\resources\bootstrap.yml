##需要修改的配置
SVC_IP: *************
#nacos
NACOS_PORT: 8848
NACOS_NAMESPACE: pacs-test   #命名空间ID,为空表示取默认命名空间(public)
#服务名和端口
HTTP_PORT: 9020
HTTPS_PORT: 9030
SERVER_NAME: gateway


server:
  port: ${HTTPS_PORT}
  ssl:
    enabled: true
    key-store: classpath:https/web.jks
    key-alias: webjks
    key-store-password: jusha1996
    key-store-type: JKS

spring:
  application:
      name: ${SERVER_NAME}
  cloud:
      nacos:
         config:
           server-addr: ${SVC_IP}:${NACOS_PORT}
           namespace:  ${NACOS_NAMESPACE}
           group: DEFAULT_GROUP
           name: ${SERVER_NAME}
           file-extension: yml
         discovery:
           server-addr: ${SVC_IP}:${NACOS_PORT}
           namespace:  ${NACOS_NAMESPACE}