package com.jusha.auth.system.service;

import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.TreeSelect;
import com.jusha.auth.mybatisplus.entity.SysGroup;

import java.util.List;

/**
 * 分组管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysGroupService {

    /**
     * 查询分组管理数据
     *
     * @param group 分组信息
     * @return 分组信息集合
     */
    public List<SysGroup> selectGroupList(SysGroup group);

    /**
     * 某个平台下的分组列表
     * @return
     */
    List<SysGroup> groupListInPlat(Long platId);

    /**
     * 新增保存分组信息
     *
     * @param group 分组信息
     * @return 结果
     */
    public SysGroup insertGroup(SysGroup group);

    /**
     * 查询分组树结构信息
     *
     * @param group 分组信息
     * @return 分组树信息集合
     */
    public List<TreeSelect> selectGroupTreeList(SysGroup group);

    /**
     * 构建前端所需要树结构
     *
     * @param groups 分组列表
     * @return 树结构列表
     */
    public List<SysGroup> buildGroupTree(List<SysGroup> groups);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param groups 分组列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildGroupTreeSelect(List<SysGroup> groups);

    /**
     * 根据角色ID查询分组树信息
     *
     * @param roleId 角色ID
     * @return 选中分组列表
     */
    public List<Long> selectGroupListByRoleId(Long roleId,Long platId);

    /**
     * 根据分组ID查询信息
     *
     * @param groupId 分组ID
     * @return 分组信息
     */
    public SysGroup selectGroupById(Long groupId);

    /**
     * 根据ID查询所有子分组（正常状态）
     *
     * @param groupId 分组ID
     * @return 子分组数
     */
    public int selectNormalChildrenGroupById(Long groupId);

    /**
     * 是否存在分组子节点
     *
     * @param groupId 分组ID
     * @return 结果
     */
    public boolean hasChildByGroupId(Long groupId);

    /**
     * 校验分组名称是否唯一
     *
     * @param group 分组信息
     * @return 结果
     */
    public boolean checkGroupNameUnique(SysGroup group);

    /**
     * 校验中心分组是否唯一
     *
     * @param group 分组信息
     * @return 结果
     */
    public boolean checkGroupCenterUnique(SysGroup group);


    /**
     * 校验分组是否有数据权限
     *
     * @param groupId 分组id
     */
    public void checkGroupDataScope(Long groupId,String platId);

    /**
     * 修改保存分组信息
     *
     * @param group 分组信息
     * @return 结果
     */
    public ResultBean updateGroup(SysGroup group);

    /**
     * 删除分组管理信息
     *
     * @param groupId 分组ID
     * @return 结果
     */
    public boolean deleteGroupById(Long groupId);

    /**
     * 新建根目录
     *
     * @param group 分组信息
     * @return 结果
     */
    public boolean insertRootGroup(SysGroup group);

    /**
     * 根据platId查询菜单
     *
     * @param platId
     * @return
     */
    public boolean hasChildByPlatId(Long platId);

    /**
     * 删除分组管理信息
     *
     * @param platId 所属平台ID
     * @return 结果
     */
    public boolean deleteGroupByPlatId(Long platId);

    /**
     * 教学平台互联网版定制接口——获取分组id列表
     * @param platId
     * @return
     */
    public ResultBean getGroupIdList(String platId);

    /**
     * 教学平台互联网版定制接口——获取联盟id
     * @param platId
     * @return
     */
    public ResultBean getAllianceMsg(String platId);

    /**
     * 教学平台互联网版定制接口——获取联盟id和名称列表
     * @return
     */
    public ResultBean getAllianceList(String platId);

}
