package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.Import;
import com.jusha.caselibrary.mybatisplus.mapper.ImportMapper;
import com.jusha.caselibrary.mybatisplus.service.ImportService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 病例导入任务表 服务实现类
 * <AUTHOR>
 */
@Service
public class ImportServiceImpl extends ServiceImpl<ImportMapper, Import> implements ImportService {

}
