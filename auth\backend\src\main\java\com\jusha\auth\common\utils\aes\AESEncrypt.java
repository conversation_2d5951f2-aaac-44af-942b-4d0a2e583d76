package com.jusha.auth.common.utils.aes;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.SecureRandom;

/**
 * AES加密
 */
public class AESEncrypt {
    public static byte[] generateMD5Hash(String key) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        return md.digest(key.getBytes("UTF-8"));
    }

    public static byte[] AESEncryptBytes(byte[] content, String key) throws Exception {
        byte[] keyBytes = generateMD5Hash(key);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        byte[] nonce = new byte[12]; // GCM Nonce size is 12 bytes
        SecureRandom random = new SecureRandom();
        random.nextBytes(nonce);
        GCMParameterSpec gcmSpec = new GCMParameterSpec(128, nonce);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmSpec);
        byte[] cipherText = cipher.doFinal(content);
        byte[] encryptedContent = new byte[nonce.length + cipherText.length];
        System.arraycopy(nonce, 0, encryptedContent, 0, nonce.length);
        System.arraycopy(cipherText, 0, encryptedContent, nonce.length, cipherText.length);
        return encryptedContent;
    }
}