package com.jusha.caselibrary.mybatisplus.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 个人目录表
 * <AUTHOR>
 */
@TableName("t_user_catalog")
@Data
public class UserCatalog {

    @ApiModelProperty(value = "目录id")
    @TableId(value = "catalog_id")
    private Long catalogId;    

    @ApiModelProperty(value = "目录名称")
    @TableField("catalog_name")
    private String catalogName;    

    @ApiModelProperty(value = "父级id")
    @TableField("parent_id")
    private Long parentId;    

    @ApiModelProperty(value = "祖级列表")
    @TableField("ancestors")
    private String ancestors;    

    @ApiModelProperty(value = "排序")
    @TableField("order_num")
    private Integer orderNum;    

    @ApiModelProperty(value = "删除标志0正常1删除")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;    

    @ApiModelProperty(value = "创建人")
    @TableField("created_by")
    private Long createdBy;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private Long updateBy;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @TableField(exist = false)
    private List<UserCatalog> children = new ArrayList<>();
}
