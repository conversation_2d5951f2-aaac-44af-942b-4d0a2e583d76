package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例导出任务表
 * <AUTHOR>
 */
@TableName("t_export")
@Data
public class Export {

    @ApiModelProperty(value = "病例导出任务ID")
    @TableId("export_id")
    private Long exportId;    

    @ApiModelProperty(value = "导出任务名称")
    @TableField("export_name")
    private String exportName;    

    @ApiModelProperty(value = "状态：0-未开始 1-进行中 2-成功 3-失败")
    @TableField("status")
    private String status;    

    @ApiModelProperty(value = "完成百分比")
    @TableField("percent")
    private Integer percent;    

    @ApiModelProperty(value = "资源文件ID")
    @TableField("resource_id")
    private Long resourceId;    

    @ApiModelProperty(value = "资源文件字节数")
    @TableField("resource_size")
    private Long resourceSize;    

    @ApiModelProperty(value = "导出人")
    @TableField("create_user_id")
    private Long createUserId;    

    @ApiModelProperty(value = "导出时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "联盟分组ID")
    @TableField("lm_gp_id")
    private Long lmGpId;    

}
