package com.jusha.caselibrary.system.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 个人目录表
 * <AUTHOR>
 */
@Data
public class UserCatalogUpdateReq {

    @ApiModelProperty(value = "目录id")
    private Long catalogId;

    @ApiModelProperty(value = "目录名称")
    private String catalogName;

    @ApiModelProperty(value = "父级id")
    private Long parentId;

    @ApiModelProperty(value = "排序")
    private Integer orderNum;
}
