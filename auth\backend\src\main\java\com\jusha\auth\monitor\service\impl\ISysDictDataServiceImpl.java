package com.jusha.auth.monitor.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.DictUtils;
import com.jusha.auth.monitor.service.ISysDictDataService;
import com.jusha.auth.mybatisplus.entity.SysDictData;
import com.jusha.auth.mybatisplus.service.SysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ISysDictDataServiceImpl implements ISysDictDataService {

    @Autowired
    private SysDictDataService sysDictDataService;

    @Autowired
    private TokenService tokenService;

    /**
     * 根据条件分页查询字典数据
     * 
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData) {
        LambdaQueryChainWrapper<SysDictData> wrapper = sysDictDataService.lambdaQuery();
        if(dictData.getDictType() != null){
            wrapper.eq(SysDictData::getDictType, dictData.getDictType());
        }
        if(dictData.getDictLabel() != null){
            wrapper.like(SysDictData::getDictLabel, dictData.getDictLabel());
        }
        if(dictData.getStatus() != null){
            wrapper.eq(SysDictData::getStatus, dictData.getStatus());
        }
        return wrapper.list();
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     * 
     * @param dictType 字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        SysDictData sysDictData = sysDictDataService.lambdaQuery().eq(SysDictData::getDictType,dictType).eq(SysDictData::getDictValue,dictValue).one();
        return sysDictData.getDictLabel();
    }

    /**
     * 根据字典数据ID查询信息
     * 
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        return sysDictDataService.getById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     * 
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        for (Long dictCode : dictCodes) {
            SysDictData data = selectDictDataById(dictCode);
            sysDictDataService.removeById(dictCode);
            List<SysDictData> dictDatas = sysDictDataService.lambdaQuery()
                    .eq(SysDictData::getDictType,data.getDictType())
                    .eq(SysDictData::getStatus, Constants.NORMAL)
                    .list();
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
    }

    /**
     * 新增保存字典数据信息
     * 
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public boolean insertDictData(SysDictData data) {
        data.setCreateTime(DateUtils.getNowDate());
        data.setCreateBy(tokenService.getUserId());
        boolean flag = sysDictDataService.save(data);
        if (flag) {
            List<SysDictData> dictDatas = sysDictDataService.lambdaQuery()
                    .eq(SysDictData::getDictType,data.getDictType())
                    .eq(SysDictData::getStatus,Constants.NORMAL)
                    .list();
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return flag;
    }

    /**
     * 修改保存字典数据信息
     * 
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public boolean updateDictData(SysDictData data) {
        data.setUpdateBy(tokenService.getUserId());
        data.setUpdateTime(DateUtils.getNowDate());
        boolean flag = sysDictDataService.updateById(data);
        if (flag) {
            List<SysDictData> dictDatas = sysDictDataService.lambdaQuery()
                    .eq(SysDictData::getDictType,data.getDictType())
                    .eq(SysDictData::getStatus,Constants.NORMAL)
                    .list();
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return flag;
    }
}
