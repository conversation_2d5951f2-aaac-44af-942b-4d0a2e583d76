<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.auth.system.mapper.ISysMenuMapper">

	<resultMap type="SysMenu" id="SysMenuResult">
		<id     property="menuId"         column="menu_id"        />
		<result property="menuName"       column="menu_name"      />
		<result property="parentName"     column="parent_name"    />
		<result property="parentId"       column="parent_id"      />
		<result property="orderNum"       column="order_num"      />
		<result property="path"           column="path"           />
		<result property="component"      column="component"      />
		<result property="keyWord"        column="key_word"       />
		<result property="keyDescribe"    column="key_describe"   />
		<result property="visible"        column="visible"        />
		<result property="status"         column="status"         />
		<result property="icon"           column="icon"           />
		<result property="createBy"       column="create_by"      />
		<result property="createTime"     column="create_time"    />
		<result property="updateTime"     column="update_time"    />
		<result property="updateBy"       column="update_by"      />
	</resultMap>
	
	<select id="selectMenuListByUserId" parameterType="SysMenu" resultMap="SysMenuResult">
		select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.visible, m.status, m.key_word, m.key_describe, m.menu_type, m.icon, m.order_num, m.create_time
		from sys_menu m
		left join sys_role_menu rm on m.menu_id = rm.menu_id
		left join sys_user_role ur on rm.role_id = ur.role_id
		left join sys_role ro on ur.role_id = ro.role_id
		where 1=1
		<if test="params.userId != null">
			AND ur.user_id = #{params.userId}
		</if>
		<if test="menuName != null and menuName != ''">
            AND m.menu_name like concat('%', #{menuName}, '%')
		</if>
		<if test="visible != null and visible != ''">
            AND m.visible = #{visible}
		</if>
		<if test="status != null and status != ''">
            AND m.status = #{status}
		</if>
		<if test="platId != null">
			AND m.plat_id = #{platId}
		</if>
		<if test="parentId != null">
			AND m.parent_id = #{parentId}
		</if>
		<if test="menuType != null">
			AND m.menu_type = #{menuType}
		</if>
		AND m.del_flag = 0
		order by m.parent_id, m.order_num
	</select>
    
    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="SysMenuResult">
		select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.visible, m.status,  m.menu_type, m.icon, m.order_num, m.create_time
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_user_role ur on rm.role_id = ur.role_id
			 left join sys_role ro on ur.role_id = ro.role_id
			 left join sys_user u on ur.user_id = u.user_id
		where u.user_id = #{userId} and m.menu_type in ('M', 'C') and m.status = 0  AND ro.status = 0
		<if test="platId != null">
			AND m.plat_id = #{platId}
		</if>
		order by m.parent_id, m.order_num
	</select>
	
	<select id="selectMenuListByRoleId" resultType="Long">
		select m.menu_id
		from sys_menu m
            left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
		and m.menu_id not in (select m.parent_id from sys_menu m inner join sys_role_menu rm on m.menu_id = rm.menu_id and rm.role_id = #{roleId})
		order by m.parent_id, m.order_num
	</select>

	<select id="selectIterfacePathsByUserId" parameterType="Long" resultType="String">
		select distinct si.interface_path
			from sys_interface si
			left join sys_menu_interface smi on smi.interface_id = si.interface_id
			left join sys_menu sm on smi.menu_id = sm.menu_id
			left join sys_role_menu rm on sm.menu_id = rm.menu_id
			left join sys_user_role ur on rm.role_id = ur.role_id
			left join sys_role r on r.role_id = ur.role_id
			where ur.user_id = #{userId}
		    and sm.status = '0' and r.status = '0'
		<if test="platId != null">
			AND sm.plat_id = #{platId}
		</if>
	</select>

	<select id="selectMenuAndParentIdListByRoleId" resultType="java.lang.Long">
		select m.menu_id
		from sys_menu m
            left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
		order by m.parent_id, m.order_num
	</select>

	<select id="selectChildIdListByMenuId" resultType="java.lang.Long">
		WITH RECURSIVE menu_tree AS (
			-- 直接查询当前节点的子节点作为起始点
			SELECT menu_id, parent_id
			FROM sys_menu
			WHERE parent_id = #{menuId} AND del_flag = 0

			UNION ALL

			-- 递归查询子节点的子节点
			SELECT m.menu_id, m.parent_id
			FROM sys_menu m
					 INNER JOIN menu_tree mt ON m.parent_id = mt.menu_id
			WHERE m.del_flag = 0
		)
		SELECT menu_id FROM menu_tree
		ORDER BY menu_id
    </select>
</mapper> 