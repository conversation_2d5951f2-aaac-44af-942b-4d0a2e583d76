package com.jusha.ris.docking.common.filter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 过滤器配置
 */
@Slf4j
@RequiredArgsConstructor
@Configuration
public class WebFilterConfig {

    private final WebFilter webFilter;


    @Bean
    public FilterRegistrationBean registFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(webFilter);
        registration.addUrlPatterns("/*");
        registration.setName("webFilter");
        registration.setOrder(1);
        return registration;
    }

}