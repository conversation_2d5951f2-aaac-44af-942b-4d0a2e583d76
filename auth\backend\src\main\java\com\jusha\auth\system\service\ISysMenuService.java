package com.jusha.auth.system.service;

import com.jusha.auth.common.core.domain.TreeSelect;
import com.jusha.auth.mybatisplus.entity.SysMenu;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.system.domain.RouterVo;

import java.util.List;
import java.util.Set;

/**
 * 菜单 业务层
 *
 * <AUTHOR>
 */
public interface ISysMenuService {
    /**
     * 根据用户查询系统菜单列表
     *
     * @return 菜单列表
     */
    public List<SysMenu> selectAllMenuList(Long roleId);

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean insertMenu(SysMenu menu);

    /**
     * 根据用户查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuList(SysMenu menu);

    /**
     * 根据用户ID查询菜单树信息
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeByUserId(Long userId, Long platId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    public List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    public List<RouterVo> buildMenus(List<SysMenu> menus, long userId);

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    public List<SysMenu> buildMenuTree(List<SysMenu> menus);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    public SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean hasChildByMenuId(Long menuId);

    /**
     * 查询菜单是否存在角色
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkMenuExistRole(Long menuId);

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean updateMenu(SysMenu menu);

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    public boolean deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean checkMenuNameUnique(SysMenu menu);

    /**
     * 校验菜单的父级是否为菜单
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean checkMenuParent(SysMenu menu);

    /**
     * 根据platId查询菜单
     *
     * @param platId 平台id
     * @return b
     */
    public boolean hasChildByPlatId(Long platId);

    /**
     * 根据userId查询接口路径
     *
     * @param user   用户
     * @param platId 平台id
     * @return 集合
     */
    public Set<String> selectIterfacePathsByUserId(SysUser user, Long platId);


    /**
     * @description 根据角色id查询菜单id和父级id
     * <AUTHOR>
     * @date 2025/3/7 16:28
     * @param roleId
     * @return List<Long>
     **/
    List<Long> selectMenuListAndParentIdByRoleId(Long roleId);

    /**
     * @description 根据菜单名称获取菜单列表
     * <AUTHOR>
     * @date 2025/7/4 14:29
     * @param menuName
     * @param platId
     * @return List<SysMenu>
     **/
    List<SysMenu> getMenuListByName(String menuName, Long platId);

    /**
     * @description 根据菜单id查询子孙id
     * <AUTHOR>
     * @date 2025/7/4 15:06
     * @param menuId
     * @return List<Long>
     **/
    List<Long> selectChildIdListByMenuId(Long menuId);

    /**
     * @description 根据菜单id列表删除菜单
     * <AUTHOR>
     * @date 2025/7/4 15:08
     * @param menuIds
     **/
    void deleteMenuByIds(List<Long> menuIds);

    /**
     * @description 根据菜单id列表删除角色菜单关联
     * <AUTHOR>
     * @date 2025/7/4 15:08
     * @param menuIds
     **/
    void deleteRoleMenuByMenuIds(List<Long> menuIds);

    SysMenu addMenu(SysMenu menu);
}
