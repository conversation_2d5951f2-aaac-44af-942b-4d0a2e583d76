package com.jusha.caselibrary.mybatisplus.service.impl;

import com.jusha.caselibrary.mybatisplus.entity.ImportError;
import com.jusha.caselibrary.mybatisplus.mapper.ImportErrorMapper;
import com.jusha.caselibrary.mybatisplus.service.ImportErrorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 病例导入失败记录表 服务实现类
 * <AUTHOR>
 */
@Service
public class ImportErrorServiceImpl extends ServiceImpl<ImportErrorMapper, ImportError> implements ImportErrorService {

}
