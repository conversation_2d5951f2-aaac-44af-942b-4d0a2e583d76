package com.jusha.auth.monitor.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.*;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.monitor.service.ISysDictTypeService;
import com.jusha.auth.mybatisplus.entity.SysDictData;
import com.jusha.auth.mybatisplus.entity.SysDictType;
import com.jusha.auth.mybatisplus.service.SysDictDataService;
import com.jusha.auth.mybatisplus.service.SysDictTypeService;
import java.util.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ISysDictTypeServiceImpl implements ISysDictTypeService {

    @Autowired
    private SysDictTypeService sysDictTypeService;

    @Autowired
    private SysDictDataService sysDictDataService;

    @Autowired
    private TokenService tokenService;

    /**
     * 项目启动时，初始化字典到缓存
     */
    @PostConstruct
    public void init() {
        loadingDictCache();
    }

    /**
     * 根据条件分页查询字典类型
     * 
     * @param dictType 字典类型信息
     * @return 字典类型集合信息
     */
    @Override
    public List<SysDictType> selectDictTypeList(SysDictType dictType) {
        LambdaQueryChainWrapper<SysDictType> wrapper = sysDictTypeService.lambdaQuery();
        if(dictType.getDictName() != null){
            wrapper.like(SysDictType::getDictName, dictType.getDictName());
        }
        if(dictType.getStatus() != null){
            wrapper.eq(SysDictType::getStatus, dictType.getStatus());
        }
        if(dictType.getDictType() != null){
            wrapper.like(SysDictType::getDictType, dictType.getDictType());
        }
        return wrapper.list();
    }

    /**
     * 根据所有字典类型
     * 
     * @return 字典类型集合信息
     */
    @Override
    public List<SysDictType> selectDictTypeAll() {
        return sysDictTypeService.lambdaQuery().list();
    }

    /**
     * 根据字典类型查询字典数据
     * 
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataByType(String dictType) {
        List<SysDictData> dictDatas =  sysDictDataService.lambdaQuery()
                .eq(SysDictData::getDictType,dictType)
                .eq(SysDictData::getStatus,Constants.NORMAL)
                .list();
        if (StringUtils.isNotEmpty(dictDatas)) {
            return dictDatas;
        }
        return Collections.emptyList();
    }

    /**
     * 根据字典类型ID查询信息
     * 
     * @param dictId 字典类型ID
     * @return 字典类型
     */
    @Override
    public SysDictType selectDictTypeById(Long dictId) {
        return sysDictTypeService.getById(dictId);
    }

    /**
     * 根据字典类型查询信息
     * 
     * @param dictType 字典类型
     * @return 字典类型
     */
    @Override
    public SysDictType selectDictTypeByType(String dictType) {
        return sysDictTypeService.lambdaQuery().eq(SysDictType::getDictType,dictType).one();
    }

    /**
     * 批量删除字典类型信息
     * 
     * @param dictIds 需要删除的字典ID
     */
    @Override
    public void deleteDictTypeByIds(Long[] dictIds) {
        for (Long dictId : dictIds) {
            SysDictType dictType = selectDictTypeById(dictId);
            List<SysDictData> sysDictDataList = sysDictDataService.lambdaQuery().eq(SysDictData::getDictType,dictType.getDictType()).list();
            if (!sysDictDataList.isEmpty()) {
                throw new ServiceException(MessageUtils.message("parameter.allot.delete"));
            }
            sysDictTypeService.removeById(dictId);
            DictUtils.removeDictCache(dictType.getDictType());
        }
    }

    /**
     * 加载字典缓存数据
     */
    @Override
    public void loadingDictCache() {
        Map<String, List<SysDictData>> dictDataMap = sysDictDataService.lambdaQuery().eq(SysDictData::getStatus,Constants.NORMAL).list()
                .stream().collect(Collectors.groupingBy(SysDictData::getDictType));
        for (Map.Entry<String, List<SysDictData>> entry : dictDataMap.entrySet()) {
            DictUtils.setDictCache(entry.getKey(), entry.getValue().stream().sorted(Comparator.comparing(SysDictData::getDictSort)).collect(Collectors.toList()));
        }
    }

    /**
     * 清空字典缓存数据
     */
    @Override
    public void clearDictCache() {
        DictUtils.clearDictCache();
    }

    /**
     * 重置字典缓存数据
     */
    @Override
    public void resetDictCache() {
        clearDictCache();
        loadingDictCache();
    }

    /**
     * 新增保存字典类型信息
     * 
     * @param dict 字典类型信息
     * @return 结果
     */
    @Override
    public boolean insertDictType(SysDictType dict) {
        dict.setCreateTime(DateUtils.getNowDate());
        dict.setCreateBy(tokenService.getUserId());
        boolean flag = sysDictTypeService.save(dict);
        if (flag) {
            DictUtils.setDictCache(dict.getDictType(), null);
        }
        return flag;
    }

    /**
     * 修改保存字典类型信息
     * 
     * @param dict 字典类型信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateDictType(SysDictType dict) {
        SysDictType oldDict = sysDictTypeService.getById(dict.getDictId());
        boolean flag = sysDictTypeService.lambdaUpdate()
                .set(SysDictType::getDictType,dict.getDictType())
                .set(SysDictType::getUpdateBy,tokenService.getUserId())
                .set(SysDictType::getUpdateTime,DateUtils.getNowDate())
                .set(SysDictType::getRemark,dict.getRemark())
                .set(SysDictType::getDictName,dict.getDictName())
                .set(SysDictType::getStatus,dict.getStatus())
                .eq(SysDictType::getDictType,oldDict.getDictType())
                .update();
        if (flag) {
            List<SysDictData> dictDatas = sysDictDataService.lambdaQuery()
                    .eq(SysDictData::getDictType,dict.getDictType())
                    .eq(SysDictData::getStatus,Constants.NORMAL)
                    .list();
            DictUtils.setDictCache(dict.getDictType(), dictDatas);
        }
        return flag;
    }

    /**
     * 校验字典类型称是否唯一
     * 
     * @param dict 字典类型
     * @return 结果
     */
    @Override
    public boolean checkDictTypeUnique(SysDictType dict) {
        Long dictId = StringUtils.isNull(dict.getDictId()) ? Constants.ALL_MINUS1L : dict.getDictId();
        List<SysDictType> dictTypeList = sysDictTypeService.lambdaQuery()
                .eq(SysDictType::getDictType, dict.getDictType()).list();
        if (!dictTypeList.isEmpty() && dictTypeList.get(0).getDictId().longValue() != dictId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }
}
