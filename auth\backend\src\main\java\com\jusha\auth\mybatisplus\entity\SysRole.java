package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@Getter
@Setter
@TableName("sys_role")
@ApiModel(value = "SysRole对象", description = "角色信息表")
public class SysRole {

    @JsonIgnore
    public boolean isAdmin() {
        return isAdmin(this.roleId);
    }

    @JsonIgnore
    public static boolean isAdmin(Long roleId) {
        return roleId != null && 1L == roleId;
    }

    public SysRole() {
    }

    public SysRole(Long platId,String status) {
        this.platId = platId;
        this.status = status;
    }

    public SysRole(Long roleId) {
        this.roleId = roleId;
    }

    @ApiModelProperty(value = "角色ID")
    @TableId(value = "role_id")
    private Long roleId;

    @ApiModelProperty(value = "所属平台id")
    @TableField("plat_id")
    private Long platId;

    @ApiModelProperty(value = "角色名称")
    @TableField("role_name")
    @Size(message = "角色名称长度不合法",max = 100)
    private String roleName;

    @ApiModelProperty(value = "角色状态（0正常 1停用）")
    @TableField("status")
    private String status;

    @ApiModelProperty(value = "角色类型（1管理员2教师3学生）")
    @TableField("role_type")
    private String roleType;

    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;

    @ApiModelProperty(value = "创建者")
    @TableField("create_by")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField("update_by")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    /** 菜单组 */
    @TableField(exist = false)
    private Long[] menuIds;

    /** 分组组（数据权限） */
    @TableField(exist = false)
    private Long[] groupIds;

    /** 分组组（数据权限） */
    @TableField(exist = false)
    private List<Long> groupList;

    /** 联盟id */
    @TableField(exist = false)
    private Long allianceId;

    /** 医院id */
    @TableField(exist = false)
    private Long sysGroupId;

    /** 角色菜单权限 */
    @TableField(exist = false)
    private Set<String> permissions;

    /** 用户是否存在此角色标识 默认不存在 */
    @TableField(exist = false)
    private boolean flag = false;
}
