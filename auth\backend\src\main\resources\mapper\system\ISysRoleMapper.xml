<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.auth.system.mapper.ISysRoleMapper">

	<resultMap type="SysRole" id="SysRoleResult">
		<id     property="roleId"             column="role_id"               />
		<result property="roleName"           column="role_name"             />
		<result property="status"             column="status"                />
		<result property="delFlag"            column="del_flag"              />
		<result property="createBy"           column="create_by"             />
		<result property="createTime"         column="create_time"           />
		<result property="updateBy"           column="update_by"             />
		<result property="updateTime"         column="update_time"           />
	</resultMap>
	
	<sql id="selectRoleVo">
	    select distinct r.role_id, r.role_name,
            r.status, r.del_flag, r.create_time
        from sys_role r
	        left join sys_user_role ur on ur.role_id = r.role_id
	        left join sys_user u on u.user_id = ur.user_id
    </sql>

	<select id="selectRolePermissionByUser" parameterType="Long" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
		WHERE r.del_flag = '0' and ur.user_id = #{userId}
		<if test="platId != null">
			and r.plat_id = #{platId}
		</if>
	</select>
	
	<select id="selectRoleListByUserId" parameterType="Long" resultType="Long">
		select r.role_id
        from sys_role r
	        left join sys_user_role ur on ur.role_id = r.role_id
	        left join sys_user u on u.user_id = ur.user_id
	    where u.user_id = #{userId}
	</select>
	
	<select id="selectRolesByUserName" parameterType="String" resultMap="SysRoleResult">
		<include refid="selectRoleVo"/>
		WHERE r.del_flag = '0' and u.user_name = #{userName}
	</select>

	<select id="selectRoleByUser" resultType="com.jusha.auth.mybatisplus.entity.SysRole">
        select distinct r.role_id as roleId, r.role_name as roleName, r.status, r.role_type as roleType,
        r.del_flag as delFlag, r.create_time as createTime, r.create_by as createBy, r.update_time as updateTime,
        r.update_by as updateBy, r.plat_id as platId
        from sys_role r
        left join sys_user_role ur on ur.role_id = r.role_id
        left join sys_user u on u.user_id = ur.user_id
        WHERE r.del_flag = '0' and ur.user_id = #{userId}
        <if test="platId != null">
            and r.plat_id = #{platId}
        </if>
    </select>

	<delete id="removeUserPlatRole">
		delete t1
		from sys_user_role t1 inner join sys_role t2 on t1.role_id = t2.role_id
		where t2.plat_id = #{platId}
		and t1.user_id in <foreach collection="userIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
	</delete>

</mapper>