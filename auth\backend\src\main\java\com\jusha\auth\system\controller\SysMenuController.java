package com.jusha.auth.system.controller;

import com.jusha.auth.common.annotation.EscapeWildcard;
import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.mybatisplus.entity.SysMenu;
import com.jusha.auth.system.domain.RouterVo;
import com.jusha.auth.system.service.ISysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 菜单信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/menu")
public class SysMenuController extends BaseController {
    @Autowired
    private ISysMenuService isysMenuService;

    @Autowired
    private RedisCache redisCache;

    @Value("${token.expireTime}")
    private int expireTime;


    /**
     * 获取菜单列表
     */
    @HasPermissions
    @GetMapping("/list")
    @EscapeWildcard
    public ResultBean list(SysMenu menu) {
        List<SysMenu> menus = isysMenuService.selectMenuList(menu);
        return success(menus);
    }

    /**
     * 新增菜单
     */
    @HasPermissions
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysMenu menu) {
        if (!isysMenuService.checkMenuNameUnique(menu)) {
            return error(MessageUtils.message("menu.already.exist.add"));
        }
        //如果一个菜单加到菜单下了，提示不可以，因为目录的下级才能是菜单
        if (!isysMenuService.checkMenuParent(menu)) {
            return error(MessageUtils.message("menu.parent.not.menu"));
        }
        return resultBean(isysMenuService.insertMenu(menu));
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long menuId) {
        return success(isysMenuService.selectMenuById(menuId));
    }

    /**
     * 删除菜单
     */
    @HasPermissions
    @PostMapping("/remove")
    public ResultBean remove(@RequestParam Long menuId) {
        if (isysMenuService.hasChildByMenuId(menuId)) {
            return error(MessageUtils.message("menu.child.delete"));
        }
        if (isysMenuService.checkMenuExistRole(menuId)) {
            return error(MessageUtils.message("menu.role.delete"));
        }
        return resultBean(isysMenuService.deleteMenuById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeselect")
    public ResultBean treeselect(SysMenu menu) {
        List<SysMenu> menus = isysMenuService.selectMenuList(menu);
        return success(isysMenuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping(value = "/roleMenuTreeselect/query")
    public ResultBean roleMenuTreeselect(@RequestParam Long roleId) {
        List<SysMenu> menus = isysMenuService.selectAllMenuList(roleId);
        ResultBean resultBean = ResultBean.success();
        resultBean.put("checkedKeys", isysMenuService.selectMenuListByRoleId(roleId));
        resultBean.put("menus", isysMenuService.buildMenuTreeSelect(menus));
        return resultBean;
    }

    /**
     * 修改菜单
     */
    @HasPermissions
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysMenu menu) {
        if (!isysMenuService.checkMenuNameUnique(menu)) {
            return error(MessageUtils.message("menu.already.exist.edit"));
        }
        else if (menu.getMenuId().equals(menu.getParentId())) {
            return error(MessageUtils.message("menu.parent.myself.edit"));
        }
        return resultBean(isysMenuService.updateMenu(menu));
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public ResultBean getRouters() {
        long userId = getLoginUser().getUserId();
        List<RouterVo> routerVos;
        if(redisCache.getCacheObject(Constants.MENU_ROUTER + userId + ":" + "0") == null){
            List<SysMenu> menus = isysMenuService.selectMenuTreeByUserId(userId, 0L);
            routerVos = isysMenuService.buildMenus(menus,userId);
            redisCache.setCacheObject(Constants.MENU_ROUTER + userId + ":" + "0", routerVos , expireTime, TimeUnit.MINUTES);
        }else {
            routerVos = redisCache.getCacheObject(Constants.MENU_ROUTER + userId + ":" + "0");
            redisCache.setCacheObject(Constants.MENU_ROUTER + userId + ":" + "0", routerVos , expireTime, TimeUnit.MINUTES);
        }
        return ResultBean.success(routerVos);
    }
}