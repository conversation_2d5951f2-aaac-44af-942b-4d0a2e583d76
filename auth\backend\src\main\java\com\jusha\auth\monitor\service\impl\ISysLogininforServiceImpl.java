package com.jusha.auth.monitor.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.monitor.domain.LoginTimeStatistics;
import com.jusha.auth.monitor.service.ISysLogininforService;
import com.jusha.auth.mybatisplus.entity.SysLogininfor;
import com.jusha.auth.mybatisplus.mapper.SysLogininforMapper;
import com.jusha.auth.mybatisplus.service.SysLogininforService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * 系统访问日志情况信息 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class ISysLogininforServiceImpl implements ISysLogininforService {

    @Autowired
    private SysLogininforService sysLogininforService;

    @Autowired
    private SysLogininforMapper sysLogininforMapper;

    /**
     * 新增系统登录日志
     * @param logininfor 访问日志对象
     */
    @Override
    public void insertLogininfor(SysLogininfor logininfor) {
        sysLogininforService.save(logininfor);
    }

    /**
     * 查询系统登录日志集合
     *
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor) {
        LambdaQueryChainWrapper<SysLogininfor> wrapper = sysLogininforService.lambdaQuery();
        if(logininfor.getIpaddr() != null){
            wrapper.like(SysLogininfor::getIpaddr, logininfor.getIpaddr());
        }
        if(logininfor.getStatus() != null){
            wrapper.eq(SysLogininfor::getStatus, logininfor.getStatus());
        }
        if(logininfor.getUserName() != null){
            wrapper.like(SysLogininfor::getUserName, logininfor.getUserName());
        }
        if(logininfor.getParams().get(Constants.BEGIN_TIME) != null){
            wrapper.ge(SysLogininfor::getLoginTime, logininfor.getParams().get(Constants.BEGIN_TIME));
        }
        if(logininfor.getParams().get(Constants.END_TIME) != null){
            wrapper.le(SysLogininfor::getLoginTime, logininfor.getParams().get(Constants.END_TIME));
        }
        return wrapper.list();
    }

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    public boolean deleteLogininforByIds(Long[] infoIds) {
        return sysLogininforService.lambdaUpdate().eq(SysLogininfor::getInfoId,infoIds).remove();
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor() {
        sysLogininforMapper.clearAll();
    }

    /**
     * 统计近7日登录最多的用户的登录次数和最近一次登录时间
     */
    @Override
    public HashMap loginStatistics() {
        LambdaQueryChainWrapper<SysLogininfor> wrapper1 = sysLogininforService.lambdaQuery();
        LambdaQueryChainWrapper<SysLogininfor> wrapper2 = sysLogininforService.lambdaQuery();
        LambdaQueryChainWrapper<SysLogininfor> wrapper3 = sysLogininforService.lambdaQuery();
        List<SysLogininfor> failSysLogininfor = wrapper1.eq(SysLogininfor::getStatus,"1").orderByDesc(SysLogininfor::getLoginTime).last("LIMIT 10").list();
        HashMap<String, Object> map = new HashMap<>();
        map.put(Constants.FAIL_SYSLOGIN_INFOR, failSysLogininfor);

        map.put(Constants.TODAY_LOGIN_COUNT,
                wrapper2.ge(SysLogininfor::getLoginTime, DateUtils.dateTime0())
                .eq(SysLogininfor::getStatus, Constants.NORMAL).eq(SysLogininfor::getMsg, Constants.LOGIN_SUCCESS_MSG).count());
        map.put(Constants.YSETODAY_LOGIN_COUNT,
                wrapper3.ge(SysLogininfor::getLoginTime, DateUtils.yestodateTimeBefore(1)) //大于昨天
                        .le(SysLogininfor::getLoginTime, DateUtils.dateTime0())
                        .eq(SysLogininfor::getStatus,Constants.NORMAL)
                        .count());

        Date days7Ago = DateUtils.yestodateTimeBefore(7);
        List<LoginTimeStatistics> loginTimeStatistics = sysLogininforMapper.queryLoginTime(days7Ago,new Date());
        for(LoginTimeStatistics loginUser : loginTimeStatistics){
            loginUser.setLoginTime(sysLogininforService.lambdaQuery()
                    .eq(SysLogininfor::getUserName,loginUser.getUserName())
                    .orderByDesc(SysLogininfor::getLoginTime)
                    .last(Constants.MYSQL_LIMIT_1).one().getLoginTime());
        }
        // 使用 Comparator 接口按照 count 排序
        Collections.sort(loginTimeStatistics, new Comparator<LoginTimeStatistics>() {
            @Override
            public int compare(LoginTimeStatistics o1, LoginTimeStatistics o2) {
                return Integer.compare(o2.getCount(), o1.getCount());
            }
        });
        map.put(Constants.RECENT7_USER_LIST,loginTimeStatistics);
        return map;
    }
}
