package com.jusha.auth.system.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.domain.TreeSelect;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.*;
import com.jusha.auth.mybatisplus.service.SysMenuInterfaceService;
import com.jusha.auth.mybatisplus.service.SysMenuService;
import com.jusha.auth.mybatisplus.service.SysRoleMenuService;
import com.jusha.auth.mybatisplus.service.SysRoleService;
import com.jusha.auth.system.domain.MetaVo;
import com.jusha.auth.system.domain.RouterVo;
import com.jusha.auth.system.mapper.ISysMenuMapper;
import com.jusha.auth.system.service.ISysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ISysMenuServiceImpl implements ISysMenuService {
    @Autowired
    private ISysMenuMapper iSysMenuMapper;

    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @Autowired
    private SysMenuInterfaceService sysMenuInterfaceService;

    @Autowired
    private TokenService tokenService;

    /**
     * 根据用户查询系统菜单列表
     * 
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectAllMenuList(Long roleId) {
        SysRole sysRole = sysRoleService.getById(roleId);
        return iSysMenuMapper.selectMenuListByUserId(new SysMenu(sysRole.getPlatId(),Constants.NORMAL));
    }

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public boolean insertMenu(SysMenu menu) {
        menu.setCreateTime(DateUtils.getNowDate());
        menu.setCreateBy(tokenService.getUserId());
        menu.setMenuId(YitIdHelper.nextId());
        if(menu.getInterfaces()!=null){
            for(Long interfaceId : menu.getInterfaces()){
                sysMenuInterfaceService.save(new SysMenuInterface(menu.getMenuId(),interfaceId));
            }
        }
        return sysMenuService.save(menu);
    }

    /**
     * 查询系统菜单列表
     * 
     * @param menu 菜单信息
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(SysMenu menu) {
        List<SysMenu> menuList = null;
        Long userId = tokenService.getUserId();
        // 管理员显示所有菜单信息
        if (SysUser.isAdmin(userId)) {
            LambdaQueryChainWrapper<SysMenu> wrapper = sysMenuService.lambdaQuery();
            wrapper.eq(SysMenu::getPlatId, menu.getPlatId());
            if(menu.getMenuName() !=null){
                wrapper.like(SysMenu::getMenuName, menu.getMenuName());
            }
            if(menu.getStatus() !=null){
                wrapper.eq(SysMenu::getStatus, menu.getStatus());
            }
            if(menu.getVisible() !=null){
                wrapper.eq(SysMenu::getVisible, menu.getVisible());
            }
            menuList = wrapper.orderByAsc(SysMenu::getOrderNum).orderByDesc(SysMenu::getCreateTime).list();
        } else {
            menu.getParams().put("userId", userId);
            menu.setPlatId(menu.getPlatId());
            menuList = iSysMenuMapper.selectMenuListByUserId(menu);
        }
        return menuList;
    }

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    @Override
    public SysMenu selectMenuById(Long menuId) {
        List<SysMenuInterface> menuInterfaces = sysMenuInterfaceService.lambdaQuery().eq(SysMenuInterface::getMenuId,menuId).list();
        List<Long> interfaces = new ArrayList<>();
        if(!menuInterfaces.isEmpty()){
            for(SysMenuInterface sysMenuInterface : menuInterfaces){
                interfaces.add(sysMenuInterface.getInterfaceId());
            }
        }
        SysMenu menu = sysMenuService.getById(menuId);
        menu.setInterfaces(interfaces.toArray(new Long[interfaces.size()]));
        return menu;
    }

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public boolean updateMenu(SysMenu menu) {
        SysMenu oldMenu = sysMenuService.getById(menu.getMenuId());
        menu.setUpdateBy(tokenService.getUserId());
        menu.setUpdateTime(DateUtils.getNowDate());
        // 后端接口和目录没关系
        if(!menu.getMenuType().equals(Constants.TYPE_DIR)){
            //删除旧的菜单接口关系
            sysMenuInterfaceService.lambdaUpdate().eq(SysMenuInterface::getMenuId,oldMenu.getMenuId()).remove();
            //插入新的菜单接口关系
            for(Long interfaceId : menu.getInterfaces()){
                sysMenuInterfaceService.save(new SysMenuInterface(menu.getMenuId(),interfaceId));
            }
        }
        // 和元素没关系,排序/显示状态/访问路径/路由地址
        if(menu.getMenuType().equals(Constants.TYPE_BUTTON)){
            menu.setOrderNum(oldMenu.getOrderNum());
            menu.setVisible(oldMenu.getVisible());
            menu.setPath(oldMenu.getPath());
            menu.setComponent(oldMenu.getComponent());
        }
        //和菜单没关系,关键字和中文描述
        if(menu.getMenuType().equals(Constants.TYPE_MENU) ){
            menu.setKeyWord(oldMenu.getKeyWord());
            menu.setKeyDescribe(oldMenu.getKeyDescribe());
        }
        //和目录没关系,关键字和中文描述、访问路径
        if(menu.getMenuType().equals(Constants.TYPE_DIR)){
            menu.setComponent(oldMenu.getComponent());
            menu.setKeyWord(oldMenu.getKeyWord());
            menu.setKeyDescribe(oldMenu.getKeyDescribe());
        }
        return sysMenuService.updateById(menu);
    }

    /**
     * 根据用户ID查询菜单
     * 
     * @param userId 账户名
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuTreeByUserId(Long userId,Long platId) {
        List<SysMenu> menus = null;
        if (tokenService.isAdmin(userId)) {
            String[] menuTypes = {"M","C"};
            menus = sysMenuService.lambdaQuery()
                            .in(SysMenu::getMenuType,menuTypes)
                    .eq(SysMenu::getPlatId,platId)
                    .eq(SysMenu::getStatus,Constants.NORMAL)
                    .orderByAsc(SysMenu::getParentId)
                    .orderByAsc(SysMenu::getOrderNum)
                    .list();
        } else {
            menus = iSysMenuMapper.selectMenuTreeByUserId(userId,platId);
        }
        return getChildPerms(menus, 0);
    }

    /**
     * 根据角色ID查询菜单树信息
     * 
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<Long> selectMenuListByRoleId(Long roleId) {
        return iSysMenuMapper.selectMenuListByRoleId(roleId);
    }

    /**
     * 构建前端路由所需要的菜单
     * 
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenu> menus,long userId) {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenu menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));

            Set<String> keyWords = new HashSet<>();
            SysMenu menuFroQ = new SysMenu(Constants.TYPE_BUTTON,menu.getMenuId());
            if(userId != 1L){
                menuFroQ.getParams().put("userId", tokenService.getUserId());
            }
            List<SysMenu> menuButtons = iSysMenuMapper.selectMenuListByUserId(menuFroQ);
            for(SysMenu sysMenu : menuButtons) {
                if(sysMenu.getKeyWord()!=null){
                    keyWords.add(sysMenu.getKeyWord());
                }
            }
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(),keyWords.toArray(new String[keyWords.size()])));
            List<SysMenu> cMenus = menu.getChildren();
            if (StringUtils.isNotEmpty(cMenus) && Constants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus,userId));
            }else if (isMenuFrame(menu)) {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), keyWords.toArray(new String[keyWords.size()])));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param menus 菜单列表
     * @return 树结构列表
     */
    @Override
    public List<SysMenu> buildMenuTree(List<SysMenu> menus) {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        List<Long> tempList = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
        for (Iterator<SysMenu> iterator = menus.iterator(); iterator.hasNext();) {
            SysMenu menu = (SysMenu) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus) {
        List<SysMenu> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 是否存在菜单子节点
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean hasChildByMenuId(Long menuId) {
        List<SysMenu> menuList = sysMenuService.lambdaQuery().eq(SysMenu::getParentId,menuId).list();
        return !menuList.isEmpty();
    }

    /**
     * 查询菜单使用数量
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean checkMenuExistRole(Long menuId) {
        List<SysRoleMenu> sysRoleMenuList = sysRoleMenuService.lambdaQuery().eq(SysRoleMenu::getMenuId,menuId).list();
        return !sysRoleMenuList.isEmpty();
    }

    /**
     * 删除菜单管理信息
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean deleteMenuById(Long menuId) {
        return sysMenuService.lambdaUpdate()
                .set(SysMenu::getUpdateBy,tokenService.getUserId())
                .set(SysMenu::getUpdateTime,DateUtils.getNowDate())
                .set(SysMenu::getDelFlag, Constants.DELETE_FLAG)
                .eq(SysMenu::getMenuId, menuId).update();
    }

    /**
     * 校验菜单名称是否唯一
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public boolean checkMenuNameUnique(SysMenu menu) {
        Long menuId = StringUtils.isNull(menu.getMenuId()) ? Constants.ALL_MINUS1L : menu.getMenuId();
        List<SysMenu> menuInfos = sysMenuService.lambdaQuery()
                .eq(SysMenu::getMenuName, menu.getMenuName())
                .eq(SysMenu::getParentId, menu.getParentId())
                .eq(SysMenu::getPlatId, menu.getPlatId()).list();
        if (!menuInfos.isEmpty() && menuInfos.get(0).getMenuId().longValue() != menuId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    @Override
    public boolean checkMenuParent(SysMenu menu) {
        SysMenu parent = sysMenuService.getById(menu.getParentId());
        if(parent == null) {
            return true;
        }
        //菜单类型（M目录 C菜单 F按钮）
        if(parent.getMenuType().equals("C") && menu.getMenuType().equals("C")){
            return false;
        }
        return true;
    }

    /**
     * 获取路由名称
     * 
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     * 
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenu menu) {
        String routerPath = menu.getPath();
        // 类型为目录
        if (0 == menu.getParentId().intValue() && Constants.TYPE_DIR.equals(menu.getMenuType())) {
            routerPath = "/" + menu.getPath();
        } else if (isMenuFrame(menu)) {
            // 类型为菜单
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenu menu) {
        String component = Constants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu)) {
            component = menu.getComponent();
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(SysMenu menu) {
        return menu.getParentId().intValue() == 0 && Constants.TYPE_MENU.equals(menu.getMenuType());
    }

    /**
     * 根据父节点的ID获取所有子节点
     * 
     * @param list 分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<SysMenu> getChildPerms(List<SysMenu> list, int parentId) {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        for (Iterator<SysMenu> iterator = list.iterator(); iterator.hasNext();) {
            SysMenu t = (SysMenu) iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     * 
     * @param list 分类表
     * @param t 子节点
     */
    private void recursionFn(List<SysMenu> list, SysMenu t) {
        // 得到子节点列表
        List<SysMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysMenu tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t) {
        List<SysMenu> tlist = new ArrayList<SysMenu>();
        Iterator<SysMenu> it = list.iterator();
        while (it.hasNext()) {
            SysMenu n = (SysMenu) it.next();
            if (n.getParentId().longValue() == t.getMenuId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysMenu> list, SysMenu t) {
        return getChildList(list, t).size() > 0;
    }

    /**
     *
     * @param platId
     * @return
     */
    @Override
    public boolean hasChildByPlatId(Long platId){
        List<SysMenu> menuList = sysMenuService.lambdaQuery().eq(SysMenu::getPlatId,platId).list();
        return !menuList.isEmpty();
    }

    /**
     * 根据userId查询接口路径
     * @param user 用户
     * @param platId 平台id
     * @return 集合
     */
    @Override
    public Set<String> selectIterfacePathsByUserId(SysUser user, Long platId) {
        if (user.isAdmin()) {
            HashSet<String> allPermission = new HashSet<>();
            allPermission.add(Constants.ALL_PERMISSION);
            return allPermission;
        }
        return iSysMenuMapper.selectIterfacePathsByUserId(user.getUserId(),platId);
    }

    @Override
    public List<Long> selectMenuListAndParentIdByRoleId(Long roleId) {
        return iSysMenuMapper.selectMenuAndParentIdListByRoleId(roleId);
    }

    @Override
    public List<SysMenu> getMenuListByName(String menuName, Long platId) {
        if (StringUtils.isNotBlank(menuName)) {
            return sysMenuService.lambdaQuery()
                    .eq(SysMenu::getMenuName, menuName)
                    .eq(SysMenu::getPlatId, platId)
                    .eq(SysMenu::getStatus, Constants.NORMAL)
                    .list();
        }
        return Collections.emptyList();
    }

    @Override
    public List<Long> selectChildIdListByMenuId(Long menuId) {
        if (menuId != null) {
            return iSysMenuMapper.selectChildIdListByMenuId(menuId);
        }
        return Collections.emptyList();
    }

    @Override
    public void deleteMenuByIds(List<Long> menuIds) {
        sysMenuService.lambdaUpdate().in(SysMenu::getMenuId,menuIds).remove();
    }

    @Override
    public void deleteRoleMenuByMenuIds(List<Long> menuIds) {
        sysRoleMenuService.lambdaUpdate().in(SysRoleMenu::getMenuId,menuIds).remove();
    }

    @Override
    public SysMenu addMenu(SysMenu menu) {
        menu.setCreateTime(DateUtils.getNowDate());
        menu.setCreateBy(tokenService.getUserId());
        menu.setMenuId(YitIdHelper.nextId());
        if(menu.getInterfaces()!=null){
            for(Long interfaceId : menu.getInterfaces()){
                sysMenuInterfaceService.save(new SysMenuInterface(menu.getMenuId(),interfaceId));
            }
        }
        return menu;
    }
}
