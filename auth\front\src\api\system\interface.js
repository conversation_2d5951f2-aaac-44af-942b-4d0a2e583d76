import request from '@/utils/request'

// 查询菜单权限列表
export function listInterface(query) {
  return request({
    url: '/system/interface/list',
    method: 'get',
    params: query
  })
}

// 查询菜单权限列表不分页
export function listInterfaceNoPage(query) {
  return request({
    url: '/system/interface/listNoPage',
    method: 'get',
    params: query
  })
}


// 查询菜单权限详细
export function getInterface(interfaceId) {
  return request({
    url: '/system/interface/query?interfaceId=' + interfaceId,
    method: 'get'
  })
}

// 新增菜单权限
export function addInterface(data) {
  return request({
    url: '/system/interface/add',
    method: 'post',
    data: data
  })
}

// 修改菜单权限
export function updateInterface(data) {
  return request({
    url: '/system/interface/edit',
    method: 'post',
    data: data
  })
}

// 删除菜单权限
export function delInterface(interfaceId) {
  return request({
    url: '/system/interface/remove?interfaceId=' + interfaceId,
    method: 'post'
  })
}
