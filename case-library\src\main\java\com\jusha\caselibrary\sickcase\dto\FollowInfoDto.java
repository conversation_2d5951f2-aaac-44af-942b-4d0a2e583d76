package com.jusha.caselibrary.sickcase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;

/**
 * @ClassName FollowInfo
 * @Description 随访信息
 * <AUTHOR>
 * @Date 2025/7/8 17:25
 **/
@Data
public class FollowInfoDto {

    @ApiModelProperty(value = "随访ID")
    @Field(type = FieldType.Long)
    private Long followId;

    @ApiModelProperty(value = "随访类型")
    @Field(type = FieldType.Keyword)
    private String followType;

    @ApiModelProperty(value = "随访类型标签")
    private String followTypeLabel;

    @ApiModelProperty(value = "随访结果")
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String followupResult;

    @ApiModelProperty(value = "随访时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
