package com.jusha.caselibrary.file.task;

import alex.mojaki.s3upload.MultiPartOutputStream;
import alex.mojaki.s3upload.StreamTransferManager;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.FileUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.file.service.impl.FileServiceImpl;
import com.jusha.caselibrary.mybatisplus.entity.Resource;
import com.jusha.caselibrary.mybatisplus.service.ResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.RandomAccessFile;
import java.util.Date;
import java.util.List;

/**
 * minio大文件流式上传任务
 */
@Slf4j
@RequiredArgsConstructor
public class MinioStreamUploadTask implements Runnable {

    /**
     * 任务ID（异步流式上传完后，回调用）
     */
    private final Long taskId;

    /**
     * 文件名(包含后缀，必须在临时文件目录下)
     */
    private final String fileName;


    @Override
    public void run(){
        FileServiceImpl fileService = ContextHolder.getBean(FileServiceImpl.class);
        ResourceService resourceService = ContextHolder.getBean(ResourceService.class);

        String fileFullPath = StringUtils.join(ContextHolder.propertiesBean().getTmpLocation(), File.separator, fileName);

        //实际存储文件名称
        String tempName = StringUtils.join(FilenameUtils.getBaseName(fileName), "_", DateUtil.convertDateToStr(new Date(), "yyyyMMddHHmmss"));
        String actualName = StringUtils.join(DigestUtils.md5Hex(tempName), ".", FilenameUtils.getExtension(fileName));
        String key = StringUtils.join( "/", DateUtil.convertDateToStr(new Date(), "yyyyMM"), "/", DateUtil.convertDateToStr(new Date(), "dd"), "/", actualName);

        StreamTransferManager manager = new StreamTransferManager(ContextHolder.propertiesBean().getMinioBucketName(), key, fileService.getS3());
        List<MultiPartOutputStream> streams = manager.getMultiPartOutputStreams();
        try {
            MultiPartOutputStream outputStream = streams.get(0);

            RandomAccessFile randomAccessFile = new RandomAccessFile(fileFullPath, "r");
            randomAccessFile.seek(0);
            long total = randomAccessFile.length();   //总字节数
            long length = 1*1024*1024;     //每次读取的字节数（1M）
            long num = total/length;     //读取次数
            long left = total%length;    //最后一次字节数
            if(left > 0){
                num++;
            }

            //流式上传
            for(int i = 0; i < num; i++){
                byte[] buffer;
                if(left > 0){
                    buffer = (i==num-1)? new byte[(int) left] :new byte[(int) length];
                }
                else {
                    buffer = new byte[(int) length];
                }

                randomAccessFile.read(buffer);
                //Writing data and potentially sending off a part
                outputStream.write(buffer);
            }

            //全部数据上传完后关闭
            outputStream.close();
            randomAccessFile.close();
        }
        catch (Exception e) {
            manager.abort(e);
        }
        //完成
        manager.complete();

        //入库
        Resource resource = new Resource();
        resource.setResourceId(YitIdHelper.nextId());
        resource.setActualName(key);
        resource.setOriginName(fileName);
        String fileUrl = StringUtils.join("/", ContextHolder.propertiesBean().getMinioBucketName(), key);
        resource.setFileUrl(fileUrl);
        resource.setLmGpId(LoginUtil.isLogin()?LoginUtil.lmGroupId():null);
        resourceService.save(resource);
        //回调,通知caselib服务上传已完成
        //删除：导出任务压缩包
        FileUtil.delFile(new File(fileFullPath));
    }

}