package com.jusha.auth.monitor.controller;

import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.monitor.service.ISysConfigService;
import com.jusha.auth.mybatisplus.entity.SysConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 参数配置 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController {
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @HasPermissions
    @GetMapping("/list")
    public TableDataInfo list(SysConfig config) {
        startPage();
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long configId) {
        return success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/query")
    public ResultBean getConfigKey(@RequestParam String configKey) {
        return success(configService.selectConfigByKey(configKey));
    }

    /**
     * 新增参数配置
     */
    @HasPermissions
    @PostMapping
    public ResultBean add(@Validated @RequestBody SysConfig config) {
        if (!configService.checkConfigKeyUnique(config)) {
            return error(MessageUtils.message("dict.key.name.exist.add"));
        }
        return resultBean(configService.insertConfig(config));
    }

    /**
     * 修改参数配置
     */
    @HasPermissions
    @PostMapping("/edit")
    public ResultBean edit(@Validated @RequestBody SysConfig config) {
        if (!configService.checkConfigKeyUnique(config)) {
            return error(MessageUtils.message("dict.key.name.exist.edit"));
        }
        return resultBean(configService.updateConfig(config));
    }

    /**
     * 删除参数配置
     */
    @HasPermissions
    @PostMapping("/remove")
    public ResultBean remove(@RequestParam Long[] configIds) {
        configService.deleteConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @HasPermissions
    @PostMapping("/refreshCache")
    public ResultBean refreshCache() {
        configService.resetConfigCache();
        return success();
    }
}
