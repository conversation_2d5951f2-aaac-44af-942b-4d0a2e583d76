package com.jusha.ris.docking.common.util;

import com.github.yitter.idgen.YitIdHelper;
import com.jusha.ris.docking.common.acHolder.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509ExtendedTrustManager;
import java.io.File;
import java.net.Socket;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

@Slf4j
public class FileUtil {

    /**
     * MultipartFile 转 File
     * ps: 会生成实际的 File文件，使用完后记得删除
     * @param multipartFile
     * @return
     * @throws Exception
     */
    public static File multipartFileToFile(MultipartFile multipartFile) {
        File file = null;
        try {
            if(multipartFile != null && multipartFile.getSize() > 0){
                String tmpLocation = ContextHolder.propertiesBean().getTmpLocation();
                file = new File(StringUtils.join(tmpLocation, "/", String.valueOf(YitIdHelper.nextId()), "-", multipartFile.getOriginalFilename()));
                FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
            }
        }
        catch (Exception e){
            file = null;
            log.error("multipartFileToFile error:", e);
        }
        return file;
    }

    /**
     * 删除文件
     * @param file
     */
    public static void delFile(File file) {
        if(file != null && file.isFile() && file.exists()) {
            file.delete();
        }
    }

    public static SSLContext createBlindlyTrustingSslContext() {
        try {
            final SSLContext sc = SSLContext.getInstance("TLS");

            sc.init(null, new TrustManager[]{new X509ExtendedTrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                @Override
                public void checkClientTrusted(final X509Certificate[] arg0, final String arg1,
                                               final Socket arg2) {
                    // no-op
                }

                @Override
                public void checkClientTrusted(final X509Certificate[] arg0, final String arg1,
                                               final SSLEngine arg2) {
                    // no-op
                }

                @Override
                public void checkClientTrusted(final X509Certificate[] certs, final String authType) {
                    // no-op
                }

                @Override
                public void checkServerTrusted(final X509Certificate[] certs, final String authType) {
                    // no-op
                }

                @Override
                public void checkServerTrusted(final X509Certificate[] arg0, final String arg1,
                                               final Socket arg2) {
                    // no-op
                }

                @Override
                public void checkServerTrusted(final X509Certificate[] arg0, final String arg1,
                                               final SSLEngine arg2) {
                    // no-op
                }
            }
            }, new java.security.SecureRandom());

            return sc;
        } catch (final NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException("Unexpected exception", e);
        }
    }

}
