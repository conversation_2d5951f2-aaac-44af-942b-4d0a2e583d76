package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.jeffreyning.mybatisplus.anno.MppMultiId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户和角色关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Getter
@Setter
@TableName("sys_user_role")
@ApiModel(value = "SysUserRole对象", description = "用户和角色关联表")
public class SysUserRole {

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    @MppMultiId
    private Long userId;

    @ApiModelProperty(value = "角色ID")
    @TableField("role_id")
    @MppMultiId
    private Long roleId;
}
