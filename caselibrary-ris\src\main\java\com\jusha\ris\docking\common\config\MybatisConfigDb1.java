package com.jusha.ris.docking.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.jusha.ris.docking.common.constant.Constant;
import com.jusha.ris.docking.common.util.LoginUtil;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * mybatis：主数据源(db1)配置
 */
@Configuration
@MapperScan(basePackages = "com.jusha.ris.docking.**.mapper", sqlSessionTemplateRef = "sqlSessionTemplate")
public class MybatisConfigDb1 {

    @Primary
    @Bean(name = "dataSource")
    @ConfigurationProperties(prefix = "spring.datasource.db1")
    public DataSource dataSource() {
        return new DruidDataSource();
    }

    @Primary
    @Bean(name = "sqlSessionFactory")
    public MybatisSqlSessionFactoryBean sqlSessionFactory(@Qualifier("dataSource") DataSource dataSource, @Value("${MINIO_ENDPOINT:}") String minioPath) throws Exception {
        MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource);
        factoryBean.setMapperLocations( new PathMatchingResourcePatternResolver().getResources("classpath:**/mapper/**/*.xml") );
        //mybatisPlus的全局配置
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        dbConfig.setIdType(IdType.INPUT);
        dbConfig.setUpdateStrategy(FieldStrategy.IGNORED);  //更新时不会忽略空值
        GlobalConfig globalConfig = new GlobalConfig().setDbConfig(dbConfig);
        factoryBean.setGlobalConfig(globalConfig);
        //设置分页插件
        factoryBean.setPlugins(new Interceptor[]{new PaginationInterceptor()});
        //全局变量
        Properties configProperties = new Properties();
        configProperties.put("minioPath", LoginUtil.getNetwork().equals(Constant.NETWORK_LAN)?minioPath:"");
        factoryBean.setConfigurationProperties(configProperties);
        return factoryBean;
    }

    @Primary
    @Bean(name = "sqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("sqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Primary
    @Bean(name = "transactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("dataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}