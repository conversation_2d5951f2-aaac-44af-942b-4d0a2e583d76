package com.jusha.caselibrary.sickcase.dto.req;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @ClassName DeptCaseSearchReq
 * @Description 科室病例库列表请求实体
 * <AUTHOR>
 * @Date 2025/7/10 10:09
 **/
@Data
@Validated
public class DeptCaseCreateReq {

    @ApiModelProperty(value = "病例名称")
    private String caseName;

    @ApiModelProperty(value = "病例编号")
    private String caseNo;

    @ApiModelProperty(value = "疾病id")
    private Long diseaseId;

    @ApiModelProperty(value = "最终诊断(疾病名称)")
    private String diagnosis;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "性别")
    private String patientSex;

    @ApiModelProperty(value = "出生日期")
    private String patientBirthDate;

    @ApiModelProperty(value = "年龄")
    private String patientAge;

    @ApiModelProperty(value = "病史")
    private String medicalHistory;

    @ApiModelProperty(value = "难度等级：字典")
    private String difficulty;

    @ApiModelProperty(value = "1典型2非典型")
    private String caseCategory;

    @ApiModelProperty(value = "征象")
    private String sign;

    @ApiModelProperty(value = "病例分析")
    private String caseAnalysis;

    @ApiModelProperty(value = "是否已导出: 0否 1是")
    private String isExport;

    @ApiModelProperty(value = "联盟分组ID")
    private Long lmGpId;

    @ApiModelProperty(value = "来源")
    private String sourceType;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @ApiModelProperty(value = "定性匹配，字典")
    private String qualityMatch;

    @ApiModelProperty(value = "定位匹配，字典")
    private String positionMatch;

}
