package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例导出详情表
 * <AUTHOR>
 */
@TableName("t_export_detail")
@Data
public class ExportDetail {

    @ApiModelProperty(value = "导出详情ID")
    @TableId("export_detail_id")
    private Long exportDetailId;    

    @ApiModelProperty(value = "病例导出任务ID")
    @TableField("export_id")
    private Long exportId;    

    @ApiModelProperty(value = "病例ID")
    @TableField("case_id")
    private Long caseId;    

    @ApiModelProperty(value = "顺序")
    @TableField("seq")
    private Integer seq;    

    @ApiModelProperty(value = "状态：0-未开始 1-进行中 2-成功 3-失败")
    @TableField("status")
    private Integer status;    

    @ApiModelProperty(value = "失败原因")
    @TableField("reason")
    private String reason;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

}
