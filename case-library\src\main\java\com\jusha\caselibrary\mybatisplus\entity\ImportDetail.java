package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 病例导入详情表
 * <AUTHOR>
 */
@TableName("t_import_detail")
@Data
public class ImportDetail {

    @ApiModelProperty(value = "导入详情ID")
    @TableId("inport_detail_id")
    private Long inportDetailId;    

    @ApiModelProperty(value = "病例导入任务ID")
    @TableField("inport_id")
    private Long inportId;    

    @ApiModelProperty(value = "病例ID")
    @TableField("case_id")
    private Long caseId;    

    @ApiModelProperty(value = "顺序")
    @TableField("seq")
    private Integer seq;    

    @ApiModelProperty(value = "状态：0-未开始 1-成功 2-失败 3-重复")
    @TableField("status")
    private Integer status;    

    @ApiModelProperty(value = "失败原因")
    @TableField("reason")
    private String reason;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

}
