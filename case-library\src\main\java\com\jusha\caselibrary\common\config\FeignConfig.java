package com.jusha.caselibrary.common.config;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import feign.RequestInterceptor;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

/**
 * feign配置
 */
@Configuration
public class FeignConfig {

    /**
     * NamingService
     */
    private static NamingService namingService;

    /**
     * 判定nacos注册中心是否存在某个服务
     * @param serverName
     * @return
     */
    @SneakyThrows
    public static boolean hasServer(String serverName){
        if(namingService == null){
            Properties properties = new Properties();
            properties.put("serverAddr", ContextHolder.propertiesBean().getNacosAddr());
            properties.put("namespace", ContextHolder.propertiesBean().getNacosNamespace());
            namingService = NacosFactory.createNamingService(properties);
        }

        List<Instance> instances = namingService.getAllInstances(serverName);
        return CollectionUtils.isNotEmpty(instances)?true:false;
    }


    /**
     * feign拦截器：添加Header和Cookie
     */
    @Bean("requestInterceptor")
    public RequestInterceptor requestInterceptor() {
        // 创建拦截器
        return template -> {
            // 使用RequestContextHolder拿到原生请求的请求头信息（下文环境保持器）
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (requestAttributes != null) {
                // 如果使用线程池进行远程调用，则request是空的（因为RequestContextHolder.getRequestAttributes()是从threadlocal里拿的值）
                HttpServletRequest oldRequest = requestAttributes.getRequest();
                if (Objects.nonNull(oldRequest)) {
                    //同步老请求里的cookie
                    String oldCookie = oldRequest.getHeader("Cookie");
                    if(StringUtils.isNotBlank(oldCookie)){
                        template.header("Cookie", oldCookie);
                    }
                    //同步老请求里的header
                    Enumeration<String> oldHeaderNames = oldRequest.getHeaderNames();
                    if (null != oldHeaderNames) {
                        while (oldHeaderNames.hasMoreElements()) {
                            String headerName = oldHeaderNames.nextElement();
                            if(template.headers().keySet().contains(headerName)) {
                                continue;
                            }
                            String headerValue = oldRequest.getHeader(headerName);
                            template.header(headerName, headerValue);
                        }
                    }
                }
            }
        };
    }

}

