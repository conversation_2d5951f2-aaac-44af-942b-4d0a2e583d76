package com.jusha.auth.monitor.controller;

import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.monitor.service.ISysDictTypeService;
import com.jusha.auth.mybatisplus.entity.SysDictType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/type")
public class SysDictTypeController extends BaseController {
    @Autowired
    private ISysDictTypeService dictTypeService;

    @HasPermissions
    @GetMapping(value = "/list")
    public TableDataInfo list(SysDictType dictType) {
        startPage();
        List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
        return getDataTable(list);
    }

    /**
     * 查询字典类型详细
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long dictId) {
        return success(dictTypeService.selectDictTypeById(dictId));
    }

    /**
     * 新增字典类型
     */
    @HasPermissions
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysDictType dict) {
        if (!dictTypeService.checkDictTypeUnique(dict)) {
            return error(MessageUtils.message("dict.key.type.exist.add"));
        }
        return resultBean(dictTypeService.insertDictType(dict));
    }

    /**
     * 修改字典类型
     */
    @HasPermissions
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysDictType dict) {
        if (!dictTypeService.checkDictTypeUnique(dict)) {
            return error(MessageUtils.message("dict.key.type.exist.edit"));
        }
        return resultBean(dictTypeService.updateDictType(dict));
    }

    /**
     * 删除字典类型
     */
    @HasPermissions
    @PostMapping(value = "/remove")
    public ResultBean remove(@RequestParam Long[] dictIds) {
        dictTypeService.deleteDictTypeByIds(dictIds);
        return success();
    }

    /**
     * 刷新字典缓存
     */
    @HasPermissions
    @PostMapping(value = "/refreshCache")
    public ResultBean refreshCache() {
        dictTypeService.resetDictCache();
        return success();
    }

    /**
     * 获取字典选择框列表
     */
    @GetMapping(value = "/optionselect")
    public ResultBean optionselect() {
        List<SysDictType> dictTypes = dictTypeService.selectDictTypeAll();
        return success(dictTypes);
    }
}
