package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import javax.validation.constraints.Size;
import java.util.*;

/**
 * <p>
 * 菜单权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-27
 */
@Getter
@Setter
@TableName("sys_menu")
@ApiModel(value = "SysMenu对象", description = "菜单权限表")
public class SysMenu {

    @ApiModelProperty(value = "菜单ID")
    @TableId(value = "menu_id")
    private Long menuId;

    @ApiModelProperty(value = "所属平台id")
    @TableField("plat_id")
    private Long platId;

    @ApiModelProperty(value = "菜单名称")
    @TableField("menu_name")
    @Size(message = "菜单名称长度不合法",max = 20)
    private String menuName;

    @ApiModelProperty(value = "父菜单ID")
    @TableField("parent_id")
    private Long parentId;

    @ApiModelProperty(value = "显示顺序")
    @TableField("order_num")
    private Integer orderNum;

    @ApiModelProperty(value = "路由地址")
    @TableField("path")
    @Size(message = "路由地址长度不合法",max = 50)
    private String path;

    @ApiModelProperty(value = "访问路径")
    @TableField("component")
    @Size(message = "访问路径长度不合法",max = 50)
    private String component;

    @ApiModelProperty(value = "菜单类型（M目录 C菜单 F按钮）")
    @TableField("menu_type")
    private String menuType;

    @ApiModelProperty(value = "菜单状态（0显示 1隐藏）")
    @TableField("visible")
    private String visible;

    @ApiModelProperty(value = "菜单状态（0正常 1停用）")
    @TableField("status")
    private String status;

    @ApiModelProperty(value = "关键字")
    @TableField("key_word")
    @Size(message = "关键字长度不合法",max = 50)
    private String keyWord;

    @ApiModelProperty(value = "关键字描述")
    @TableField("key_describe")
    private String keyDescribe;

    @ApiModelProperty(value = "菜单图标")
    @TableField("icon")
    private String icon;

    @ApiModelProperty(value = "0正常1删除")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;

    @ApiModelProperty(value = "创建者")
    @TableField("create_by")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField("update_by")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    public SysMenu(Long platId,String status){
        this.platId = platId;
        this.status = status;
    }

    public SysMenu(String menuType,Long parentId){
        this.parentId = parentId;
        this.menuType = menuType;
    }

    public SysMenu(){
       super();
    }

    @TableField(exist = false)
    private List<SysMenu> children = new ArrayList<SysMenu>();

    @TableField(exist = false)
    private Long[] interfaces;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }
}
