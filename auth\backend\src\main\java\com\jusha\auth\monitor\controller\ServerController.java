package com.jusha.auth.monitor.controller;

import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.monitor.domain.server.Server;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务器监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/server")
public class ServerController {
    @HasPermissions
    @GetMapping()
    public ResultBean getInfo() throws Exception {
        Server server = new Server();
        server.copyTo();
        return ResultBean.success(server);
    }
}
