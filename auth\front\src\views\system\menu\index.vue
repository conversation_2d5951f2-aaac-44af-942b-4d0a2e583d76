<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="所属平台" prop="platId">
        <el-select :default-first-option="false" v-model="queryParams.platId" placeholder="所属平台">
          <el-option
            v-for="item in this.platList"
            :key="item.platId"
            :label="item.platName"
            :value="item.platId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="菜单名称" prop="menuName">
        <el-input
          maxlength="50"
          v-model="queryParams.menuName"
          placeholder="请输入菜单名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="菜单状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-if="keyWords.includes('add')"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="menuList"
      row-key="menuId"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="menuName" label="菜单名称" :show-overflow-tooltip="true" width="200"></el-table-column>
      <el-table-column prop="icon" label="图标" align="center" width="100" v-if="this.tubiaoFlag">
        <template slot-scope="scope">
          <svg-icon :icon-class="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="排序" width="150"></el-table-column>
      <!-- <el-table-column prop="perms" label="后端接口路径" :show-overflow-tooltip="true"></el-table-column> -->
      <el-table-column prop="component" label="访问路径" :show-overflow-tooltip="true" width="300"></el-table-column>
      <!-- <el-table-column prop="status" label="状态" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column> -->
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-if="keyWords.includes('edit')"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
            v-if="keyWords.includes('add')"
          >新增</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-if="keyWords.includes('remove')"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单对话框  -->
    <el-dialog :title="title" :visible.sync="open" width="720px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属平台" prop="platId">
              <el-select v-model="form.platId" placeholder="所属平台" @change="changeSelectedPlat">
                <el-option
                  v-for="item in this.platList"
                  :key="item.platId"
                  :label="item.platName"
                  :value="item.platId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级菜单" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="menuOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="menuType">
              <el-radio-group v-model="form.menuType" @change="changeRadio">
                <el-radio label="M">目录</el-radio>
                <el-radio label="C">菜单</el-radio>
                <el-radio label="F">元素</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.menuType != 'F' && form.platId == 0">
            <el-form-item label="菜单图标" prop="icon">
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected" :active-icon="form.icon" />
                <el-input slot="reference" v-model="form.icon" placeholder="点击选择图标" readonly>
                  <svg-icon
                    v-if="form.icon"
                    slot="prefix"
                    :icon-class="form.icon"
                    style="width: 25px;"
                  />
                  <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.menuType === 'M'" label="目录名称" prop="menuName">
              <el-input v-model="form.menuName" placeholder="请输入目录名称" />
            </el-form-item>
            <el-form-item v-if="form.menuType === 'C'" label="菜单名称" prop="menuName">
              <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
            </el-form-item>
            <el-form-item v-if="form.menuType === 'F'" label="元素名称" prop="menuName">
              <el-input v-model="form.menuName" placeholder="请输入元素名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item  label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item prop="component">
              <span slot="label">
                <el-tooltip content="页面访问路径，如：`system/user/index`" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                访问路径
              </span>
              <el-input v-model="form.component" placeholder="请输入页面访问路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="path">
              <span slot="label">
                <el-tooltip content="访问的路由地址，如：`user`" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                路由地址
              </span>
              <el-input v-model="form.path" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="form.menuType  === 'F'">
            <el-form-item label="关键字" prop="keyWord">
              <el-input v-model="form.keyWord" placeholder="请输入关键字" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType  === 'F'">
            <el-form-item label="中文描述" prop="keyDescribe">
              <el-input v-model="form.keyDescribe" placeholder="请输入关键字的中文描述" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'M'">
            <el-form-item label="后端接口" prop="interfaces">
              <el-select v-model="form.interfaces" multiple filterable placeholder="请选择接口" prop="interfaces">
                <el-option
                  v-for="item in interfaceList"
                  :key="item.interfaceId"
                  :label="item.interfaceName"
                  :value="item.interfaceId">
                  <span style="float: left">{{ item.interfaceName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 10px">{{ item.interfacePath }}</span>
              </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType != 'F'">
            <el-form-item prop="visible">
              <span slot="label">
                <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                显示状态
              </span>
              <el-radio-group v-model="form.visible">
                <el-radio
                  v-for="dict in dict.type.sys_show_hide"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item prop="status">
              <span slot="label">
                <el-tooltip content="选择停用则不会出现在侧边栏，也不能被访问" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                菜单状态
              </span>
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPlat} from "@/api/system/plat";
import { listInterfaceNoPage} from "@/api/system/interface";
import { listMenu, getMenu, delMenu, addMenu, updateMenu } from "@/api/system/menu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";

export default {
  name: "Menu",
  dicts: ['sys_show_hide', 'sys_normal_disable','button_type'],
  components: { Treeselect, IconSelect },
  data() {
    return {
      keyWords : [],
      // 遮罩层
      loading: true,
      tubiaoFlag : false,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        platId : undefined,
        menuName: undefined,
        visible: undefined
      },
      queryParamsDialog: {
        platId : undefined,
      },
      // 表单参数
      form: {},
      platList:[],
      interfaceList:[],
      // 表单校验
      rules: {
        platId: [
          { required: true, message: "所属平台不能为空", trigger: "blur" }
        ],
        parentId: [
          { required: true, message: "上级菜单不能为空", trigger: "blur" }
        ],
        menuType: [
          { required: true, message: "菜单类型不能为空", trigger: "blur" }
        ],
        menuName: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '名称长度必须介于 2 和 20 之间', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/,
            message: "仅支持中文、英文、数字及下划线",
            trigger: "blur"
          }
        ],
        keyWord: [
          { required: true, message: "关键字不能为空", trigger: "blur" },
          { min: 2, max: 50, message: '关键字长度必须介于 2 和 50 之间', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9_]{2,50}$/,
            message: "仅支持英文字母、数字及下划线",
            trigger: "blur"
          }
        ],
        orderNum: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" }
        ],
        path: [
          { required: true, message: "路由地址不能为空", trigger: "blur" },
          { min: 2, max: 50, message: '路由地址长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        keyDescribe :[
          { min: 2, max: 50, message: '中文描述长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        component :[
        { required: true, message: "访问路径不能为空", trigger: "blur" },
          { min: 2, max: 50, message: '访问路径长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        visible:[
          { required: true, message: "显示状态不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getButton();
    this.getPlatList();
  },
  methods: {
    getButton(){
      this.keyWords = this.$route.meta.keyWords
    },
    getInterfaceList(){
      listInterfaceNoPage(this.queryParamsDialog).then(response => {
        this.interfaceList = response.data;
      });
    },
    getPlatList(){
      listPlat(this.queryParams).then(response => {
        this.platList = response.rows;
        if(response.total!=0){
          this.queryParams.platId = this.platList[0].platId
          this.getList();
        }else{
          this.queryParams.platId = undefined
          this.getList();
        }
      });
    },
    // 选择图标
    selected(name) {
      this.form.icon = name;
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      listMenu(this.queryParams).then(response => {
        this.menuList = this.handleTree(response.data, "menuId");
        this.loading = false;
      });
      if(this.queryParams.platId === 0){
        this.tubiaoFlag = true
      }else{
        this.tubiaoFlag = false
      }
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.menuId,
        label: node.menuName,
        children: node.children
      };
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      listMenu(this.queryParamsDialog).then(response => {
        this.menuOptions = [];
        const menu = { menuId: 0, menuName: '菜单根目录', children: [] };
        menu.children = this.handleTree(response.data, "menuId");
        this.menuOptions.push(menu);
      });
    },
    changeRadio(){
      this.$refs["form"].clearValidate();
    },
    changeSelectedPlat(){
      this.form.parentId = undefined
      this.queryParamsDialog.platId = this.form.platId
      this.getTreeselect();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        menuId: undefined,
        parentId: 0,
        menuName: undefined,
        icon: undefined,
        menuType: "M",
        orderNum: undefined,
        isFrame: "1",
        isCache: "0",
        visible: "0",
        status: "0",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.getPlatList();
      // this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.queryParamsDialog.platId = this.queryParams.platId
      this.getTreeselect();
      if (row != null && row.menuId) {
        this.form.parentId = row.menuId;
      } else {
        this.form.parentId = 0;
      }
      this.form.platId = this.queryParams.platId
      this.queryParamsDialog.platId = this.form.platId
      this.getInterfaceList()
      this.open = true;
      this.title = "添加菜单";
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.queryParamsDialog.platId = this.queryParams.platId
      this.getInterfaceList()
      this.getTreeselect();
      getMenu(row.menuId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改菜单";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      // if(this.form.interfaces.length === 0){
      //   this.$message.error(`后端接口不能为空!`);
      //   return false;
      // }
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.menuId != undefined) {
            updateMenu(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMenu(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除"' + row.menuName + '"？').then(function() {
        return delMenu(row.menuId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
