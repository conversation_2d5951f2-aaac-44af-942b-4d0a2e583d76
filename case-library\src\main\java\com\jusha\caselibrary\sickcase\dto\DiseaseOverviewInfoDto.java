package com.jusha.caselibrary.sickcase.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @ClassName DiseaseOverviewInfo
 * @Description 疾病概述信息
 * <AUTHOR>
 * @Date 2025/7/8 17:26
 **/
@Data
public class DiseaseOverviewInfoDto {

    @ApiModelProperty(value = "疾病概述")
    private String overview;

    @ApiModelProperty(value = "病理表现")
    private String pathology;

    @ApiModelProperty(value = "临床表现")
    private String clinical;

    @ApiModelProperty(value = "影像学表现")
    private String imaging;

    @ApiModelProperty(value = "诊断依据")
    private String diagnosis;

    @ApiModelProperty(value = "鉴别诊断")
    private String differential;

    @ApiModelProperty(value = "关键帧")
    private String keyframe;
}
