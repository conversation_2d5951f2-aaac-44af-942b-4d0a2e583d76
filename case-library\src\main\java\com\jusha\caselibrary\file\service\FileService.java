package com.jusha.caselibrary.file.service;

import com.jusha.caselibrary.file.dto.FileReportDto;
import com.jusha.caselibrary.file.dto.req.StreamUploadReq;
import com.jusha.caselibrary.file.resp.FileUploadResp;
import com.jusha.caselibrary.file.resp.SignatureBucketResponse;
import com.jusha.caselibrary.mybatisplus.entity.Resource;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

/**
 * @ClassName FileService
 * @Description 文件服务接口
 * <AUTHOR>
 * @Date 2025/7/11 11:03
 **/
public interface FileService {

    /**
     * 单个文件上传到minio
     * @param uploadResp
     * @return
     */
    void upload(FileUploadResp uploadResp);

    /**
     * 多个文件上传到minio
     * @param multipartFiles
     * @return
     */
    List<FileUploadResp> uploadMulti(MultipartFile[] multipartFiles);

    /**
     * 文件删除
     * @param resourceId
     */
    void deleteFile(Long resourceId);

    /**
     * 文件批量删除
     * @param resourceIds
     */
    void deleteFileBatch(List<Long> resourceIds);

    /**
     * 大文件异步流式上传
     * @param req
     */
    void streamUpload(StreamUploadReq req);

    /**
     * 前端自己上传文件后，上报给后端
     * @param fileReportDto
     */
    Resource report(FileReportDto fileReportDto);


    /**
     * 获取上传凭证，有效期只有十分钟
     * @param fileName
     * @return
     */
    SignatureBucketResponse getPostPolicy(String fileName, String bucket);
}
