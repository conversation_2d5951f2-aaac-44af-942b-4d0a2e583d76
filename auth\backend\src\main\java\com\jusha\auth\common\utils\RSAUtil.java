package com.jusha.auth.common.utils;
/**
 * RSA加密
 *
 * <AUTHOR>
 */
import com.jusha.auth.common.exception.ServiceException;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import sun.misc.BASE64Decoder;
import javax.crypto.Cipher;
import java.io.IOException;
import java.security.*;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Arrays;

public class RSAUtil {

    private static Cipher cipher;
    static {
        try {
            Provider provider = new BouncyCastleProvider();
            Security.addProvider(provider);
            cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding", provider);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static final KeyPair keyPair = initKey();

    private static KeyPair initKey() {
        try {
            Provider provider = new BouncyCastleProvider();
            Security.addProvider(provider);
            SecureRandom random = new SecureRandom();
            KeyPairGenerator generator = KeyPairGenerator.getInstance("RSA", provider);
            generator.initialize(1024, random);
            return generator.generateKeyPair();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String decryptBase64WithPrivate(String string, PrivateKey privateKey) {
        return new String(decryptWithPrivate(Base64.decodeBase64(string.getBytes()), privateKey));
    }

    private static byte[] decryptWithPrivate(byte[] byteArray, PrivateKey privateKey) {
        try {
//            Provider provider = new BouncyCastleProvider();
//            Security.addProvider(provider);
//            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding", provider);
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] plainText = cipher.doFinal(byteArray);
            return plainText;
        } catch (Exception e) {
            throw new ServiceException(MessageUtils.message("user.password.not.match"));
        }
    }

    public static String generateBase64PublicKey() {
        PublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        return new String(Base64.encodeBase64(publicKey.getEncoded()));
    }

    public static PrivateKey loadPrivateKey(String key64) throws GeneralSecurityException, IOException {
        byte[] clear = new BASE64Decoder().decodeBuffer(key64);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(clear);
        KeyFactory fact = KeyFactory.getInstance("RSA");
        PrivateKey priv = fact.generatePrivate(keySpec);
        Arrays.fill(clear, (byte) 0);
        return priv;

    }
}
 
 