package com.jusha.auth.system.controller;

import com.jusha.auth.common.annotation.EscapeWildcard;
import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.annotation.NoDuplicate;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.core.redis.RedisCache;
import com.jusha.auth.common.core.service.SysPasswordService;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.RSAUtil;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.SysRole;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.system.domain.SysUserForAuthRole;
import com.jusha.auth.system.service.ISysRoleService;
import com.jusha.auth.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.PrivateKey;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 新增用户
     */
    @HasPermissions
    @PostMapping(value = "/add")
    @NoDuplicate(keys = {"#user.UserName"},waitTag = 2)
    public ResultBean add(@Validated @RequestBody SysUser user) throws GeneralSecurityException, IOException {
        if (!iSysUserService.checkUserNameUnique(user)) {
            return error(MessageUtils.message("user.already.exist.add"));
        }else if (StringUtils.isNotEmpty(user.getPhoneNumber()) && !iSysUserService.checkPhoneUnique(user)) {
            return error(MessageUtils.message("user.phone.already.exist.add"));
        }else if (StringUtils.isNotEmpty(user.getWorkNumber()) && !iSysUserService.checkWorkNumberUnique(user)) {
            return error(MessageUtils.message("user.worknumber.already.exist.add"));
        }
        if(user.getPassword()!=null){
            PrivateKey privateKey = RSAUtil.loadPrivateKey(redisCache.getCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PRIVATE).toString());
            String password = RSAUtil.decryptBase64WithPrivate(user.getPassword().trim(), privateKey);
            user.setPassword(password);
        }
        return success(iSysUserService.insertUser(user));
    }

    /**
     * 获取用户列表
     */
    @HasPermissions
    @EscapeWildcard
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        List<SysUser> list = iSysUserService.selectUserList(user);
        return getDataTable(list);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long userId,@RequestParam Long platId) {
        SysUser sysUser = iSysUserService.selectUserById(userId,platId);
        List<SysRole> roles = roleService.selectRoleAll(platId);
        ResultBean resultBean = ResultBean.success();
        resultBean.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        resultBean.put(ResultBean.DATA_TAG, sysUser);
        resultBean.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        return resultBean;
    }

    /**
     * 修改用户
     */
    @HasPermissions
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysUser user) {
        iSysUserService.checkUserAllowed(user);
        if (!iSysUserService.checkUserNameUnique(user)) {
            return error(MessageUtils.message("user.already.exist.edit"));
        }else if (StringUtils.isNotEmpty(user.getPhoneNumber()) && !iSysUserService.checkPhoneUnique(user)) {
            return error(MessageUtils.message("user.phone.already.exist.edit"));
        }else if (StringUtils.isNotEmpty(user.getWorkNumber()) && !iSysUserService.checkWorkNumberUnique(user)) {
            return error(MessageUtils.message("user.worknumber.already.exist.edit"));
        }
        return resultBean(iSysUserService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @HasPermissions
    @PostMapping("/remove")
    public ResultBean remove(@RequestParam Long userId) {
        if (getUserId().equals(userId)) {
            return error(MessageUtils.message("user.myself.delete"));
        }
        return resultBean(iSysUserService.deleteUserById(userId));
    }

    /**
     * 重置密码
     */
    @HasPermissions
    @PostMapping("/resetPwd")
    public ResultBean resetPwd(@Validated @RequestBody SysUser user) throws Exception {
        iSysUserService.checkUserAllowed(user);
        PrivateKey privateKey = RSAUtil.loadPrivateKey(redisCache.getCacheObject(Constants.SYS_RSA_KEY + Constants.RSA_KEY_PRIVATE).toString());
        String password = RSAUtil.decryptBase64WithPrivate(user.getPassword().trim(), privateKey);
        user.setPassword(SysPasswordService.encryptPassword(password));
        return resultBean(iSysUserService.updateUser(user));
    }

    /**
     * 状态修改
     */
    @HasPermissions
    @PostMapping("/changeStatus")
    public ResultBean changeStatus(@Validated @RequestBody SysUser user) {
        iSysUserService.checkUserAllowed(user);
        return resultBean(iSysUserService.updateUser(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @HasPermissions
    @GetMapping("/authRole/query")
    public ResultBean authRole(@RequestParam Long userId,@RequestParam Long platId) {
        ResultBean resultBean = ResultBean.success();
        SysUser user = iSysUserService.selectUserById(userId,platId);
        user.setPlatId(platId);
        List<SysRole> roles = roleService.selectRolesByUser(user);
        resultBean.put("user", user);
        resultBean.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return resultBean;
    }

    /**
     * 用户授权角色
     */
    @HasPermissions
    @PostMapping("/authRole")
    public ResultBean insertAuthRole(@RequestBody SysUserForAuthRole user) {
        iSysUserService.insertUserAuth(user.getUserId(), user.getRoleIds(), user.getPlatId());
        return success();
    }

    @HasPermissions
    @PostMapping("/importData")
    public ResultBean importExcel(HttpServletRequest request, HttpServletResponse response){
        return iSysUserService.importExcel(request, response);
    }

    @HasPermissions
    @PostMapping("/export")
    public ModelAndView export(SysUser user) {
        return iSysUserService.export(user);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response){
        iSysUserService.importTemplate(response);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public ResultBean getInfo() {
        LoginUser loginUser = getLoginUser();
        ResultBean resultBean = ResultBean.success();
        resultBean.put("user", loginUser);
        return resultBean;
    }

    /**
     * 首页用户数统计
     * @return
     */
    @GetMapping("/userStatistics")
    public ResultBean userStatistics() {
        return ResultBean.success(iSysUserService.userStatistics());
    }
}
