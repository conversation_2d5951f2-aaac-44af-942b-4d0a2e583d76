package com.jusha.auth.system.controller;

import com.jusha.auth.common.annotation.EscapeWildcard;
import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.page.TableDataInfo;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.SysInterface;
import com.jusha.auth.system.service.ISysInterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 接口Controller
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@RestController
@RequestMapping("/system/interface")
public class SysInterfaceController extends BaseController {
    @Autowired
    private ISysInterfaceService isysInterfaceService;

    /**
     * 查询接口列表
     */
    @HasPermissions
    @GetMapping("/list")
    @EscapeWildcard
    public TableDataInfo list(SysInterface sysInterface) {
        startPage();
        List<SysInterface> list = isysInterfaceService.selectSysInterfaceList(sysInterface);
        return getDataTable(list);
    }

    @HasPermissions
    @GetMapping("/listNoPage")
    @EscapeWildcard
    public ResultBean listNoPage(SysInterface sysInterface) {
        List<SysInterface> list = isysInterfaceService.selectSysInterfaceList(sysInterface);
        return success(list);
    }

    /**
     * 获取接口详细信息
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long interfaceId) {
        return success(isysInterfaceService.selectSysInterfaceByInterfaceId(interfaceId));
    }

    /**
     * 新增接口
     */
    @HasPermissions
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysInterface sysInterface) {
        if (!isysInterfaceService.checkInterfaceNameUnique(sysInterface)) {
            return ResultBean.error(MessageUtils.message("interface.already.exist.add"));
        }
        if (!isysInterfaceService.checkInterfacePathUnique(sysInterface)) {
            return ResultBean.error(MessageUtils.message("interfacePath.already.exist.add"));
        }
        return resultBean(isysInterfaceService.insertSysInterface(sysInterface));
    }

    /**
     * 修改接口
     */
    @HasPermissions
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysInterface sysInterface) {
        if (!isysInterfaceService.checkInterfaceNameUnique(sysInterface)) {
            return ResultBean.error(MessageUtils.message("interface.already.exist.edit"));
        }
        if (!isysInterfaceService.checkInterfacePathUnique(sysInterface)) {
            return ResultBean.error(MessageUtils.message("interfacePath.already.exist.edit"));
        }
        return resultBean(isysInterfaceService.updateSysInterface(sysInterface));
    }

    /**
     * 删除接口
     */
    @HasPermissions
    @PostMapping("/remove")
    public ResultBean remove(@RequestParam Long interfaceId) {
        String menuNames = isysInterfaceService.checkInterfaceMenu(interfaceId);
        if (StringUtils.isNotEmpty(menuNames)) {
            if(menuNames.endsWith(",")){
                menuNames = menuNames.substring(0, menuNames.length() - 1);
            }
            menuNames = " ["+ menuNames + "] ";
            return ResultBean.error(MessageUtils.message("interfacePath.already.menu.remove",  new Object[]{menuNames}));
        }
        return resultBean(isysInterfaceService.deleteSysInterfaceByInterfaceId(interfaceId));
    }
}
