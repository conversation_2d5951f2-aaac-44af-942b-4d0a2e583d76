package com.jusha.auth.system.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.service.TokenService;
import com.jusha.auth.common.exception.ServiceException;
import com.jusha.auth.common.utils.DateUtils;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.common.utils.spring.SpringUtils;
import com.jusha.auth.mybatisplus.entity.*;
import com.jusha.auth.mybatisplus.service.*;
import com.jusha.auth.system.mapper.ISysRoleMapper;
import com.jusha.auth.system.service.ISysPlatService;
import com.jusha.auth.system.service.ISysRoleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ISysRoleServiceImpl implements ISysRoleService {
    @Autowired
    private ISysRoleMapper roleMapper;

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @Autowired
    private SysRoleGroupService sysRoleGroupService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysPlatService iSysPlatService;

    @Autowired
    private SysGroupService sysGroupService;

    @Value("${token.network:}")
    private String network;

    /**
     * 根据条件分页查询角色数据
     *
     * @param sysRole 角色信息
     * @return 角色数据集合信息
     */
    @Override
    public List<SysRole> selectRoleList(SysRole sysRole) {
        LambdaQueryChainWrapper<SysRole> wrapper = sysRoleService.lambdaQuery();
        if (sysRole.getRoleName() != null) {
            wrapper.like(SysRole::getRoleName, sysRole.getRoleName());
        }
        if (sysRole.getStatus() != null) {
            wrapper.eq(SysRole::getStatus, sysRole.getStatus());
        }
        if (sysRole.getPlatId() != null) {
            wrapper.eq(SysRole::getPlatId, sysRole.getPlatId());
        }
        wrapper.ne(SysRole::getRoleId, Constants.ADMIN_ROLE_ID);
        return wrapper.orderByDesc(SysRole::getCreateTime).list();
    }

    @Override
    public List<SysRole> selectRoleListByType(Long platId, String roleType) {
        LambdaQueryChainWrapper<SysRole> wrapper = sysRoleService.lambdaQuery();
        wrapper.eq(SysRole::getPlatId, platId);
        wrapper.eq(SysRole::getRoleType, roleType);
        wrapper.ne(SysRole::getRoleId, Constants.ADMIN_ROLE_ID);
        return wrapper.orderByDesc(SysRole::getCreateTime).list();
    }

    /**
     * 根据用户查询角色
     *
     * @param user 用户
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRolesByUser(SysUser user) {
        List<SysRole> userRoles = roleMapper.selectRolePermissionByUser(user);

        SysUser myUser = tokenService.getLoginUser().getSysUser();
        myUser.setPlatId(user.getPlatId());

        List<SysRole> roles = new ArrayList<>();
        if(myUser.isAdmin()){
            roles = selectRoleAll(myUser.getPlatId());
        }else{
            //这里要加个逻辑，适配一下教学平台互联网版，如果查询的人的角色是非中心分组的，那么只让他查看到自己医院关联的几个角色
            List<SysRole> realRoles = myUser.getRoles();
            List<Long> groupIds = new ArrayList<>();
            for(SysRole role : realRoles){
                List<SysRoleGroup> sysRoleGroupList = sysRoleGroupService.lambdaQuery().eq(SysRoleGroup::getRoleId,role.getRoleId()).list();
                for(SysRoleGroup sysRoleGroup : sysRoleGroupList){
                    groupIds.add(sysRoleGroup.getGroupId());
                }
            }
            if(groupIds.isEmpty()){
                return new ArrayList<>();
            }
            //查出这个角色的所有关联分组
            List<SysGroup> sysGroupList = sysGroupService.lambdaQuery().in(SysGroup::getGroupId,groupIds).list();
            boolean center = false;
            for(SysGroup sysGroup : sysGroupList){
                if(sysGroup.getCenter().equals("0")){
                    center = true;
                }
            }
            if(!center){
                List<Long> roleIds = new ArrayList<>();
                //拿到这些分组关联的所有角色
                for(SysGroup sysGroup : sysGroupList){
                    List<SysRoleGroup> sysRoleGroupList = sysRoleGroupService.lambdaQuery().eq(SysRoleGroup::getGroupId, sysGroup.getGroupId()).list();
                    for(SysRoleGroup sysRoleGroup : sysRoleGroupList){
                        roleIds.add(sysRoleGroup.getRoleId());
                    }
                }
                roles.addAll(sysRoleService.lambdaQuery().in(SysRole::getRoleId,roleIds).list());
            }else {
                roles = selectRoleAll(user.getPlatId());
            }
        }

        for (SysRole role : roles) {
            for (SysRole userRole : userRoles) {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue()) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    @Override
    public List<SysRole> authRoleListByUserId(SysUser user) {
        return roleMapper.selectRoleByUser(user);
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll(long platId) {
        return SpringUtils.getAopProxy(this).selectRoleList(new SysRole(platId,"0"));
    }

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    @Override
    public List<Long> selectRoleListByUserId(Long userId) {
        return roleMapper.selectRoleListByUserId(userId);
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRole selectRoleById(Long roleId) {
        return sysRoleService.getById(roleId);
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleNameUnique(SysRole role) {
        Long roleId = StringUtils.isNull(role.getRoleId()) ? Constants.ALL_MINUS1L : role.getRoleId();
        List<SysRole> roleInfos = sysRoleService.lambdaQuery()
                .eq(SysRole::getRoleName, role.getRoleName())
                .eq(SysRole::getPlatId, role.getPlatId()).list();
        if (!roleInfos.isEmpty() && roleInfos.get(0).getRoleId().longValue() != roleId.longValue()) {
            return Constants.NOT_UNIQUE;
        }
        return Constants.UNIQUE;
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    @Override
    public void checkRoleAllowed(SysRole role) {
        if (StringUtils.isNotNull(role.getRoleId()) && role.isAdmin()) {
            throw new ServiceException(MessageUtils.message("user.dowith.admin.role"));
        }
    }

    /**
     * 校验角色是否有数据权限
     *
     * @param platId 平台id
     */
    @Override
    public void checkRoleDataScope(String platId) {
        if (platId == null) {
            throw new ServiceException(MessageUtils.message("take.plate.parameters"));
        }
        if (iSysPlatService.selectSysPlatByPlatId(Long.parseLong(platId)) == null) {
            throw new ServiceException(MessageUtils.message("this.plate.not.exist"));
        }
        if (!SysUser.isAdmin(tokenService.getUserIdCanNull())) {
            SysRole role = new SysRole();
            role.setPlatId(Long.parseLong(platId));
            List<SysRole> roles = SpringUtils.getAopProxy(this).selectRoleList(role);
            if (StringUtils.isEmpty(roles)) {
                throw new ServiceException(MessageUtils.message("no.permission.visit.role"));
            }
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public int countUserRoleByRoleId(Long roleId) {
        return sysUserRoleService.lambdaQuery().eq(SysUserRole::getRoleId, roleId).count();
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional
    public SysRole insertRole(SysRole role) {
        //雪花生成id
        long roleId = YitIdHelper.nextId();
        role.setRoleId(roleId);
        //当前用户id作为创建者
        role.setCreateBy(tokenService.getUserId());
        //当前时间作为创建时间
        role.setCreateTime(DateUtils.getNowDate());

        // 新增角色信息
        boolean saveRole = sysRoleService.save(role);
        if (saveRole) {
            insertRoleMenu(role);
        }
        return sysRoleService.getById(roleId);
    }

    /**
     * 修改保存角色信息
     *
     * @param sysRole 角色信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateRole(SysRole sysRole) {
        SysRole sysRoleForUpdate = sysRoleService.getById(sysRole.getRoleId());
        if (!sysRole.getPlatId().equals(sysRoleForUpdate.getPlatId()) && countUserRoleByRoleId(sysRole.getRoleId()) > 0) {
            throw new ServiceException(MessageUtils.message("role.already.allot.edit"));
        }
        // 修改角色信息
        sysRole.setUpdateTime(DateUtils.getNowDate());
        sysRole.setUpdateBy(tokenService.getUserId());
        boolean flag = sysRoleService.updateById(sysRole);
        // 删除角色与菜单关联
        if (flag && sysRole.getMenuIds()!=null) {
            sysRoleMenuService.lambdaUpdate().eq(SysRoleMenu::getRoleId, sysRole.getRoleId()).remove();
            return insertRoleMenu(sysRole);
        }
        return true;
    }

    /**
     * 修改角色状态
     *
     * @param sysRole 角色信息
     * @return 结果
     */
    @Override
    public boolean updateRoleStatus(SysRole sysRole) {
        // 修改角色信息
        sysRole.setUpdateTime(DateUtils.getNowDate());
        sysRole.setUpdateBy(tokenService.getUserId());
        return sysRoleService.updateById(sysRole);
    }

    /**
     * 修改数据权限信息
     *
     * @param sysRole 角色信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean authDataScope(SysRole sysRole) {
        // 修改角色信息
        sysRole.setUpdateTime(DateUtils.getNowDate());
        sysRole.setUpdateBy(tokenService.getUserId());
        // 删除角色与分组关联
        sysRoleGroupService.lambdaUpdate().eq(SysRoleGroup::getRoleId, sysRole.getRoleId()).remove();
        // 新增角色和分组信息（数据权限）
        return insertRoleGroup(sysRole);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public boolean insertRoleMenu(SysRole role) {
        List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
        if(role.getMenuIds()!=null){
            for (Long menuId : role.getMenuIds()) {
                SysRoleMenu rm = new SysRoleMenu();
                rm.setRoleId(role.getRoleId());
                rm.setMenuId(menuId);
                list.add(rm);
            }
        }
        if (!list.isEmpty()) {
            return sysRoleMenuService.saveOrUpdateBatchByMultiId(list);
        }
        return false;
    }

    /**
     * 新增角色分组信息(数据权限)
     *
     * @param role 角色对象
     */
    public boolean insertRoleGroup(SysRole role) {
        // 新增角色与分组（数据权限）管理
        List<SysRoleGroup> list = new ArrayList<SysRoleGroup>();
        for (Long groupId : role.getGroupIds()) {
            SysRoleGroup rd = new SysRoleGroup();
            rd.setRoleId(role.getRoleId());
            rd.setGroupId(groupId);
            list.add(rd);
        }
        if (!list.isEmpty()) {
            return sysRoleGroupService.saveOrUpdateBatchByMultiId(list);
        }
        return false;
    }

    /**
     * 删除角色信息
     *
     * @param roleId 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteRoleById(Long roleId) {
        checkRoleAllowed(new SysRole(roleId));
        if (countUserRoleByRoleId(roleId) > 0) {
            throw new ServiceException(MessageUtils.message("role.already.allot.delete"));
        }
        // 删除角色与菜单关联
        sysRoleMenuService.lambdaUpdate().eq(SysRoleMenu::getRoleId, roleId).remove();
        // 删除角色与分组关联
        sysRoleGroupService.lambdaUpdate().eq(SysRoleGroup::getRoleId, roleId).remove();
        return sysRoleService.lambdaUpdate()
                .set(SysRole::getUpdateBy, tokenService.getUserId())
                .set(SysRole::getUpdateTime, DateUtils.getNowDate())
                .set(SysRole::getDelFlag, Constants.DELETE_FLAG)
                .eq(SysRole::getRoleId, roleId).update();
    }

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    @Override
    public boolean deleteAuthUser(SysUserRole userRole) {
        return sysUserRoleService.lambdaUpdate()
                .eq(SysUserRole::getRoleId, userRole.getRoleId())
                .eq(SysUserRole::getUserId, userRole.getUserId())
                .remove();
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    public boolean deleteAuthUsers(Long roleId, Long[] userIds) {
        return sysUserRoleService.lambdaUpdate()
                .eq(SysUserRole::getRoleId, roleId)
                .in(SysUserRole::getUserId, userIds)
                .remove();
    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    @Override
    public boolean insertAuthUsers(Long roleId, Long[] userIds) {
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<SysUserRole>();
        for (Long userId : userIds) {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        return sysUserRoleService.saveOrUpdateBatchByMultiId(list);
    }

    @Override
    public boolean insertAuthUsersForTeach(String roleType, Long[] userIds, long sysGroupId, long platId) {
        SysGroup sysGroup = sysGroupService.getById(sysGroupId);
        if(sysGroup == null){
            return false;
        }
        //先看看是否有这个角色，如果有的话，直接把用户和角色进行绑定，如果没有这个类型的角色，创建一个再绑定
        String roleName = "";
        if(roleType.equals("1")){
            roleName = network.equals("WAN") ? sysGroup.getGroupName() + Constants.ROLE_NAME_MANAGER : Constants.ROLE_NAME_MANAGER;
        }
        if(roleType.equals("2")){
            roleName = network.equals("WAN") ? sysGroup.getGroupName() + Constants.ROLE_NAME_TEACHER : Constants.ROLE_NAME_TEACHER;
        }
        if(roleType.equals("3")){
            roleName = network.equals("WAN") ? sysGroup.getGroupName() + Constants.ROLE_NAME_STUDENT : Constants.ROLE_NAME_STUDENT;
        }
        SysRole sysRoleFQ = new SysRole();
        sysRoleFQ.setRoleName(roleName);
        sysRoleFQ.setPlatId(platId);
        List<SysRole> sysRoleList = getRoleByName(sysRoleFQ);
        long roleId = 0L;
        if(!sysRoleList.isEmpty()){
            //如果现在有角色，先把这个角色取消绑定
            roleMapper.removeUserPlatRole(Arrays.asList(userIds), platId);
            roleId = sysRoleList.get(0).getRoleId();
            insertAuthUsers(roleId,userIds);
        }
        //再看看这个分组是不是已经绑定了，没绑定的话把它绑定一下
        int i = sysRoleGroupService.lambdaQuery().eq(SysRoleGroup::getRoleId,roleId).eq(SysRoleGroup::getGroupId,sysGroupId).count();
        if(i==0){
            SysRoleGroup sysRoleGroup = new SysRoleGroup();
            sysRoleGroup.setRoleId(roleId);
            sysRoleGroup.setGroupId(sysGroupId);
            return sysRoleGroupService.save(sysRoleGroup);
        }else {
            return true;
        }
    }

    @Override
    public void insertAuthUsersForPlato(String roleType, Long[] userIds, long sysGroupId, long platId){
        SysGroup sysGroup = sysGroupService.getById(sysGroupId);
        if(sysGroup == null){
            throw new ServiceException(MessageUtils.message("group.not.exist"));
        }
        //根据角色类型、网络环境获取角色名称
        StringBuilder roleName = new StringBuilder();
        if(network.equals("WAN")){
            roleName.append(sysGroup.getGroupName());
        }
        switch(roleType){
            case "1":
                roleName.append(Constants.PLATO_ROLE_NAME_1);
                break;
            case "2":
                roleName.append(Constants.PLATO_ROLE_NAME_2);
                break;
            case "3":
                roleName.append(Constants.PLATO_ROLE_NAME_3);
                break;
        }
        SysRole sysRoleFQ = new SysRole();
        sysRoleFQ.setRoleName(roleName.toString());
        sysRoleFQ.setPlatId(platId);
        List<SysRole> sysRoleList = getRoleByName(sysRoleFQ);
        if(CollectionUtils.isEmpty(sysRoleList)){
            throw new ServiceException(MessageUtils.message("role.do.not.exist"));
        }

        //若用户已绑定角色，先去掉绑定
        roleMapper.removeUserPlatRole(Arrays.asList(userIds), platId);
        //将用户绑定到该角色
        Long roleId = sysRoleList.get(0).getRoleId();
        insertAuthUsers(roleId,userIds);
        //再看该角色是否已经绑定分组了，没绑的话绑一下
        int i = sysRoleGroupService.lambdaQuery().eq(SysRoleGroup::getRoleId,roleId).eq(SysRoleGroup::getGroupId,sysGroupId).count();
        if(i==0){
            SysRoleGroup sysRoleGroup = new SysRoleGroup();
            sysRoleGroup.setRoleId(roleId);
            sysRoleGroup.setGroupId(sysGroupId);
            sysRoleGroupService.save(sysRoleGroup);
        }
    }

    /**
     * @param platId
     * @return
     */
    @Override
    public boolean hasChildByPlatId(Long platId) {
        List<SysRole> menuList = sysRoleService.lambdaQuery().eq(SysRole::getPlatId, platId).list();
        return !menuList.isEmpty();
    }

    /**
     * VACS定制接口，根据角色id获取下属角色列表
     * 逻辑：如果角色管理某几个分组，那么查询这几个分组关联的所有角色列表，并且这些角色不能有超出这些分组的权限
     */
    @Override
    public List<SysRole> selectChildrenListByRoleId(Long roleId, String platId) {
        if(tokenService.getUserId() == 1L){
            return sysRoleService.lambdaQuery().eq(SysRole::getPlatId,Long.parseLong(platId)).list();
        }
        List<Long> returnRoles = new ArrayList<>();
        //第一次查询，把当前角色关联的所有分组查询到
        List<SysRoleGroup> sysRoleGroups = sysRoleGroupService.lambdaQuery().eq(SysRoleGroup::getRoleId,roleId).list();
        if(sysRoleGroups.isEmpty()){
            return new ArrayList<>();
        }
        List<Long> groupIds = new ArrayList<>();
        for(SysRoleGroup sysRoleGroup : sysRoleGroups){
            groupIds.add(sysRoleGroup.getGroupId());
        }
        //第二次查询，把所有分组关联的所有角色查询到，需要去重一下
        List<SysRoleGroup> relativeSysRoleGroups = sysRoleGroupService.lambdaQuery().in(SysRoleGroup::getGroupId,groupIds).list();
        if(relativeSysRoleGroups.isEmpty()){
            return new ArrayList<>();
        }
        //拿到角色列表应该会重复，先去一下重
        Set<Long> relativeSysRoleIds = new HashSet<>();
        for(SysRoleGroup sysRoleGroup : relativeSysRoleGroups){
            relativeSysRoleIds.add(sysRoleGroup.getRoleId());
        }

        for(Long sysRoleId : relativeSysRoleIds){
            //第三次遍历查询，把所有关联的分组角色都拿到，按照角色归纳，看看哪个角色关联的分组范围不大于当前的分组，就是目标角色
            List<SysRoleGroup> singleSysRoleGroups = sysRoleGroupService.lambdaQuery().eq(SysRoleGroup::getRoleId,sysRoleId).list();
            boolean flag = true;
            for(SysRoleGroup singleSysRoleGroup : singleSysRoleGroups){
                if(!groupIds.contains(singleSysRoleGroup.getGroupId())){
                    flag = false;
                    break;
                }
            }
            if(flag){
                returnRoles.add(sysRoleId);
            }
        }
        returnRoles.remove(roleId);
        return sysRoleService.lambdaQuery().in(SysRole::getRoleId,returnRoles).list();
    }

    /**
     * 晨会定制接口，根据角色id获取下属用户列表，并且去重
     */
    @Override
    public List<SysUser> selectUserListByRoleIds(Long[] roleIds, String platId) {
        if(roleIds == null){
            return new ArrayList();
        }
        List<SysUserRole> sysUserRoleList = sysUserRoleService.lambdaQuery().in(SysUserRole::getRoleId,roleIds).list();
        Set<Long> userIdList = new HashSet<>();
        if(!sysUserRoleList.isEmpty()){
            for(SysUserRole sysUserRole : sysUserRoleList){
                userIdList.add(sysUserRole.getUserId());
            }
        }
        Long[] userIds = userIdList.toArray(new Long[userIdList.size()]);
        if(userIds.length == 0){
            return new ArrayList();
        }
        return sysUserService.lambdaQuery().in(SysUser::getUserId,userIds).list();
    }

    /**
     * 教学平台互联网定制接口，根据角色名称获取角色列表
     */
    @Override
    public List<SysRole> getRoleByName(SysRole sysRole) {
        LambdaQueryChainWrapper<SysRole> wrapper = sysRoleService.lambdaQuery();
        if (sysRole.getRoleName() != null) {
            wrapper.eq(SysRole::getRoleName, sysRole.getRoleName());
        }
        if (sysRole.getStatus() != null) {
            wrapper.eq(SysRole::getStatus, sysRole.getStatus());
        }
        if (sysRole.getPlatId() != null) {
            wrapper.eq(SysRole::getPlatId, sysRole.getPlatId());
        }
        wrapper.ne(SysRole::getRoleId, Constants.ADMIN_ROLE_ID);
        return wrapper.orderByDesc(SysRole::getCreateTime).list();
    }

    @Override
    public List<SysRole> getRoleByKeyWord(String platId, String keyWord) {
        LambdaQueryChainWrapper<SysRole> wrapper = sysRoleService.lambdaQuery();
        if (Strings.isNotBlank(keyWord)) {
            wrapper.like(SysRole::getRoleName, keyWord);
        }
        if (Strings.isNotBlank(platId)) {
            wrapper.eq(SysRole::getPlatId, platId);
        }
        wrapper.ne(SysRole::getRoleId, Constants.ADMIN_ROLE_ID);
        return wrapper.orderByDesc(SysRole::getCreateTime).list();
    }

    /**
     * 教学平台互联网定制接口，根据角色id列表删除所有角色和其相关联的用户。
     * 注意，如果这个用户不止绑定一个角色，那么只解绑这个关系，再删除角色，不要删除用户
     */
    @Override
    public ResultBean removeRolesAndUsers(String platId, Long[] roleIds) {
        for (long roleId : roleIds){
            List<SysUserRole> sysUserRoles = sysUserRoleService.lambdaQuery().eq(SysUserRole::getRoleId,roleId).list();
            if(!sysUserRoles.isEmpty()){
                //删除关联关系
                sysUserRoleService.lambdaUpdate()
                        .eq(SysUserRole::getUserId, sysUserRoles.get(0).getUserId())
                        .eq(SysUserRole::getRoleId, roleId)
                        .remove();
                if(sysUserRoles.size()==1){
                    //删除用户
                    sysUserService.lambdaUpdate()
                            .set(SysUser::getUpdateTime, DateUtils.getNowDate())
                            .set(SysUser::getDelFlag, Constants.DELETE_FLAG)
                            .eq(SysUser::getUserId, sysUserRoles.get(0).getUserId()).update();
                }
            }
            //删除角色
            sysRoleService.lambdaUpdate()
                    .set(SysRole::getUpdateTime, DateUtils.getNowDate())
                    .set(SysRole::getDelFlag, Constants.DELETE_FLAG)
                    .eq(SysRole::getRoleId, roleId).update();
            //删除角色对应的菜单关联列表
            sysRoleMenuService.lambdaUpdate().eq(SysRoleMenu::getRoleId, roleId).remove();
        }
        return ResultBean.success();
    }
}
