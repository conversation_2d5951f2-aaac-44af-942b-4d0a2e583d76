package com.jusha.auth.system.mapper;

import com.jusha.auth.mybatisplus.entity.SysMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 菜单表 数据层
 *
 * <AUTHOR>
 */
public interface ISysMenuMapper {
    /**
     * 根据用户查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuListByUserId(SysMenu menu);

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<SysMenu> selectMenuTreeByUserId(@Param("userId") Long userId,@Param("platId") Long platId);

    /**
     * 根据角色ID查询菜单树信息
     * 
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    public List<Long> selectMenuListByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据userId查询接口路径
     * @param userId 用户id
     * @param platId 平台id
     * @return 集合
     */
    public Set<String> selectIterfacePathsByUserId(@Param("userId")Long userId, @Param("platId")Long platId);

    /**
     * @description 根据角色id查询菜单id和父级id
     * <AUTHOR>
     * @date 2025/3/7 16:29
     * @param roleId
     * @return List<Long>
     **/
    List<Long> selectMenuAndParentIdListByRoleId(@Param("roleId") Long roleId);

    /**
     * @description 根据菜单id查询子孙id
     * <AUTHOR>
     * @date 2025/7/4 15:08
     * @param menuId
     * @return List<Long>
     **/
    List<Long> selectChildIdListByMenuId(@Param("menuId") Long menuId);
}
