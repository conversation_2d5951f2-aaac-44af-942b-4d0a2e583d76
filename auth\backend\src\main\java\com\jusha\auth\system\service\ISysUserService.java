package com.jusha.auth.system.service;

import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.core.domain.model.LoginUser;
import com.jusha.auth.mybatisplus.entity.SysUser;
import com.jusha.auth.system.domain.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService {
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser user);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过电话号码查询用户
     *
     * @param phoneNumber 电话号码
     * @return 用户对象信息
     */

    public SysUser selectUserByPhoneNumber(String phoneNumber);

    /**
     * 通过工号查询用户
     *
     * @param workNumber 工号
     * @return 用户对象信息
     */

    public SysUser selectUserByWorkNumber(String workNumber);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId,Long platId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String userName);

    /**
     * 校验账户名是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(SysUser user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkPhoneUnique(SysUser user);

    /**
     * 校验工号是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkWorkNumberUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(SysUser user);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public SysUser insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean updateUser(SysUser user);

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds,Long platId);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean updateUserProfile(SysUser user);

    /**
     * 重置用户密码
     *
     * @param passwordReset 密码
     * @return 结果
     */
    public ResultBean resetUserPwd(PasswordReset passwordReset)throws Exception;

    /**
     * 验证密码
     *
     * @param passwordVerify 密码
     * @return 结果
     */
    public ResultBean verifyPwd(PasswordVerify passwordVerify)throws Exception;

    /**
     * 忘记密码
     *
     * @param forgetReset 密码
     * @return 结果
     */
    public ResultBean forgetPwd(ForgetReset forgetReset)throws Exception;


    /**
     * 修改用户手机号
     * @param modifyUserPhone 手机号码
     * @return 结果
     */
    ResultBean editMyPhone(ModifyUserPhone modifyUserPhone, LoginUser loginUser);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public boolean deleteUserById(Long userId);

    /**
     * 导入用户数据
     *
     * @param request response
     * @return 结果
     */
    ResultBean importExcel(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导出用户数据
     *
     * @param user
     * @return 结果
     */
    ModelAndView export(SysUser user);

    /**
     * 导出模板文件
     *
     * @param response
     * @return 结果
     */
    void importTemplate(HttpServletResponse response);

    /**
     * 通过用户名/工号/手机号查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public List<SysUser> selectUserByUserNamePhoneWork(String userName);

    /**
     * 首页用户数统计
     *
     * @return
     */
    public HashMap userStatistics();


    /**
     * 晨会定制接口，根据用户id列表获取所有用户列表(增加角色)
     * @param userIds
     * @param platId
     * @return
     */
    public List<SysUser> getUserListByIds(Long[] userIds,long platId);

    /**
     * 根据用户名来修改用户信息
     */
    public void updateUserByUserName(SysUser user);

    /**
     * 根据手机号来修改用户信息
     */
    public void updateUserByPhoneNumber(SysUser user);

    /**
     * 根据工号来修改用户信息
     */
    public void updateUserByWorkNumber(SysUser user);

    /**
     * 注册用户信息
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUser user);

    /**
     * 获取所有用户
     * @return 结果
     */
    List<SysUser> getAllUsers();

    /**
     * 获取这个份平台下除了自己所有用户
     * @return 结果
     */
    List<SysUser> getChatUsers(String platId);
}
