<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="dcm4cherepo" />
      <option name="name" value="dcm4che-repo" />
      <option name="url" value="https://www.dcm4che.org/maven2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://maven.aliyun.com/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun-maven" />
      <option name="name" value="aliyun maven" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo1.maven.org/maven2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="alirepo" />
      <option name="name" value="ali-repo" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
  </component>
</project>