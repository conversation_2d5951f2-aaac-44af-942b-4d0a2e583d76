package com.jusha.caselibrary.search.service;

import com.jusha.caselibrary.search.config.SearchConfig;
import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import com.jusha.caselibrary.search.dto.AdvancedSearchResponse;
import com.jusha.caselibrary.search.service.impl.AdvancedSearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 高级检索服务测试类
 * 
 * 测试内容：
 * 1. 基本搜索功能
 * 2. 缓存机制
 * 3. 参数验证
 * 4. 新增字段支持
 * 5. 性能优化功能
 * 
 * <AUTHOR>
 * @date 2025/07/09
 */
@ExtendWith(MockitoExtension.class)
class AdvancedSearchServiceTest {

    @Mock
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Mock
    private SearchConfig searchConfig;

    @Mock
    private SearchCacheService searchCacheService;

    @InjectMocks
    private AdvancedSearchServiceImpl advancedSearchService;

    private AdvancedSearchRequest testRequest;

    @BeforeEach
    void setUp() {
        testRequest = new AdvancedSearchRequest();
        testRequest.setSearchType("department");
        testRequest.setKeyword("肺癌");
        testRequest.setPageNum(1);
        testRequest.setPageSize(20);
        testRequest.setEnableCache(true);
        
        // Mock SearchConfig
        when(searchConfig.getIndexes()).thenReturn(createMockIndexes());
        when(searchConfig.getPerformance()).thenReturn(createMockPerformance());
        when(searchConfig.getQueryOptimization()).thenReturn(createMockQueryOptimization());
        when(searchConfig.getHighlight()).thenReturn(createMockHighlight());
    }

    @Test
    void testBasicSearch() {
        // Given
        when(searchCacheService.getCachedResult(any())).thenReturn(null);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
        // 注意：由于没有真实的Elasticsearch环境，这里主要测试不抛异常
    }

    @Test
    void testSearchWithCache() {
        // Given
        AdvancedSearchResponse cachedResponse = AdvancedSearchResponse.success(null, 0L, 1, 20, 100L);
        when(searchCacheService.getCachedResult(testRequest)).thenReturn(cachedResponse);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
        assertEquals(cachedResponse, response);
        verify(searchCacheService, times(1)).getCachedResult(testRequest);
    }

    @Test
    void testInvalidRequest() {
        // Given
        AdvancedSearchRequest invalidRequest = new AdvancedSearchRequest();
        // 不设置必要参数，使请求无效
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(invalidRequest);
        
        // Then
        assertNotNull(response);
        assertFalse(response.getSuccess());
    }

    @Test
    void testPersonalCaseSearch() {
        // Given
        testRequest.setSearchType("personal");
        testRequest.setUserId(123L);
        testRequest.setCaseName("测试病例");
        
        when(searchCacheService.getCachedResult(any())).thenReturn(null);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
    }

    @Test
    void testSearchWithFollowInfo() {
        // Given
        testRequest.setFollowType("电话随访");
        testRequest.setFollowupResult("病情稳定");
        testRequest.setFollowTimeStart(LocalDateTime.now().minusDays(30));
        testRequest.setFollowTimeEnd(LocalDateTime.now());
        
        when(searchCacheService.getCachedResult(any())).thenReturn(null);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
    }

    @Test
    void testSearchWithDiseaseOverview() {
        // Given
        testRequest.setDiseaseOverview("肺部疾病概述");
        testRequest.setPathology("腺癌");
        testRequest.setClinical("咳嗽、胸痛");
        testRequest.setImaging("CT显示肺部阴影");
        testRequest.setDiagnosisBasis("病理诊断");
        testRequest.setDifferential("与肺炎鉴别");
        testRequest.setKeyframe("关键帧信息");
        
        when(searchCacheService.getCachedResult(any())).thenReturn(null);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
    }

    @Test
    void testComplexQuery() {
        // Given
        testRequest.setKeyword("肺癌");
        testRequest.setPatientName("张三");
        testRequest.setDiagnosis("肺腺癌");
        testRequest.setStudyTimeStart(LocalDateTime.now().minusDays(30));
        testRequest.setStudyTimeEnd(LocalDateTime.now());
        testRequest.setPatientAgeMin(50);
        testRequest.setPatientAgeMax(70);
        testRequest.setPatientSex("男");
        
        when(searchCacheService.getCachedResult(any())).thenReturn(null);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
        assertTrue(testRequest.isComplexQuery());
    }

    @Test
    void testDeepPaginationOptimization() {
        // Given
        testRequest.setPageNum(500); // 深度分页
        testRequest.setPageSize(20);
        
        when(searchCacheService.getCachedResult(any())).thenReturn(null);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
        assertTrue(testRequest.needsDeepPaginationOptimization());
    }

    @Test
    void testSearchAfterPagination() {
        // Given
        testRequest.setUseSearchAfter(true);
        testRequest.setSearchAfter(new Object[]{"value1", "value2"});
        
        when(searchCacheService.getCachedResult(any())).thenReturn(null);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
    }

    @Test
    void testSmartOptimization() {
        // Given
        testRequest.setEnableSmartOptimization(true);
        testRequest.setKeyword("肺");
        testRequest.setPatientName("张");
        testRequest.setDiagnosis("肺癌");
        testRequest.setStudyTimeStart(LocalDateTime.now().minusDays(365));
        testRequest.setStudyTimeEnd(LocalDateTime.now());
        
        when(searchCacheService.getCachedResult(any())).thenReturn(null);
        
        // When
        AdvancedSearchResponse<?> response = advancedSearchService.search(testRequest);
        
        // Then
        assertNotNull(response);
    }

    // Mock对象创建方法
    private SearchConfig.Indexes createMockIndexes() {
        SearchConfig.Indexes indexes = new SearchConfig.Indexes();
        indexes.setDepartmentCases("department_cases");
        indexes.setPersonalCases("personal_cases");
        return indexes;
    }

    private SearchConfig.Performance createMockPerformance() {
        SearchConfig.Performance performance = new SearchConfig.Performance();
        performance.setMaxResultWindow(10000);
        performance.setSearchAfterThreshold(5000);
        performance.setQueryTimeoutMs(30000L);
        performance.setFieldWeights(new SearchConfig.Performance.FieldWeights());
        return performance;
    }

    private SearchConfig.QueryOptimization createMockQueryOptimization() {
        SearchConfig.QueryOptimization optimization = new SearchConfig.QueryOptimization();
        optimization.setEnableSmartOptimization(true);
        optimization.setMinimumShouldMatch("70%");
        optimization.setComplexQuery(new SearchConfig.QueryOptimization.ComplexQuery());
        optimization.setDeepPagination(new SearchConfig.QueryOptimization.DeepPagination());
        return optimization;
    }

    private SearchConfig.Highlight createMockHighlight() {
        SearchConfig.Highlight highlight = new SearchConfig.Highlight();
        highlight.setPreTags("<em>");
        highlight.setPostTags("</em>");
        highlight.setFragmentSize(100);
        highlight.setNumberOfFragments(3);
        return highlight;
    }
}