package com.jusha.auth.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <p>
 * 业务平台信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Getter
@Setter
@TableName("sys_plat")
@ApiModel(value = "SysPlat对象", description = "业务平台信息表")
public class SysPlat {

    @ApiModelProperty(value = "平台ID")
    @TableId("plat_id")
    private Long platId;

    @ApiModelProperty(value = "平台名称")
    @TableField("plat_name")
    @Size(message = "平台名称长度不合法",max = 20)
    private String platName;

    @ApiModelProperty(value = "平台地址")
    @TableField("plat_host")
    private String platHost;

    @ApiModelProperty(value = "平台标识")
    @TableField("plat_tag")
    @Size(message = "平台标识长度不合法",max = 20)
    private String platTag;

    @ApiModelProperty(value = "可见性（可在接口中查询到）")
    @TableField("visible")
    private String visible;

    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    @TableField("del_flag")
    @TableLogic
    private String delFlag;

    @ApiModelProperty(value = "创建者")
    @TableField("create_by")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    @TableField("update_by")
    private Long updateBy;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;
}
