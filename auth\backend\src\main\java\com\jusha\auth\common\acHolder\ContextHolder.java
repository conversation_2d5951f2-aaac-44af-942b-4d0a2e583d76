package com.jusha.auth.common.acHolder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * Spring工厂的静态持有
 */
@Slf4j
@Component
public class ContextHolder implements ApplicationContextAware {

    /**
     * ApplicationContext
     */
    private static ApplicationContext context;

    /**
     * redisTemplate
     */
    private static StringRedisTemplate redisTemplate;

    /**
     * restTemplate
     */
    private static RestTemplate restTemplate;

    /**
     * propertiesBean
     */
    private static PropertiesBean propertiesBean;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }


    /**
     * 通过name、Class获取Bean
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        T result = null;
        if(context != null){
            result = context.getBean(name, clazz);
        }
        return result;
    }

    /**
     * 通过Class获取Bean
     */
    public static <T> T getBean(Class<T> clazz) {
        T result = null;
        if(context != null){
            result = context.getBean(clazz);
        }
        return result;
    }

    /**
     * 获取 redisTemplate
     * @return
     */
    public static StringRedisTemplate stringRedisTemplate(){
        if(redisTemplate == null){
            redisTemplate = ContextHolder.getBean(StringRedisTemplate.class);
        }
        return redisTemplate;
    }

    /**
     * 获取 restTemplate
     * @return
     */
    public static RestTemplate restTemplate(){
        if(restTemplate == null){
            restTemplate = ContextHolder.getBean(RestTemplate.class);
        }
        return restTemplate;
    }

    /**
     * 获取 propertiesBean
     * @return
     */
    public static PropertiesBean propertiesBean(){
        if(propertiesBean == null){
            propertiesBean = ContextHolder.getBean(PropertiesBean.class);
        }
        return propertiesBean;
    }

}