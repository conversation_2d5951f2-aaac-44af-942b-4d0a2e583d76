package com.jusha.caselibrary.system.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName SysRole
 * @Description 角色信息类
 * <AUTHOR>
 * @Date 2024/3/7 9:43
 **/
@Data
@ApiModel(value = "SysRole对象", description = "角色信息表")
public class SysRole {
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

    @ApiModelProperty(value = "所属平台id")
    private Long platId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色状态（0正常 1停用）")
    private String status;

    @ApiModelProperty(value = "角色类型：1管理员 2老师 3学员")
    private String roleType;

    /** 菜单组 */
    @TableField(exist = false)
    private Long[] menuIds;
}
