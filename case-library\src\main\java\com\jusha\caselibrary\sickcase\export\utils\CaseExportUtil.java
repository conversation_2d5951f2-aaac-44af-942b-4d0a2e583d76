package com.jusha.caselibrary.sickcase.export.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.jusha.caselibrary.sickcase.dto.StudyInfoDto;
import com.jusha.caselibrary.common.aop.ExportField;
import com.jusha.caselibrary.sickcase.export.dto.CaseExportDataDto;
import com.jusha.caselibrary.sickcase.export.dto.ExportFieldInfo;
import com.jusha.caselibrary.sickcase.export.dto.FollowCaseExportDataDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.lang.reflect.Field;
import java.util.*;

/**
 * @ClassName CaseExportUtil
 * @Description 病例导出工具类 - 支持复杂单元格合并，使用注解动态生成表头
 * <AUTHOR>
 * @Date 2025/7/10 14:39
 **/
@Slf4j
@Component
public class CaseExportUtil {

    /**
     * 导出病例数据到Excel文件，支持复杂的单元格合并
     *
     * @param exportDataList 导出数据列表
     * @param outputPath     输出文件路径
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportToExcel(List<CaseExportDataDto> exportDataList, String outputPath) throws Exception {
        
        if (CollUtil.isEmpty(exportDataList)) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        // 创建输出目录
        File outputDir = new File(outputPath).getParentFile();
        if (!outputDir.exists()) {
            FileUtil.mkdir(outputDir);
        }

        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("病例导出数据");
            
            // 解析导出字段信息
            List<ExportFieldInfo> fieldInfoList = parseExportFields();
            
            // 创建样式
            Map<String, CellStyle> styles = createCellStyles(workbook);
            
            // 创建表头
            createHeader(sheet, fieldInfoList, styles.get("header"));
            
            // 处理数据并创建数据行
            List<CaseExportDataDto> processedDataList = processExportData(exportDataList);
            createDataRows(sheet, processedDataList, fieldInfoList, styles);
            
            // 应用单元格合并
            applyCellMerges(sheet, processedDataList, fieldInfoList);
            
            // 设置列宽
            setColumnWidths(sheet, fieldInfoList);
            
            // 写入文件
            try (FileOutputStream outputStream = new FileOutputStream(outputPath)) {
                workbook.write(outputStream);
            }

            log.info("病例数据导出完成，文件路径: {}, 导出数量: {}", outputPath, exportDataList.size());
            return outputPath;
            
        } catch (Exception e) {
            log.error("病例数据导出失败: {}", e.getMessage(), e);
            throw new Exception("病例数据导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出随访病例数据到Excel文件，支持复合数据结构（病例数据 + 统计数据）
     *
     * @param followExportData 随访导出数据
     * @param outputPath       输出文件路径
     * @return 导出文件路径
     * @throws Exception 导出异常
     */
    public String exportFollowCaseToExcel(FollowCaseExportDataDto followExportData, String outputPath) throws Exception {
        
        if (followExportData == null) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        // 创建输出目录
        File outputDir = new File(outputPath).getParentFile();
        if (!outputDir.exists()) {
            FileUtil.mkdir(outputDir);
        }

        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("随访病例导出数据");
            
            // 创建样式
            Map<String, CellStyle> styles = createCellStyles(workbook);
            
            int currentRowIndex = 0;
            
            // 第一部分：导出病例数据
            if (CollUtil.isNotEmpty(followExportData.getCaseExportDataDtoList())) {
                currentRowIndex = exportCaseDataSection(sheet, followExportData.getCaseExportDataDtoList(),
                                                      currentRowIndex, styles);
            }
            
            // 添加分隔行
            currentRowIndex += 2;
            
            // 第二部分：导出统计数据
            exportStatisticsSection(sheet, followExportData, currentRowIndex, styles);
            
            // 写入文件
            try (FileOutputStream outputStream = new FileOutputStream(outputPath)) {
                workbook.write(outputStream);
            }

            log.info("随访病例数据导出完成，文件路径: {}, 病例数量: {}, 统计字段数量: {}",
                    outputPath,
                    followExportData.getCaseExportDataDtoList() != null ? followExportData.getCaseExportDataDtoList().size() : 0,
                    followExportData.getDynamicExportFieldNames().size());
            return outputPath;
            
        } catch (Exception e) {
            log.error("随访病例数据导出失败: {}", e.getMessage(), e);
            throw new Exception("随访病例数据导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出病例数据部分
     */
    private int exportCaseDataSection(Sheet sheet, List<CaseExportDataDto> caseDataList,
                                     int startRowIndex, Map<String, CellStyle> styles) throws Exception {
        
        // 解析导出字段信息
        List<ExportFieldInfo> fieldInfoList = parseExportFields();
        
        // 创建病例数据表头
        Row headerRow = sheet.createRow(startRowIndex);
        for (ExportFieldInfo fieldInfo : fieldInfoList) {
            Cell cell = headerRow.createCell(fieldInfo.getIndex());
            cell.setCellValue(fieldInfo.getHeaderName());
            cell.setCellStyle(styles.get("header"));
        }
        startRowIndex++;
        
        // 处理病例数据并创建数据行
        List<CaseExportDataDto> processedDataList = processExportData(caseDataList);
        
        for (int i = 0; i < processedDataList.size(); i++) {
            Row row = sheet.createRow(startRowIndex + i);
            CaseExportDataDto data = processedDataList.get(i);
            
            for (ExportFieldInfo fieldInfo : fieldInfoList) {
                Cell cell = row.createCell(fieldInfo.getIndex());
                
                try {
                    Object value = fieldInfo.getField().get(data);
                    String cellValue = value != null ? value.toString() : "";
                    cell.setCellValue(cellValue);
                } catch (IllegalAccessException e) {
                    log.warn("获取字段值失败: {}", fieldInfo.getField().getName(), e);
                    cell.setCellValue("");
                }
                
                cell.setCellStyle(styles.get("content"));
            }
        }
        
        // 应用单元格合并
        applyCellMerges(sheet, processedDataList, fieldInfoList, startRowIndex - 1);
        
        // 设置列宽
        setColumnWidths(sheet, fieldInfoList);
        
        return startRowIndex + processedDataList.size();
    }

    /**
     * 导出统计数据部分
     */
    private void exportStatisticsSection(Sheet sheet, FollowCaseExportDataDto followExportData,
                                        int startRowIndex, Map<String, CellStyle> styles) {
        
        // 创建统计数据标题行
        Row titleRow = sheet.createRow(startRowIndex);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("统计信息");
        titleCell.setCellStyle(styles.get("statisticsTitle"));
        startRowIndex++;
        
        // 创建统计数据表头
        Row headerRow = sheet.createRow(startRowIndex);
        Cell fieldNameHeader = headerRow.createCell(0);
        fieldNameHeader.setCellValue("统计项目");
        fieldNameHeader.setCellStyle(styles.get("header"));
        
        Cell fieldValueHeader = headerRow.createCell(1);
        fieldValueHeader.setCellValue("统计值");
        fieldValueHeader.setCellStyle(styles.get("header"));
        startRowIndex++;
        
        // 导出动态统计字段
        Set<String> fieldNames = followExportData.getDynamicExportFieldNames();
        for (String fieldName : fieldNames) {
            Row dataRow = sheet.createRow(startRowIndex);
            
            Cell nameCell = dataRow.createCell(0);
            nameCell.setCellValue(fieldName);
            nameCell.setCellStyle(styles.get("content"));
            
            Cell valueCell = dataRow.createCell(1);
            Object value = followExportData.getDynamicExportFieldValue(fieldName);
            valueCell.setCellValue(value != null ? value.toString() : "");
            valueCell.setCellStyle(styles.get("content"));
            
            startRowIndex++;
        }
        
        // 设置统计部分的列宽
        sheet.setColumnWidth(0, 20 * 256); // 统计项目列
        sheet.setColumnWidth(1, 15 * 256); // 统计值列
    }

    /**
     * 应用单元格合并（带起始行偏移）
     */
    private void applyCellMerges(Sheet sheet, List<CaseExportDataDto> dataList,
                                List<ExportFieldInfo> fieldInfoList, int headerRowIndex) {
        int currentRow = headerRowIndex + 1; // 从表头下一行开始
        
        for (CaseExportDataDto data : dataList) {
            if (data.isFirstRowOfMerge() && data.getMergeRowCount() > 1) {
                // 对支持合并的字段进行合并
                for (ExportFieldInfo fieldInfo : fieldInfoList) {
                    if (fieldInfo.isMergeable() && "CASE".equals(fieldInfo.getMergeType())) {
                        int startRow = currentRow;
                        int endRow = currentRow + data.getMergeRowCount() - 1;
                        int col = fieldInfo.getIndex();
                        
                        // 创建合并区域
                        CellRangeAddress mergeRegion = new CellRangeAddress(startRow, endRow, col, col);
                        sheet.addMergedRegion(mergeRegion);
                        
                        log.debug("合并单元格: 行{}到{}, 列{}", startRow, endRow, col);
                    }
                }
            }
            currentRow++;
        }
        
        log.debug("单元格合并完成");
    }

    /**
     * 解析导出字段信息
     */
    private List<ExportFieldInfo> parseExportFields() {
        List<ExportFieldInfo> fieldInfoList = new ArrayList<>();
        
        Field[] fields = CaseExportDataDto.class.getDeclaredFields();
        for (Field field : fields) {
            ExportField annotation = field.getAnnotation(ExportField.class);
            if (annotation != null && annotation.exportable()) { // 只处理可导出的字段
                field.setAccessible(true);
                ExportFieldInfo fieldInfo = new ExportFieldInfo(
                    annotation.value(),
                    annotation.index(),
                    annotation.width(),
                    annotation.mergeable(),
                    annotation.mergeType(),
                    annotation.exportable(),
                    field
                );
                fieldInfoList.add(fieldInfo);
            }
        }
        
        // 按索引排序
        fieldInfoList.sort(Comparator.comparingInt(ExportFieldInfo::getIndex));
        
        log.info("解析到 {} 个可导出字段", fieldInfoList.size());
        return fieldInfoList;
    }

    /**
     * 处理导出数据，生成支持合并的行数据
     */
    private List<CaseExportDataDto> processExportData(List<CaseExportDataDto> exportDataList) {
        List<CaseExportDataDto> processedDataList = new ArrayList<>();
        
        for (CaseExportDataDto caseData : exportDataList) {
            // 处理随访结果分类
            caseData.processFollowupResults();
            caseData.processOtherFields();
            
            // 检查是否有多个检查报告
            List<StudyInfoDto> studyList = caseData.getStudyInfoDtoList();
            
            if (CollUtil.isEmpty(studyList)) {
                // 没有检查报告，创建一行基础数据
                caseData.setRowType("CASE");
                caseData.setFirstRowOfMerge(true);
                caseData.setMergeRowCount(1);
                processedDataList.add(caseData);
            } else {
                // 有检查报告，第一行包含第一个检查报告信息
                caseData.processStudyInfo();
                caseData.setRowType("CASE");
                caseData.setFirstRowOfMerge(true);
                caseData.setMergeRowCount(studyList.size());
                processedDataList.add(caseData);
                
                // 为其余检查报告创建额外的行
                for (int i = 1; i < studyList.size(); i++) {
                    CaseExportDataDto studyRow = CaseExportDataDto.createStudyRow(caseData, studyList.get(i));
                    processedDataList.add(studyRow);
                }
            }
        }
        
        log.info("处理后生成 {} 行数据", processedDataList.size());
        return processedDataList;
    }

    /**
     * 创建表头
     */
    private void createHeader(Sheet sheet, List<ExportFieldInfo> fieldInfoList, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(0);
        
        for (ExportFieldInfo fieldInfo : fieldInfoList) {
            Cell cell = headerRow.createCell(fieldInfo.getIndex());
            cell.setCellValue(fieldInfo.getHeaderName());
            cell.setCellStyle(headerStyle);
        }
        
        log.debug("创建表头完成，共 {} 列", fieldInfoList.size());
    }

    /**
     * 创建数据行
     */
    private void createDataRows(Sheet sheet, List<CaseExportDataDto> dataList, 
                               List<ExportFieldInfo> fieldInfoList, Map<String, CellStyle> styles) {
        
        CellStyle contentStyle = styles.get("content");
        
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet.createRow(i + 1); // +1 因为第0行是表头
            CaseExportDataDto data = dataList.get(i);
            
            for (ExportFieldInfo fieldInfo : fieldInfoList) {
                Cell cell = row.createCell(fieldInfo.getIndex());
                
                try {
                    Object value = fieldInfo.getField().get(data);
                    String cellValue = value != null ? value.toString() : "";
                    cell.setCellValue(cellValue);
                } catch (IllegalAccessException e) {
                    log.warn("获取字段值失败: {}", fieldInfo.getField().getName(), e);
                    cell.setCellValue("");
                }
                
                cell.setCellStyle(contentStyle);
            }
        }
        
        log.debug("创建数据行完成，共 {} 行", dataList.size());
    }

    /**
     * 应用单元格合并
     */
    private void applyCellMerges(Sheet sheet, List<CaseExportDataDto> dataList, List<ExportFieldInfo> fieldInfoList) {
        int currentRow = 1; // 从第1行开始（第0行是表头）
        
        for (CaseExportDataDto data : dataList) {
            if (data.isFirstRowOfMerge() && data.getMergeRowCount() > 1) {
                // 对支持合并的字段进行合并
                for (ExportFieldInfo fieldInfo : fieldInfoList) {
                    if (fieldInfo.isMergeable() && "CASE".equals(fieldInfo.getMergeType())) {
                        int startRow = currentRow;
                        int endRow = currentRow + data.getMergeRowCount() - 1;
                        int col = fieldInfo.getIndex();
                        
                        // 创建合并区域
                        CellRangeAddress mergeRegion = new CellRangeAddress(startRow, endRow, col, col);
                        sheet.addMergedRegion(mergeRegion);
                        
                        log.debug("合并单元格: 行{}到{}, 列{}", startRow, endRow, col);
                    }
                }
            }
            currentRow++;
        }
        
        log.debug("单元格合并完成");
    }

    /**
     * 设置列宽
     */
    private void setColumnWidths(Sheet sheet, List<ExportFieldInfo> fieldInfoList) {
        for (ExportFieldInfo fieldInfo : fieldInfoList) {
            // 将字符宽度转换为POI的宽度单位（1个字符约等于256个单位）
            int width = fieldInfo.getWidth() * 256;
            sheet.setColumnWidth(fieldInfo.getIndex(), width);
        }
        
        log.debug("设置列宽完成");
    }

    /**
     * 创建单元格样式
     */
    private Map<String, CellStyle> createCellStyles(Workbook workbook) {
        Map<String, CellStyle> styles = new HashMap<>();
        
        // 表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        
        Font headerFont = workbook.createFont();
        headerFont.setFontName("宋体");
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        
        styles.put("header", headerStyle);
        
        // 内容样式
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setAlignment(HorizontalAlignment.LEFT);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setBorderTop(BorderStyle.THIN);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setWrapText(true); // 自动换行
        
        Font contentFont = workbook.createFont();
        contentFont.setFontName("宋体");
        contentFont.setFontHeightInPoints((short) 11);
        contentStyle.setFont(contentFont);
        
        styles.put("content", contentStyle);
        
        // 统计标题样式
        CellStyle statisticsTitleStyle = workbook.createCellStyle();
        statisticsTitleStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        statisticsTitleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        statisticsTitleStyle.setAlignment(HorizontalAlignment.CENTER);
        statisticsTitleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        statisticsTitleStyle.setBorderTop(BorderStyle.THIN);
        statisticsTitleStyle.setBorderBottom(BorderStyle.THIN);
        statisticsTitleStyle.setBorderLeft(BorderStyle.THIN);
        statisticsTitleStyle.setBorderRight(BorderStyle.THIN);
        
        Font statisticsTitleFont = workbook.createFont();
        statisticsTitleFont.setFontName("宋体");
        statisticsTitleFont.setFontHeightInPoints((short) 14);
        statisticsTitleFont.setBold(true);
        statisticsTitleStyle.setFont(statisticsTitleFont);
        
        styles.put("statisticsTitle", statisticsTitleStyle);
        
        log.debug("创建单元格样式完成");
        return styles;
    }
}
