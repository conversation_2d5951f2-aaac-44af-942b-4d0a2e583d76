package com.jusha.caselibrary.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 */
@Slf4j
public class DateUtil {
    /**
     * 默认格式化时间格式
     */
    public static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String MOUDLE_PATTERN = "yyyy-MM-dd HH:mm";
    public static final String SHORT_PATTERN = "yyyy-MM-dd";
    public static final String MONTH_PATTERN = "yyyy-MM";
    public static final String YEAR_PATTERN = "yyyy";

    public static final String DEFAULT_TIME_PATTERN = "HH:mm:ss";
    public static final String HOUR_MINUTE_PATTERN = "HH:mm";
    public static final String MINUTE_PATTERN = "mm";

    public static final String DAY_PATTERN = "MM月dd日";


    /**
     * 将 日期 转为 字符串
     */
    public static String convertDateToStr(Date date, String pattern) {
        if (null == date) {
            return null;
        }
        if (StringUtils.isEmpty(pattern)) {
            pattern = DEFAULT_PATTERN;
        }
        return formatDate(date, pattern);
    }

    /**
     * 将 字符串 转为 日期
     */
    public static Date convertStrToDate(String dateStr, String pattern) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        return parseDate(dateStr, pattern);
    }

    /**
     * 获取给定日期的n年后日期
     */
    public static Date getSomeYearDate(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        cal.add(Calendar.YEAR, n);
        return cal.getTime();
    }

    /**
     * 获取给定日期的n月后的日期
     */
    public static Date getSomeMonthDate(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, n);
        return cal.getTime();
    }

    /**
     * 获取给定日期的n天后的日期
     */
    public static Date getSomeDayDate(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, n);
        return cal.getTime();
    }

    /**
     * 获取给定日期的n小时后的日期
     */
    public static Date getSomeHourDate(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR, n);
        return cal.getTime();
    }

    /**
     * 获取给定日期的n分钟后的日期
     */
    public static Date getSomeMinuteDate(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, n);
        return cal.getTime();
    }

    /**
     * 获取给定日期的n秒后的日期
     */
    public static Date getSomeSecondDate(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.SECOND, n);
        return cal.getTime();
    }

    /**
     * 获取n年后的第一天
     */
    public static Date getSomeYearFirstDate(Date date, int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.YEAR, n);

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取n年后的最后一天
     */
    public static Date getSomeYearLastDate(Date date, int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.YEAR, n + 1);
        calendar.add(Calendar.DATE, -1);

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 获取n个月后的第一天
     */
    public static Date getSomeMonthFirstDay(Date date, int n) {
        Calendar lastDate = Calendar.getInstance();
        lastDate.setTime(date);

        lastDate.set(Calendar.DATE, 1);
        lastDate.add(Calendar.MONTH, n);

        lastDate.set(Calendar.HOUR_OF_DAY, 0);
        lastDate.set(Calendar.MINUTE, 0);
        lastDate.set(Calendar.SECOND, 0);
        return new Date(lastDate.getTimeInMillis());
    }

    /**
     * 获取n个月后的最后一天
     */
    public static Date getSomeMonthLastDay(Date d, int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);

        calendar.set(Calendar.DATE, 1);
        calendar.add(Calendar.MONTH, 1 + n);
        calendar.add(Calendar.DATE, -1);

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }


    /**
     * 获取相差月数（endTime - beginTime）
     */
    public static int monthBetween(Date beginTime, Date endTime) {
        Calendar cal1 = getCalendar(beginTime);
        Calendar cal2 = getCalendar(endTime);
        return (cal2.get(Calendar.YEAR) - cal1.get(Calendar.YEAR)) * 12 + cal2.get(Calendar.MONTH) - cal1.get(Calendar.MONTH);
    }

    /**
     * 获取相差天数
     */
    public static int dayBetween(Date newDate, Date oldDate) {
        int days = 0;
        Calendar calo = Calendar.getInstance();
        Calendar caln = Calendar.getInstance();
        calo.setTime(oldDate);
        caln.setTime(newDate);
        int oday = calo.get(Calendar.DAY_OF_YEAR);
        int nyear = caln.get(Calendar.YEAR);
        int oyear = calo.get(Calendar.YEAR);
        while (nyear > oyear) {
            calo.set(Calendar.MONTH, 11);
            calo.set(Calendar.DATE, 31);
            days = days + calo.get(Calendar.DAY_OF_YEAR);
            oyear = oyear + 1;
            calo.set(Calendar.YEAR, oyear);
        }
        int nday = caln.get(Calendar.DAY_OF_YEAR);
        days = days + nday - oday;
        return days;
    }

    /**
     * 获取 相差分钟数（endTime - startTime）
     **/
    public static int minutesBetween(Date startTime, Date endTime) {
        long cha = endTime.getTime() - startTime.getTime();
        double result = new BigDecimal(cha).divide(new BigDecimal(1000 * 60), 1, BigDecimal.ROUND_DOWN).doubleValue();
        return Integer.parseInt(String.format("%.0f", result));
    }

    /**
     * 获取 相差秒数（endTime - startTime）
     **/
    public static int secondsBetween(Date startTime, Date endTime) {
        long cha = endTime.getTime() - startTime.getTime();
        double result = new BigDecimal(cha).divide(new BigDecimal(1000), 1, BigDecimal.ROUND_DOWN).doubleValue();
        return Integer.parseInt(String.format("%.0f", result));
    }


    /**
     * 设置成日的最后时间点
     */
    public static Date setDateEnd(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);

        return c.getTime();
    }

    /**
     * 设置成最后一秒
     */
    public static Date setSecondEnd(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.SECOND, 59);

        return c.getTime();
    }

    /**
     * 清掉时间的 时、分、秒
     */
    public static Date clearDate(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 清掉时间的 分、秒
     */
    public static Date clearMinute(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 清掉时间的 秒
     */
    public static Date clearSecond(Date d) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }


    /**
     * 获取 季度
     */
    public static int getQuartar(Date d) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        int month = cal.get(Calendar.MONTH) + 1;
        int quarter = 0;
        if (month % 3 == 0) {
            quarter = month / 3;
        } else {
            quarter = month / 3 + 1;
        }
        return quarter;
    }

    /**
     * 获得是周几 (1代表周日...)
     */
    public static int getDateWeekNum(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_WEEK);
    }

    /**
     * 获取时间的年份、月份、日
     */
    public static int getYear(Date d) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        return calendar.get(Calendar.YEAR);
    }

    public static int getMonth(Date d) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        return calendar.get(Calendar.MONTH) + 1;
    }

    public static int getDay(Date d) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获取日期的小时（12小时制）
     */
    public static int getHourFor12(Date d) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        return calendar.get(Calendar.HOUR);
    }

    /**
     * 获取日期的小时（24小时制）
     */
    public static int getHourFor24(Date d) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 得到日期所在月的天数
     */
    public static int getMonthDays(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 判断日期是否是当前的月份
     */
    public static boolean isCurrentMonth(Date d) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(d);
        Calendar c2 = Calendar.getInstance();

        if (c1.get(Calendar.MONTH) == c2.get(Calendar.MONTH))
            return true;

        return false;
    }

    /**
     * 判断日期是否是当天
     */
    public static boolean isCurrentDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        Calendar n = Calendar.getInstance();
        if (c.get(Calendar.YEAR) == n.get(Calendar.YEAR)
                && c.get(Calendar.MONTH) == n.get(Calendar.MONTH)
                && c.get(Calendar.DAY_OF_MONTH) == n.get(Calendar.DAY_OF_MONTH)) {
            return true;
        }
        return false;
    }


    //获取给定日期的calendar实例
    private static Calendar getCalendar(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal;
    }

    //将日期按照给定的格式转化为字符串
    private static String formatDate(Date date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    //将字符串日期按照给定的格式转化为日期
    private static Date parseDate(String dateStr, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException("DateUtil -- format date error!");
        }
    }

    //将日期按照给定的格式转化为日期
    public static Date parseDateFromDate(Date date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        String dateStr = sdf.format(date);
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException("DateUtil -- format date error!");
        }
    }

}