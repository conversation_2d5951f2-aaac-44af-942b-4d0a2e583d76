package com.jusha.caselibrary.search.controller;

import com.jusha.caselibrary.search.service.ESSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName SyncController
 * @Description 数据同步管理控制器
 * <AUTHOR>
 * @Date 2025/7/7 16:28
 **/
@Slf4j
@RestController
@RequestMapping("/open/sync")
@Api(tags = "ES数据同步管理API")
public class SyncController {

    @Autowired
    private ESSyncService esSyncService;

    /**
     * 同步单个科室病例
     */
    @PostMapping("/department/case")
    @ApiOperation(value = "同步单个科室病例单个病例", notes = "将指定科室病例数据同步到ES")
    public ResponseEntity<Map<String, Object>> syncDepartmentCase(
            @ApiParam(value = "病例ID", required = true)
            @RequestParam("caseId") Long caseId,
            @ApiParam(value = "操作类型", allowableValues = "CREATE,UPDATE,DELETE")
            @RequestParam(defaultValue = "UPDATE") String operation) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始同步科室病例，caseId: {}, operation: {}", caseId, operation);

            esSyncService.syncDepartmentCase(Collections.singletonList(caseId), operation);

            result.put("success", true);
            result.put("message", "科室病例同步成功");
            result.put("caseId", caseId);
            result.put("operation", operation);

            log.info("科室病例同步完成，caseId: {}", caseId);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("同步科室病例异常，caseId: {}", caseId, e);

            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
            result.put("caseId", caseId);

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 同步单个个人病例
     */
    @PostMapping("/personal/case/userId")
    @ApiOperation(value = "同步单个个人病例", notes = "将指定个人病例数据同步到ES")
    public ResponseEntity<Map<String, Object>> syncPersonalSingCase(
            @ApiParam(value = "病例ID", required = true)
            @RequestParam("userCaseId") Long userCaseId,
            @ApiParam(value = "操作类型", allowableValues = "CREATE,UPDATE,DELETE")
            @RequestParam(defaultValue = "UPDATE") String operation) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始同步个人病例，userCaseId: {}, operation: {}", userCaseId, operation);

            esSyncService.syncPersonalCase(Collections.singletonList(userCaseId), operation);

            result.put("success", true);
            result.put("message", "个人病例同步成功");
            result.put("userCaseId", userCaseId);
            result.put("operation", operation);

            log.info("个人病例同步完成，caseId: {}", userCaseId);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("同步个人病例异常，caseId: {}", userCaseId, e);

            result.put("success", false);
            result.put("message", "同步失败：" + e.getMessage());
            result.put("userCaseId", userCaseId);

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 批量同步科室病例
     */
    @PostMapping("/department/batch")
    @ApiOperation(value = "批量同步科室病例", notes = "批量同步多个科室病例数据到ES")
    public ResponseEntity<Map<String, Object>> batchSyncDepartmentCases(
            @ApiParam(value = "病例ID列表", required = true)
            @RequestBody List<Long> caseIds,
            @ApiParam(value = "操作类型", allowableValues = "CREATE,UPDATE,DELETE")
            @RequestParam(defaultValue = "UPDATE") String operation) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始批量同步科室病例，数量: {}, operation: {}", caseIds.size(), operation);

            esSyncService.syncDepartmentCase(caseIds, operation);

            result.put("success", true);
            result.put("message", "批量同步科室病例已提交");
            result.put("count", caseIds.size());
            result.put("operation", operation);

            log.info("批量同步科室病例提交完成，数量: {}", caseIds.size());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量同步科室病例异常", e);

            result.put("success", false);
            result.put("message", "批量同步失败：" + e.getMessage());
            result.put("count", caseIds.size());

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 批量同步个人病例
     */
    @PostMapping("/personal/batch")
    @ApiOperation(value = "批量同步个人病例", notes = "批量同步多个个人病例数据到ES")
    public ResponseEntity<Map<String, Object>> batchSyncPersonalCases(
            @ApiParam(value = "病例ID列表", required = true)
            @RequestBody List<Long> userCaseIds,
            @ApiParam(value = "操作类型", allowableValues = "CREATE,UPDATE,DELETE")
            @RequestParam(defaultValue = "UPDATE") String operation,
            @ApiParam(value = "用户ID")
            @RequestParam("userId") Long userId) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始批量同步个人病例，数量: {}, operation: {}", userCaseIds.size(), operation);

            esSyncService.syncPersonalCase(userCaseIds, operation);

            result.put("success", true);
            result.put("message", "批量同步个人病例已提交");
            result.put("count", userCaseIds.size());
            result.put("operation", operation);

            log.info("批量同步个人病例提交完成，数量: {}", userCaseIds.size());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量同步个人病例异常", e);

            result.put("success", false);
            result.put("message", "批量同步失败：" + e.getMessage());
            result.put("count", userCaseIds.size());

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 全量同步科室病例
     */
    @PostMapping("/department/full")
    @ApiOperation(value = "全量同步科室病例", notes = "全量同步所有科室病例数据到ES")
    public ResponseEntity<Map<String, Object>> fullSyncDepartmentCases() {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始全量同步科室病例");

            esSyncService.fullSyncDepartmentCases();

            result.put("success", true);
            result.put("message", "全量同步科室病例已启动");

            log.info("全量同步科室病例启动完成");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("全量同步科室病例异常", e);

            result.put("success", false);
            result.put("message", "全量同步失败：" + e.getMessage());

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 全量同步个人病例
     */
    @PostMapping("/personal/full")
    @ApiOperation(value = "全量同步个人病例", notes = "全量同步所有个人病例数据到ES")
    public ResponseEntity<Map<String, Object>> fullSyncPersonalCases(@RequestParam("userId") Long userId) {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始全量同步个人病例");

            esSyncService.fullSyncPersonalCases(userId);

            result.put("success", true);
            result.put("message", "全量同步个人病例已启动");

            log.info("全量同步个人病例启动完成");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("全量同步个人病例异常", e);

            result.put("success", false);
            result.put("message", "全量同步失败：" + e.getMessage());

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 重建ES索引
     */
    @PostMapping("/indexes/recreate")
    @ApiOperation(value = "重建ES索引", notes = "删除并重新创建ES索引")
    public ResponseEntity<Map<String, Object>> recreateIndexes() {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("开始重建ES索引");

            esSyncService.recreateIndexes();

            result.put("success", true);
            result.put("message", "ES索引重建成功");

            log.info("ES索引重建完成");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("重建ES索引异常", e);

            result.put("success", false);
            result.put("message", "重建索引失败：" + e.getMessage());

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 检查ES索引状态
     */
    @GetMapping("/indexes/status")
    @ApiOperation(value = "检查ES索引状态", notes = "检查ES索引是否存在并确保创建")
    public ResponseEntity<Map<String, Object>> checkIndexStatus() {

        Map<String, Object> result = new HashMap<>();
        try {
            log.info("检查ES索引状态");

            esSyncService.ensureIndexesExistOrCreate();

            result.put("success", true);
            result.put("message", "ES索引状态正常");

            log.info("ES索引状态检查完成");

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查ES索引状态异常", e);

            result.put("success", false);
            result.put("message", "索引状态检查失败：" + e.getMessage());

            return ResponseEntity.ok(result);
        }
    }
}
