package com.jusha.auth.common.core.domain.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.jusha.auth.mybatisplus.entity.SysUser;
import java.util.Set;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */
public class LoginUser {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @JSONField
    private Long userId;

    private Long platId;

    /**
     * 用户唯一标识
     */
    @JSONField
    private String token;

    /**
     * 登录时间
     */
    @JSONField
    private Long loginTime;

    /**
     * 过期时间
     */
    @JSONField
    private Long expireTime;

    /**
     * 登录IP地址
     */
    @JSONField
    private String ipaddr;

    /**
     * 登录地点
     */
    @JSONField
    private String loginLocation;

    /**
     * 浏览器类型
     */
    @JSONField
    private String browser;

    /**
     * 操作系统
     */
    @JSONField
    private String os;

    /**
     * 权限列表
     */
    @JSONField
    private Set<String> permissions;

    /**
     * 用户信息
     */
    @JSONField
    private SysUser sysUser;

    @JSONField
    private String network;

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public LoginUser() {
    }

    public LoginUser(SysUser sysUser, Set<String> permissions) {
        this.sysUser = sysUser;
        this.permissions = permissions;
    }

    public LoginUser(Long userId, SysUser sysUser, Set<String> permissions) {
        this.userId = userId;
        this.sysUser = sysUser;
        this.permissions = permissions;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @JSONField(serialize = false)
    public String getPassword() {
        return sysUser.getPassword();
    }

    public String getUsername() {
        return sysUser.getUserName();
    }

    /**
     * 账户是否未过期,过期无法验证
     */
    @JSONField(serialize = false)
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     *
     * @return
     */
    @JSONField(serialize = false)
    public boolean isEnabled() {
        return true;
    }

    public Long getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(Long loginTime) {
        this.loginTime = loginTime;
    }

    public String getIpaddr() {
        return ipaddr;
    }

    public void setIpaddr(String ipaddr) {
        this.ipaddr = ipaddr;
    }

    public String getLoginLocation() {
        return loginLocation;
    }

    public void setLoginLocation(String loginLocation) {
        this.loginLocation = loginLocation;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public Set<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }

    public SysUser getSysUser() {
        return sysUser;
    }

    public void setSysUser(SysUser sysuser) {
        this.sysUser = sysuser;
    }
}
