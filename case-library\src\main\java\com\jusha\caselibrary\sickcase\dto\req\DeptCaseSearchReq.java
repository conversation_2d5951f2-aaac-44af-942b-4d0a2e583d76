package com.jusha.caselibrary.sickcase.dto.req;

import com.jusha.caselibrary.common.aop.EscapeWildcard;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Value;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * @ClassName DeptCaseSearchReq
 * @Description 科室病例库列表请求实体
 * <AUTHOR>
 * @Date 2025/7/10 10:09
 **/
@Data
public class DeptCaseSearchReq {

    @ApiModelProperty(value = "病例Id", hidden = true)
    private Long caseId;

    @ApiModelProperty(value = "检查开始时间")
    private String beginTime;

    @ApiModelProperty(value = "检查结束时间")
    private String endTime;

    @ApiModelProperty(value = "病例库类型")
    @NotNull(message = "病例库类型不能为空")
    private String caseType;

    @ApiModelProperty(value = "检查类型")
    private String deviceType;

    @ApiModelProperty(value = "患者性别：男、女")
    private String patientSex;

    @ApiModelProperty(value = "随访类型：手术，超声，临床，病理")
    private String followType;

    @ApiModelProperty(value = "随访状态")
    private String followStatus;

    @ApiModelProperty(value = "关键字: 患者姓名、影像号、住院号、门诊号、" +
            "影像学表现、影像学诊断、随访结果、病例分析等")
    private String keyword;

    @ApiModelProperty(value = "第几页")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;
}
