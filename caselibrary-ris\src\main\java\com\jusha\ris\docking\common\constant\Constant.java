package com.jusha.ris.docking.common.constant;

/**
 * 常量
 */
public class Constant {

    /**
     * 线上环境的 spring.profiles.active
     */
    public static final String SPRING_PROFILES_PROD = "prod";

    /**
     * traceId
     */
    public static final String TRACEID = "traceId";

    /**
     * token的键名
     */
    public static final String TOKEN_KEY = "Authorization";

    /**
     * 用户初始密码
     */
    public final static String USER_INITIAL_PASSWORD = "123456";

    /**
     * 所有权限标识
     */
    public static final String ALL_PERMISSION = "/*/*/*";

    /**
     * 网络环境 redis key
     */
    public static final String NETWORK_ENVIRONMENT = "network_environment:";

    /**
     * 当前用户网络环境: LAN 内网, WAN 外网
     */
    public static final String NETWORK_LAN = "LAN";
    public static final String NETWORK_WAN = "WAN";

}
