package com.jusha.caselibrary.file.service.impl;

import com.alibaba.fastjson.JSON;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.github.yitter.idgen.YitIdHelper;
import com.jusha.caselibrary.common.acHolder.ContextHolder;
import com.jusha.caselibrary.common.acHolder.PropertiesBean;
import com.jusha.caselibrary.common.constant.Constant;
import com.jusha.caselibrary.common.exception.BusinessException;
import com.jusha.caselibrary.common.util.DateUtil;
import com.jusha.caselibrary.common.util.FileUtil;
import com.jusha.caselibrary.common.util.LocaleUtil;
import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.file.dto.FileReportDto;
import com.jusha.caselibrary.file.dto.PostPolicyMinio;
import com.jusha.caselibrary.file.dto.req.StreamUploadReq;
import com.jusha.caselibrary.file.mapper.FileMapper;
import com.jusha.caselibrary.file.resp.FileUploadResp;
import com.jusha.caselibrary.file.resp.SignatureBucketResponse;
import com.jusha.caselibrary.file.service.FileService;
import com.jusha.caselibrary.file.task.MinioStreamUploadTask;
import com.jusha.caselibrary.mybatisplus.entity.Resource;
import com.jusha.caselibrary.mybatisplus.service.ResourceService;
import io.minio.*;
import io.minio.errors.MinioException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

@Slf4j
@RequiredArgsConstructor
@Service("fileService_LAN")
public class FileServiceImpl implements FileService {

    private final ResourceService resourceService;
    private final FileMapper fileMapper;
    private final PropertiesBean propertiesBean;

    @Value("${MINIO_ENDPOINT:http://127.0.0.1:9000}")
    private String endpoint;

    private AmazonS3 s3 = null;

    /**
     * 桶占位符
     */
    private static final String BUCKET_PARAM = "${bucket}";
    /**
     * bucket权限-只读
     */
    private static final String READ_ONLY = "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetBucketLocation\",\"s3:ListBucket\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "\"]},{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetObject\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "/*\"]}]}";
    /**
     * bucket权限-只读
     */
    private static final String WRITE_ONLY = "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetBucketLocation\",\"s3:ListBucketMultipartUploads\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "\"]},{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:AbortMultipartUpload\",\"s3:DeleteObject\",\"s3:ListMultipartUploadParts\",\"s3:PutObject\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "/*\"]}]}";
    /**
     * bucket权限-读写
     */
    private static final String READ_WRITE = "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:GetBucketLocation\",\"s3:ListBucket\",\"s3:ListBucketMultipartUploads\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "\"]},{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:DeleteObject\",\"s3:GetObject\",\"s3:ListMultipartUploadParts\",\"s3:PutObject\",\"s3:AbortMultipartUpload\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_PARAM + "/*\"]}]}";


    @PostConstruct
    public void init() {
        AWSCredentials awsCredentials = new BasicAWSCredentials(propertiesBean.getMinioAccessKey(), propertiesBean.getMinioSecretKey());
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.getApacheHttpClientConfig()
                .withSslSocketFactory(new SSLConnectionSocketFactory(FileUtil.createBlindlyTrustingSslContext(), NoopHostnameVerifier.INSTANCE));

        s3 = AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .withClientConfiguration(clientConfiguration)
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, "us-east-1"))
                .enablePathStyleAccess()
                .build();

        createMinioClient();
        createDefaultBucket(Constant.UPLOAD_BUCKET);
    }

    private MinioClient minioClient = null;

    private void createMinioClient(){
        MinioClient.Builder builder = MinioClient.builder();
        builder.endpoint(endpoint);
        if (StringUtils.isNotBlank(propertiesBean.getMinioAccessKey()) && StringUtils.isNotBlank(propertiesBean.getMinioSecretKey())) {
            builder.credentials(propertiesBean.getMinioAccessKey(),propertiesBean.getMinioSecretKey());
        }
        minioClient = builder.build();
    }

    private void createDefaultBucket(String bucketName){
        try {
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!isExist) {
                // 新建桶
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                setBucketPolicy(bucketName,"read-write");
            }
        }catch (Exception e){
            log.error("新建存储桶失败"+e);
        }

    }

    /**
     * 更新桶权限策略
     *
     * @param bucket 桶
     * @param policy 权限
     */
    public void setBucketPolicy(String bucket, String policy){
        try {
            switch (policy) {
                case "read-only":
                    minioClient.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucket).config(READ_ONLY.replace(BUCKET_PARAM, bucket)).build());
                    break;
                case "write-only":
                    minioClient.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucket).config(WRITE_ONLY.replace(BUCKET_PARAM, bucket)).build());
                    break;
                case "read-write":
                    minioClient.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucket).config(READ_WRITE.replace(BUCKET_PARAM, bucket)).build());
                    break;
                case "none":
                default:
                    break;
            }
        }catch (Exception e){
            log.error("设置存储桶策略失败"+e);
        }
    }


    public AmazonS3 getS3(){
        return s3;
    }


    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void upload(FileUploadResp uploadResp){
        MultipartFile multipartFile = uploadResp.getMultipartFile();

        //原始文件名称(包含后缀)
        String originName = multipartFile.getOriginalFilename();
        //实际存储文件名称
        String tempName = StringUtils.join(FilenameUtils.getBaseName(originName), "_", DateUtil.convertDateToStr(new Date(), "yyyyMMddHHmmss"));
        String actualName = StringUtils.join(DigestUtils.md5Hex(tempName), ".", FilenameUtils.getExtension(originName));
        String key = StringUtils.join( "/", DateUtil.convertDateToStr(new Date(), "yyyyMM"), "/", DateUtil.convertDateToStr(new Date(), "dd"), "/", actualName);

        log.info("upload-1-originName:{}", originName);
        //上传到minio
        File file = FileUtil.multipartFileToFile(multipartFile);
        try {
            s3.putObject(propertiesBean.getMinioBucketName(), key, file);
        }
        catch (SdkClientException e) {
            log.error("upload minio SdkClientException:{}", e);
            throw new BusinessException(LocaleUtil.getLocale("file.minio.upload.fail"));
        }
        finally {
            FileUtil.delFile(file);
        }
        log.info("upload-2-minioBucketName:{} key:{}", propertiesBean.getMinioBucketName(), key);

        //入库
        Resource resource = new Resource();
        resource.setResourceId(YitIdHelper.nextId());
        resource.setOriginName(originName);
        resource.setActualName(key);
        String fileUrl = StringUtils.join("/", ContextHolder.propertiesBean().getMinioBucketName(), key);
        resource.setFileUrl(fileUrl);
        resource.setLmGpId(LoginUtil.isLogin()? LoginUtil.lmGroupId():null);
        resourceService.save(resource);
        //填充返回数据
        BeanUtils.copyProperties(resource ,uploadResp);
        uploadResp.setFullUrl(StringUtils.join(endpoint, fileUrl));
        uploadResp.setMultipartFile(null);
        log.info("upload-3-uploadResp:{}", JSON.toJSONString(uploadResp));
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public List<FileUploadResp> uploadMulti(MultipartFile[] multipartFiles){
        List<FileUploadResp> result = new ArrayList<>();

        if(multipartFiles != null && multipartFiles.length > 0){
            for(int i = 0; i < multipartFiles.length; i++){
                FileUploadResp uploadResp = new FileUploadResp();
                uploadResp.setMultipartFile(multipartFiles[i]);
                uploadResp.setSeq(i+1);

                result.add(uploadResp);
            }
        }
        result.parallelStream().forEach(uploadResp -> this.upload(uploadResp));

        return result;
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void deleteFile(Long resourceId) {
        //删除文件
        Resource resource = resourceService.getById(resourceId);
        if(resource == null) {
            return;
        }
        String fileUrl = resource.getFileUrl();
        String key = fileUrl.replace(StringUtils.join("/", propertiesBean.getMinioBucketName()), "");
        s3.deleteObject(propertiesBean.getMinioBucketName(), key);
        //删除关联数据
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void deleteFileBatch(List<Long> resourceIds){
        if(CollectionUtils.isEmpty(resourceIds)) {
            return;
        }
        //删除文件
        resourceIds.parallelStream().forEach(resourceId -> {
            Resource resource = resourceService.getById(resourceId);
            if(resource == null) {
                return;
            }
            String fileUrl = resource.getFileUrl();
            String key = fileUrl.replace(StringUtils.join("/", propertiesBean.getMinioBucketName()), "");
            s3.deleteObject(propertiesBean.getMinioBucketName(), key);
        });
        //删除关联数据
    }

    @Override
    public void streamUpload(StreamUploadReq req){
        ContextHolder.getBean("cachedPool", ExecutorService.class).submit(new MinioStreamUploadTask(req.getTaskId(), req.getFileName()));
    }

    @Override
    public Resource report(FileReportDto fileReportDto) {
        //入库
        Resource resource = new Resource();
        resource.setResourceId(YitIdHelper.nextId());
        resource.setOriginName(fileReportDto.getFileName());
        String tmpStr = StringUtils.join(ContextHolder.propertiesBean().getMinioPath(), "/", ContextHolder.propertiesBean().getMinioBucketName());
        String key = fileReportDto.getFullUrl().replace(tmpStr, "");   //文件key,对应: /202411/12/4a917608d85438eed58fa6741ec28b07.zip
        resource.setActualName(key);
        String fileUrl = fileReportDto.getFullUrl().replace(ContextHolder.propertiesBean().getMinioPath(), "");
        resource.setFileUrl(fileUrl);
        resource.setLmGpId(LoginUtil.lmGroupId());
        resourceService.save(resource);
        return resource;
    }

    @Override
    public SignatureBucketResponse getPostPolicy(String fileName, String bucket) {
        SignatureBucketResponse signatureBucketResponse = new SignatureBucketResponse();
        signatureBucketResponse.setServerType("LAN");
        PostPolicyMinio postPolicyMinio = createMinioPostPolicy(fileName,bucket);
        signatureBucketResponse.setPostPolicyMinio(postPolicyMinio);
        return signatureBucketResponse;
    }

    private PostPolicyMinio createMinioPostPolicy(String fileName, String bucket) {
        PostPolicyMinio postPolicyMinio = new PostPolicyMinio();
        // 设置凭证过期时间
        ZonedDateTime expirationDate = ZonedDateTime.now().plusMinutes(Constant.upload_expire_time);
        // 创建一个凭证
        PostPolicy policy = new PostPolicy(bucket==null?Constant.UPLOAD_BUCKET:bucket, fileName, expirationDate);
        // 限制文件大小，单位是字节byte，也就是说可以设置如：只允许10M以内的文件上传
        // policy.setContentRange(1, 10 * 1024);
        // 限制上传文件请求的ContentType
        // policy.setContentType("image/png");
        try {
            // 生成凭证并返回
            final Map<String, String> map = minioClient.presignedPostPolicy(policy);
            postPolicyMinio.setBucket(map.get("bucket"));
            postPolicyMinio.setEndpoint(endpoint);
            postPolicyMinio.setXAmzDate(map.get("x-amz-date"));
            postPolicyMinio.setXAmzSignature(map.get("x-amz-signature"));
            postPolicyMinio.setKey(map.get("key"));
            postPolicyMinio.setXAmzAlgorithm(map.get("x-amz-algorithm"));
            postPolicyMinio.setXAmzCredential(map.get("x-amz-credential"));
            postPolicyMinio.setPolicy(map.get("policy"));
        } catch (MinioException | InvalidKeyException | IOException | NoSuchAlgorithmException e) {
            log.error("===========获取minio上传凭证有误====="+e);
        }
        return postPolicyMinio;
    }
}