package com.jusha.caselibrary.search.service;

import com.jusha.caselibrary.mybatisplus.entity.DepCase;
import com.jusha.caselibrary.mybatisplus.entity.UserCase;

/**
 * @ClassName CaseManagementService
 * @Description 病例管理服务接口
 * <AUTHOR>
 * @Date 2025/7/7 17:03
 **/
public interface CaseManagementService {

    /**
     * 创建科室病例
     *
     * @param depCase 科室病例
     * @return 创建结果
     */
    Long createDepartmentCase(DepCase depCase);

    /**
     * 更新科室病例
     *
     * @param depCase 科室病例
     * @return 更新结果
     */
    Long updateDepartmentCase(DepCase depCase);

    /**
     * 删除科室病例
     *
     * @param caseId 病例ID
     * @return 删除结果
     */
    boolean deleteDepartmentCase(Long caseId);

    /**
     * 创建个人病例
     *
     * @param userCase 个人病例
     * @return 创建结果
     */
    boolean createPersonalCase(UserCase userCase);

    /**
     * 更新个人病例
     *
     * @param userCase 个人病例
     * @return 更新结果
     */
    boolean updatePersonalCase(UserCase userCase);

    /**
     * 删除个人病例
     *
     * @param caseId 病例ID
     * @return 删除结果
     */
    boolean deletePersonalCase(Long caseId);

    /**
     * 批量导入科室病例
     *
     * @param depCases 科室病例列表
     * @return 导入结果
     */
    boolean batchImportDepartmentCases(java.util.List<DepCase> depCases);

    /**
     * 批量导入个人病例
     *
     * @param userCases 个人病例列表
     * @return 导入结果
     */
    boolean batchImportPersonalCases(java.util.List<UserCase> userCases);

    /**
     * 在指定目录下创建个人病例
     *
     * @param caseId 病例ID
     * @param catalogId 目录ID
     * @return 创建结果
     */
    boolean createPersonalCaseInCatalog(Long caseId, Long catalogId);

    /**
     * 从指定目录删除个人病例
     *
     * @param caseId 病例ID
     * @param catalogId 目录ID
     * @return 删除结果
     */
    boolean deletePersonalCaseFromCatalog(Long caseId, Long catalogId);

    /**
     * 将个人病例添加到指定目录（兼容旧接口）
     *
     * @param caseId 病例ID
     * @param catalogId 目录ID
     * @return 添加结果
     */
    @Deprecated
    boolean addPersonalCaseToCatalog(Long caseId, Long catalogId);

    /**
     * 从指定目录移除个人病例（兼容旧接口）
     *
     * @param caseId 病例ID
     * @param catalogId 目录ID
     * @return 移除结果
     */
    @Deprecated
    boolean removePersonalCaseFromCatalog(Long caseId, Long catalogId);

}
