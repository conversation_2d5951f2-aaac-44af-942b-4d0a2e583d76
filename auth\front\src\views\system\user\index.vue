<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :span="24" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="所属平台" prop="platId">
            <el-select :default-first-option="false" v-model="queryParams.platId" placeholder="所属平台" @change="changeSelectedPlat">
              <el-option
                v-for="item in this.platList"
                :key="item.platId"
                :label="item.platName"
                :value="item.platId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属角色" prop="roleId">
            <el-select :default-first-option="false"
            filterable 
            clearable
            v-model="queryParams.roleId" 
            placeholder="所属角色">
              <el-option
                v-for="item in this.roleList"
                :key="item.roleId"
                :label="item.roleName"
                :value="item.roleId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="账户名称" prop="userName">
            <el-input
              maxlength="50"
              v-model="queryParams.userName"
              placeholder="请输入账户名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="用户姓名" prop="nickName">
            <el-input
              maxlength="50"
              v-model="queryParams.nickName"
              placeholder="请输入用户姓名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="phoneNumber">
            <el-input
              maxlength="50"
              v-model="queryParams.phoneNumber"
              placeholder="请输入手机号码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="工号" prop="workNumber">
            <el-input
              maxlength="50"
              v-model="queryParams.workNumber"
              placeholder="请输入工号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="用户状态"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-if="keyWords.includes('add')"
            >新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-upload2"
              size="mini"
              @click="handleImport"
              v-if="keyWords.includes('import')"
            >导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-if="keyWords.includes('export')"
            >导出</el-button>
          </el-col> -->
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="userList">
          <!-- <el-table-column type="selection" width="50" align="center" /> -->
          <el-table-column label="用户编号" align="center" key="userId" prop="userId" width="200"/>
          <el-table-column label="账户名称" align="center" key="userName" prop="userName" width="150"/>
          <el-table-column label="用户姓名" align="center" key="nickName" prop="nickName" width="150"/>
          <el-table-column label="手机号码" align="center" key="phoneNumber" prop="phoneNumber" width="200"/>
          <el-table-column label="工号" align="center" key="workNumber" prop="workNumber" width="200"/>
          <el-table-column label="状态" align="center" key="status" width="150">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-if="keyWords.includes('edit')"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-if="keyWords.includes('remove')"
              >删除</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-key"
                @click="handleResetPwd(scope.row)"
                v-if="keyWords.includes('edit')"
              >重置密码</el-button> -->
              <el-button
                size="mini"
                type="text"
                icon="el-icon-circle-check"
                @click="handleAuthRole(scope.row)"
                v-if="keyWords.includes('edit')"
              >分配角色</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框  -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属平台" prop="platId">
              <el-select :default-first-option="false" v-model="form.platId" placeholder="所属平台" @change="changeSelectedPlatDialog">
                <el-option
                  v-for="item in this.platList"
                  :key="item.platId"
                  :label="item.platName"
                  :value="item.platId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属角色" prop="roleIds">
              <el-select v-model="form.roleIds" multiple placeholder="请选择角色">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工号" prop="workNumber">
              <el-input v-model="form.workNumber" placeholder="请输入工号"/>
            </el-form-item>
          </el-col>    
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="账户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入账户名称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="账户密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入账户密码" type="password" maxlength="20" show-password/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户姓名" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入用户姓名" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import store from '../../../store'
import { listPlat} from "@/api/system/plat";
import { listRole} from "@/api/system/role";
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus } from "@/api/system/user";
import { getToken } from "@/utils/auth";
import { getKeys} from "@/api/login";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  dicts: ['sys_normal_disable'],
  components: { Treeselect },
  data() {
    return {
      keyWords : [],
      userName : [],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 默认密码
      initPassword: undefined,
      // 角色选项
      roleOptions: [],
      platList:[],
      roleList:[],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        nickName: undefined,
        phoneNumber: undefined,
        workNumber:undefined,
        status: undefined,
        platId:undefined,
        roleId:undefined
      },
      queryParamsRoles: {
        platId:undefined
      },
      queryParamsDialog: {
        platId:undefined
      },
      userId:'',
      // 表单校验
      rules: {
        platId: [
          { required: true, message: "所属平台不能为空", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "账户名称不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '账户名称长度必须介于 2 和 20 之间', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9_]{2,20}$/,
            message: "仅支持英文字母、数字及下划线",
            trigger: "blur"
          }
        ],
        nickName: [
          { required: true, message: "用户姓名不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '用户姓名长度必须介于 2 和 20 之间', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/,
            message: "仅支持中文、英文、数字及下划线",
            trigger: "blur"
          }
        ],
        password: [
          { required: true, message: "账户密码不能为空", trigger: "blur" },
          { min: 6, max: 20, message: '账户密码长度必须介于 6 和 20 之间', trigger: 'blur' }
        ],
        roleIds:[
          { required: true, message: "所属角色不能为空", trigger: "blur" }
        ],
        workNumber: [
          { min: 2, max: 20, message: '工号长度必须介于 2 和 20 之间', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9_]{2,20}$/,
            message: "仅支持英文字母、数字及下划线",
            trigger: "blur"
          }
        ],
        phoneNumber: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ],
        status : [
          { required: true, message: "所属平台不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getButton();
    this.getPlatList();
  },
  methods: {
    getButton(){
      this.keyWords = this.$route.meta.keyWords
    },
    getPlatList(){
      listPlat(this.queryParams).then(response => {
        this.platList = response.rows;
        if(response.total!=0){
          this.queryParams.platId = this.platList[0].platId
          this.findRoleList()
          this.getList();
          this.getConfigKey("sys.user.initPassword").then(response => {
          this.initPassword = response.msg;
        });
        }else{
          this.queryParams.platId = undefined
          this.getList();
          this.getConfigKey("sys.user.initPassword").then(response => {
          this.initPassword = response.msg;
        });
        }
      }); 
    },
    findRoleList(){
      this.queryParamsRoles.platId = this.queryParams.platId
      listRole(this.queryParamsRoles).then(response => {
          this.roleList = response.rows;
        })
    },
    changeSelectedPlat(){
      this.roleList = [],
      this.queryParams.roleId = undefined,
      this.findRoleList()
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗？').then(function() {
        return changeUserStatus(row.userId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        platId:undefined,
        userId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phoneNumber: undefined,
        workNumber: undefined,
        status: "0",
        roleIds: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.pageNum = null;
      this.getPlatList();
      // this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.platId = this.queryParams.platId
      listRole(this.queryParams).then(response => {
          this.roleOptions = response.rows;
          this.open = true;
          this.title = "添加用户";
          this.form.password = this.initPassword;
        })
    },
    changeSelectedPlatDialog(){
      this.roleOptions = [],
      this.form.roleIds = [],
      this.queryParamsDialog.platId = this.form.platId
      listRole(this.queryParamsDialog).then(response => {
          this.roleOptions = response.rows;
        })
      this.queryParamsDialog.platId = this.form.platId
      // if(this.userId === ''){
         
      // }else{
      //   getUser(this.userId,this.form.platId).then(response => {
      //     response.data.platId = this.form.platId
      //     this.form = response.data;
      //     this.roleOptions = response.roles;
      //     this.$set(this.form, "roleIds", response.roleIds);
      //   });
      // }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form.platId = this.queryParams.platId
      const userId = row.userId;
      this.userId = row.userId;
      const platId = this.queryParams.platId;
      getUser(userId,platId).then(response => {
        response.data.platId = this.form.platId
        this.form = response.data;
        this.roleOptions = response.roles;
        this.$set(this.form, "roleIds", response.roleIds);
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputPattern: /^.{6,20}$/,
        inputErrorMessage: "账户密码长度必须介于 6 和 20 之间"
      }).then(({ value }) => {
        const reg = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^0-9a-zA-Z]).{6,20}$/;
        if (!reg.test(value)) {
          this.$message.error('密码由6-20位字符(字母、数字、符号)组合而成');
        } else {
          getKeys().then(response => {
            let orginValue = value
            //创建加密实例
              let encrypt = new this.$jsEncrypt();
              encrypt.setPublicKey(response.data);
              value = encrypt.encrypt(value);
              resetUserPwd(row.userId, value).then(response => {
                this.$modal.msgSuccess("重置成功，新密码是" + orginValue);
              });
            });
          }
        }).catch(() => {});
    },
    /** 分配角色操作 */
    handleAuthRole: function(row) {
      // alert(this.queryParams.platId);
      const userId = row.userId;
      if(this.queryParams.platId == undefined){
              this.$message.error(`所属平台不能为空!`);
              return false;
      }
      // this.$router.push("/system/user-auth/role");
      this.$router.push({ path: '/system/user-auth/role', query: { userId: userId, platId:  this.queryParams.platId} });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.form.roleIds.length === 0){
              this.$message.error(`所属角色不能为空!`);
              return false;
            }
            if((this.form.phoneNumber == undefined || this.form.phoneNumber == '') & (this.form.workNumber == undefined || this.form.workNumber == '')){
              this.$message.error(`手机号码和工号不能同时为空!`);
              return false;
            }
            if (this.form.userId != undefined) {
              updateUser(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              getKeys().then(response => {
                //创建加密实例
                let encrypt = new this.$jsEncrypt();
                encrypt.setPublicKey(response.data);
                this.form.password = encrypt.encrypt(this.form.password);
                addUser(this.form).then(response => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                }).catch(() => {
                  this.form.password = ""
                })
              });
            }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userId = row.userId
      this.$modal.confirm('是否确认删除用户？').then(function() {
        return delUser(userId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    handleTest() {
      this.download('system/user/test', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.handleUploadSuccess(response.msg,response.data)
      this.getList();
    },
    handleUploadSuccess (message, result) {
      if (result && Array.isArray(result.detail)) {
        message += 
          `<div class="custom-alert">
            <p>${result.msg}</p>
            <p>${result.detail.join('</p><p>')}</p>
          </div>`
        this.$message({
          message,
          type: 'warning',
          dangerouslyUseHTMLString: true,
          showClose: true,
          duration: 0
        })
        return
      }
      this.$message.success(message)
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
