package com.jusha.auth.system.domain.excel;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

@Data
public class SysUserExcelImportVo {
    @Excel(name = "序号")
    private String indexNo;
    @Excel(name="所属平台名称")
    private String platName;
    @Excel(name="所属角色名称")
    private String roleNames;
    @Excel(name = "账户名称")
    private String userName;
    @Excel(name = "用户姓名")
    private String nickName;
    @Excel(name = "手机号码")
    private String phoneNumber;
    @Excel(name = "工号")
    private String workNumber;
}
