package com.jusha.auth.system.controller;

import com.jusha.auth.common.annotation.EscapeWildcard;
import com.jusha.auth.common.annotation.HasPermissions;
import com.jusha.auth.common.constant.Constants;
import com.jusha.auth.common.core.controller.BaseController;
import com.jusha.auth.common.core.domain.ResultBean;
import com.jusha.auth.common.utils.MessageUtils;
import com.jusha.auth.common.utils.match.StringUtils;
import com.jusha.auth.mybatisplus.entity.SysGroup;
import com.jusha.auth.system.service.ISysGroupService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 分组信息
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/group")
public class SysGroupController extends BaseController {
    @Autowired
    private ISysGroupService igroupService;

    @HasPermissions
    @EscapeWildcard
    @GetMapping("/list")
    public ResultBean list(SysGroup group) {
        return success(igroupService.selectGroupList(group));
    }

    /**
     * 某个平台下的分组列表
     * @return
     */
    @GetMapping("/list/in/plat/open")
    public ResultBean listInPlat() {
        Long platId = Long.valueOf(getPlatId());
        return success(igroupService.groupListInPlat(platId));
    }

    /**
     * 查询分组列表（排除节点）
     */
    @HasPermissions
    @GetMapping("/list/exclude")
    public ResultBean excludeChild(@RequestParam Long groupId,@RequestParam Long platId) {
        SysGroup sysGroup = new SysGroup();
        sysGroup.setPlatId(platId);
        List<SysGroup> groups = igroupService.selectGroupList(sysGroup);
        groups.removeIf(d -> d.getGroupId().equals(groupId) ||
                ArrayUtils.contains(StringUtils.split(d.getAncestors(), Constants.SEPARATOR), groupId + Constants.EMPTY_STRING));
        return success(groups);
    }

    /**
     * 根据分组编号获取详细信息
     */
    @HasPermissions
    @GetMapping(value = "/query")
    public ResultBean getInfo(@RequestParam Long groupId) {
        return success(igroupService.selectGroupById(groupId));
    }

    /**
     * 新增分组
     */
    @HasPermissions
    @PostMapping(value = "/add")
    public ResultBean add(@Validated @RequestBody SysGroup group) {
        //同一分组下，分组名称不可以重复
        if (!igroupService.checkGroupNameUnique(group)) {
            return error(MessageUtils.message("group.already.exist.add"));
        }
        return success(igroupService.insertGroup(group));
    }

    /**
     * 修改分组
     */
    @HasPermissions
    @PostMapping(value = "/edit")
    public ResultBean edit(@Validated @RequestBody SysGroup group) {
        return igroupService.updateGroup(group);
    }

    /**
     * 删除分组
     */
    @HasPermissions
    @PostMapping("/remove")
    public ResultBean remove(@Validated @RequestParam Long groupId) {
        if (igroupService.hasChildByGroupId(groupId)) {
            return error(MessageUtils.message("group.child.exist.delete"));
        }
        return resultBean(igroupService.deleteGroupById(groupId));
    }
}
