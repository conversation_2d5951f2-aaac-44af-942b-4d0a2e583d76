package com.jusha.caselibrary.common.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jusha.caselibrary.mybatisplus.system.entity.Disease;
import com.jusha.caselibrary.mybatisplus.system.entity.UserCatalog;
import lombok.Data;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
@Data
public class TreeSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    public TreeSelect() {

    }

    public TreeSelect(Disease disease) {
        this.id = disease.getDiseaseId();
        this.label = disease.getDiseaseName();
        this.children = disease.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(UserCatalog userCatalog) {
        this.id = userCatalog.getCatalogId();
        this.label = userCatalog.getCatalogName();
        this.children = userCatalog.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

}
