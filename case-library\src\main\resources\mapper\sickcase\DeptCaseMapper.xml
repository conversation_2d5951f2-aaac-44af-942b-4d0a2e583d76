<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jusha.caselibrary.sickcase.mapper.DeptCaseMapper">
    <resultMap id="DeptCaseDetail" type="com.jusha.caselibrary.sickcase.dto.resp.DeptCaseDetailResp">
        <id property="caseId" column="case_id"/>
        <result property="caseName" column="case_name"/>
        <result property="caseNo" column="case_no"/>
        <result property="diseaseId" column="disease_id"/>
        <result property="diagnosis" column="diagnosis"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientSex" column="patient_sex"/>
        <result property="patientBirthDate" column="patient_birth_date"/>
        <result property="patientAge" column="patient_age"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="difficulty" column="difficulty"/>
        <result property="caseCategory" column="case_category"/>
        <result property="sign" column="sign"/>
        <result property="caseAnalysis" column="case_analysis"/>
        <result property="sourceType" column="source_type"/>
        <result property="followStatus" column="follow_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="meetings" column="meetings"/>
        <result property="qualityMatch" column="quality_match"/>
        <result property="positionMatch" column="position_match"/>
        <result property="diseaseName" column="disease_name"/>
        <result property="caseType" column="case_type"/>
        <association property="diseaseOverviewInfoDto" javaType="com.jusha.caselibrary.search.document.DiseaseOverviewInfo">
            <result property="overview" column="overview"/>
            <result property="pathology" column="pathology"/>
            <result property="clinical" column="clinical"/>
            <result property="imaging" column="imaging"/>
            <result property="diagnosis" column="disease_overview_diagnosis"/>
            <result property="differential" column="differential"/>
            <result property="keyframe" column="keyframe"/>
        </association>
        <collection property="studyInfoDtoList" ofType="com.jusha.caselibrary.search.document.StudyInfo">
            <result property="id" column="study_id"/>
            <result property="studyUid" column="study_uid"/>
            <result property="accessNumber" column="access_number"/>
            <result property="studyTime" column="study_time"/>
            <result property="studyNo" column="study_no"/>
            <result property="patientId" column="study_patient_id"/>
            <result property="patientName" column="study_patient_name"/>
            <result property="patientSex" column="study_patient_sex"/>
            <result property="patientBirthDate" column="study_patient_birth_date"/>
            <result property="patientAge" column="study_patient_age"/>
            <result property="patientType" column="patient_type"/>
            <result property="visitDate" column="visit_date"/>
            <result property="outPatientNo" column="out_patient_no"/>
            <result property="inPatientNo" column="in_patient_no"/>
            <result property="physicalSign" column="physical_sign"/>
            <result property="clinicalDiagnosis" column="clinical_diagnosis"/>
            <result property="studyItemName" column="study_item_name"/>
            <result property="partName" column="part_name"/>
            <result property="deviceType" column="device_type"/>
            <result property="studyState" column="study_state"/>
            <result property="deviceName" column="device_name"/>
            <result property="medicalHistory" column="study_medical_history"/>
            <result property="selfReportedSymptom" column="self_reported_symptom"/>
            <result property="reportDescribe" column="report_describe"/>
            <result property="reportDiagnose" column="report_diagnose"/>
            <result property="isPostive" column="is_postive"/>
            <result property="registerTime" column="register_time"/>
            <result property="reportTime" column="report_time"/>
            <result property="reporter" column="reporter"/>
            <result property="checker" column="checker"/>
            <result property="checkTime" column="check_time"/>
            <result property="applyNumber" column="apply_number"/>
            <result property="applyDepartment" column="apply_department"/>
            <result property="applyDoctor" column="apply_doctor"/>
            <result property="artificer" column="artificer"/>
            <result property="isPublic" column="is_public"/>
            <!-- 使用 collection 映射 tags -->
            <collection property="modalityList" ofType="java.lang.String">
                <result column="modality"/>
            </collection>
        </collection>
        <collection property="followInfoDtoList" ofType="com.jusha.caselibrary.search.document.FollowInfo">
            <result property="followId" column="follow_id"/>
            <result property="followType" column="follow_type"/>
            <result property="followupResult" column="followup_result"/>
            <result property="createTime" column="follow_create_time"/>
        </collection>
        <!-- 使用 collection 映射 tags -->
        <collection property="tagInfoDtoList" ofType="com.jusha.caselibrary.sickcase.dto.TagInfoDto">
            <result property="tagId" column="tag_id"/>
            <result property="tagName" column="tag_name"/>
        </collection>
    </resultMap>

    <select id="getDeptCaseDetailList" resultMap="DeptCaseDetail">
        SELECT dc.case_id,
        dc.case_name,
        dc.case_no,
        dc.disease_id,
        dc.diagnosis,
        dc.patient_id,
        dc.patient_name,
        dc.patient_sex,
        dc.patient_birth_date,
        dc.patient_age,
        dc.medical_history,
        dc.difficulty,
        dc.case_category,
        dc.sign,
        dc.case_analysis,
        dc.source_type,
        dc.follow_status,
        dc.create_by,
        dc.create_time,
        dc.meetings,
        dc.quality_match,
        dc.position_match,
        d.disease_name,
        -- 病例库类型信息
        dct.case_type_name AS case_type,
        -- 病例库标签信息
        t.tag_id,
        t.tag_name,
        -- study信息
        s.id AS study_id,
        s.study_uid,
        s.access_number,
        s.study_time,
        s.study_no,
        s.patient_id AS study_patient_id,
        s.patient_name AS study_patient_name,
        s.patient_sex AS study_patient_sex,
        s.patient_birth_date AS study_patient_birth_date,
        s.patient_age AS study_patient_age,
        s.patient_type,
        s.visit_date,
        s.out_patient_no,
        s.in_patient_no,
        s.physical_sign,
        s.clinical_diagnosis,
        s.study_item_name,
        s.part_name,
        s.device_type,
        s.study_state,
        s.device_name,
        s.medical_history AS study_medical_history,
        s.self_reported_symptom,
        s.report_describe,
        s.report_diagnose,
        s.is_postive,
        s.register_time,
        s.report_time,
        s.reporter,
        s.checker,
        s.check_time,
        s.apply_number,
        s.apply_department,
        s.apply_doctor,
        s.artificer,
        s.is_public,
        -- 序列的模态信息
        cs.modality,
        -- follow信息
        f.follow_id,
        f.follow_type,
        f.followup_result,
        f.create_time AS follow_create_time,
        -- diseaseOverview信息
        do.overview,
        do.pathology,
        do.clinical,
        do.imaging,
        do.diagnosis AS disease_overview_diagnosis,
        do.differential,
        do.keyframe
        FROM t_dep_case dc
        LEFT JOIN t_disease d ON dc.disease_id = d.disease_id
        LEFT JOIN t_follow f ON dc.case_id = f.case_id
        LEFT JOIN t_disease_overview do ON dc.disease_id = do.disease_id
        LEFT JOIN t_dep_case_classify dcc ON dc.case_id = dcc.case_id
        LEFT JOIN t_dep_case_type dct ON dcc.case_type_id = dct.case_type_id
        LEFT JOIN t_dep_case_tag dctt ON dc.case_id = dctt.case_id
        LEFT JOIN t_tag t ON dctt.tag_id = t.tag_id
        LEFT JOIN t_case_study s ON dc.case_id = s.case_id
        LEFT JOIN t_case_series cs ON s.study_uid = cs.study_uid
        <where>
            1 = 1
            <if test="caseId != null">
                dc.case_id = #{caseId}
            </if>
            <if test="caseType != null and caseType != ''">
                AND dct.case_type_name = #{caseType}
            </if>
            <!-- 如果有时间区间条件，则要求该病例至少有一个报告在时间区间内 -->
            <if test="(beginTime != null and beginTime != '') or (endTime != null and endTime != '')">
                AND dc.case_id IN (
                SELECT DISTINCT cs.case_id
                FROM t_case_study cs
                WHERE 1=1
                <if test="beginTime != null and beginTime != ''">
                    AND cs.study_time >= #{beginTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    AND cs.study_time &lt;= #{endTime}
                </if>
                )
            </if>
            <if test="deviceType != null and deviceType != ''">
                AND s.device_type = #{deviceType}
            </if>
            <if test="patientSex != null and patientSex != ''">
                AND dc.patient_sex = #{patientSex}
            </if>
            <if test="followType != null and followType != ''">
                AND f.follow_type = #{followType}
            </if>
            <if test="followStatus != null and followStatus != ''">
                AND dc.follow_status = #{followStatus}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                dc.patient_name LIKE CONCAT('%', #{keyword}, '%')
                OR s.access_number LIKE CONCAT('%', #{keyword}, '%')
                OR s.in_patient_no LIKE CONCAT('%', #{keyword}, '%')
                OR s.out_patient_no LIKE CONCAT('%', #{keyword}, '%')
                OR s.report_describe LIKE CONCAT('%', #{keyword}, '%')
                OR s.report_diagnose LIKE CONCAT('%', #{keyword}, '%')
                OR f.followup_result LIKE CONCAT('%', #{keyword}, '%')
                OR dc.case_analysis LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY dc.case_id,
        -- 报告排序：区间内的按时间正序在前，区间外的按时间正序在后
        <choose>
            <when test="(beginTime != null and beginTime != '') or (endTime != null and endTime != '')">
                CASE
                WHEN 1=1
                <if test="beginTime != null and beginTime != ''">
                    AND s.study_time >= #{beginTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    AND s.study_time &lt;= #{endTime}
                </if>
                THEN 0 ELSE 1
                END,
                s.study_time ASC
            </when>
            <otherwise>
                s.study_time ASC
            </otherwise>
        </choose>
    </select>
</mapper>