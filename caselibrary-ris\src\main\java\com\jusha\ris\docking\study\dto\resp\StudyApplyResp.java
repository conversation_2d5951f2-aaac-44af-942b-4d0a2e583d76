package com.jusha.ris.docking.study.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title StudyApplyResp
 * @description
 * @date 2025/2/25
 */
@ApiModel
@Data
public class StudyApplyResp {

    @ApiModelProperty("患者id")
    private Long patientId;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("患者性别")
    private Integer patientSex;

    @ApiModelProperty("患者号")
    private String patientNo;

    @ApiModelProperty("申请单id")
    private Long applyId;

    @ApiModelProperty("申请单号")
    private String applyNo;

    @ApiModelProperty("患者类型")
    private String patientType;

    @ApiModelProperty("优先级顺序")
    private Integer prioritySeq;

    @ApiModelProperty("门诊/住院号")
    private String outInPatientNo;

    @ApiModelProperty("患者年龄")
    private String patientAge;

    @ApiModelProperty("检查类型")
    private String studyType;

    @ApiModelProperty("检查部位")
    private String studyParts;

    @ApiModelProperty("检查项目")
    private String studyItems;

    @ApiModelProperty("检查id")
    private Long studyId;

    @ApiModelProperty("检查状态 0-创建中，1-待报告，2-待审核，3-已审核，4-已退回")
    private Integer studyStatus;

    @ApiModelProperty("对接状态 0-未完成 1-已完成")
    private Integer dockStatus;

    @ApiModelProperty("是否被锁定 0-否 1-是")
    private Integer locked;

    @ApiModelProperty("诊断号--系统生成")
    private String diagnosisNo;

    @ApiModelProperty("检查号")
    private String studyNo;

    @ApiModelProperty("检查设备")
    private String studyDevice;

    @ApiModelProperty("影像号")
    private String accessNumber;

    @ApiModelProperty("影像uid")
    private String studyUid;

    @ApiModelProperty("检查时间")
    private String studyTime;

    @ApiModelProperty("上传医院")
    private String uploadHospital;

    @ApiModelProperty("分组id")
    private Long groupId;

    @ApiModelProperty("上传时间")
    private String uploadTime;

    @ApiModelProperty("报告医生")
    private String reportDoctor;

    @ApiModelProperty("报告时间")
    private String reportTime;

    @ApiModelProperty("审核医生")
    private String checkDoctor;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String checkTime;

    @ApiModelProperty("该用户是否有排班 0-无 1-有")
    private Integer isSchedule;

    @ApiModelProperty("有无该检查报告资质 0-无 1-有")
    private Integer reportPower;

    @ApiModelProperty("有无该检查审核资质 0-无 1-有")
    private Integer checkPower;

}
