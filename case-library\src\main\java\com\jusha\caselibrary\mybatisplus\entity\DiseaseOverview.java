package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 疾病概述表
 * <AUTHOR>
 */
@TableName("t_disease_overview")
@Data
public class DiseaseOverview {

    @ApiModelProperty(value = "疾病id")
    @TableId("disease_id")
    private Long diseaseId;    

    @ApiModelProperty(value = "概述")
    @TableField("overview")
    private String overview;    

    @ApiModelProperty(value = "病理表现")
    @TableField("pathology")
    private String pathology;    

    @ApiModelProperty(value = "临床表现")
    @TableField("clinical")
    private String clinical;    

    @ApiModelProperty(value = "影像学表现")
    @TableField("imaging")
    private String imaging;    

    @ApiModelProperty(value = "诊断要点")
    @TableField("diagnosis")
    private String diagnosis;    

    @ApiModelProperty(value = "鉴别诊断")
    @TableField("differential")
    private String differential;    

    @ApiModelProperty(value = "关键帧")
    @TableField("keyframe")
    private String keyframe;    

    @ApiModelProperty(value = "创建人")
    @TableField("created_by")
    private Long createdBy;    

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;    

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private Long updateBy;    

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;    

}
