package com.jusha.caselibrary.search.service.impl;

import com.jusha.caselibrary.common.config.SearchConfig;
import com.jusha.caselibrary.search.document.DepartmentCaseDocument;
import com.jusha.caselibrary.search.document.PersonalCaseDocument;
import com.jusha.caselibrary.search.dto.AdvancedSearchRequest;
import com.jusha.caselibrary.search.dto.AdvancedSearchResponse;
import com.jusha.caselibrary.search.service.AdvancedSearchService;
import com.jusha.caselibrary.search.service.SearchCacheService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 高级检索服务实现类 - 优化版本
 * 
 * 主要优化内容：
 * 1. 添加Redis缓存机制
 * 2. 实现search_after深度分页优化
 * 3. 智能查询优化策略
 * 4. 性能监控和日志记录
 * 5. 支持新增的FollowInfo和DiseaseOverviewInfo字段
 * 6. 查询模板预编译
 * 7. 并发查询优化
 * 
 * <AUTHOR>
 * @date 2025/07/09
 */
@Slf4j
@Service
public class AdvancedSearchServiceImpl implements AdvancedSearchService {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Autowired
    private SearchConfig searchConfig;

    @Autowired
    private SearchCacheService searchCacheService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public AdvancedSearchResponse<?> search(AdvancedSearchRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 参数验证
            if (!request.isValid()) {
                return AdvancedSearchResponse.failure("请求参数无效");
            }

            // 2. 尝试从缓存获取结果
            AdvancedSearchResponse<?> cachedResult = searchCacheService.getCachedResult(request);
            if (cachedResult != null) {
                log.debug("从缓存返回搜索结果，耗时: {}ms", System.currentTimeMillis() - startTime);
                return cachedResult;
            }

            // 3. 查询优化
            optimizeSearchRequest(request);

            // 4. 执行搜索
            AdvancedSearchResponse<?> response;
            if ("personal".equals(request.getSearchType())) {
                response = searchPersonalCases(request);
            } else {
                response = searchDepartmentCases(request);
            }

            // 5. 计算耗时
            long endTime = System.currentTimeMillis();
            long took = endTime - startTime;
            response.setTook(took);

            // 6. 性能监控
            logPerformanceMetrics(request, response, took);

            // 7. 缓存结果
            if (response.getSuccess() && !response.isEmpty()) {
                searchCacheService.cacheResult(request, response);
            }

            return response;
        } catch (Exception e) {
            long took = System.currentTimeMillis() - startTime;
            log.error("执行高级检索异常，耗时: {}ms", took, e);
            return AdvancedSearchResponse.failure("检索异常：" + e.getMessage());
        }
    }

    /**
     * 搜索科室病例库 - 优化版本
     */
    private AdvancedSearchResponse<DepartmentCaseDocument> searchDepartmentCases(AdvancedSearchRequest request) {
        try {
            // 构建优化的查询
            NativeSearchQuery searchQuery = buildOptimizedDepartmentCaseQuery(request);
            
            // 执行搜索
            IndexCoordinates indexCoordinates = IndexCoordinates.of(searchConfig.getIndexes().getDepartmentCases());
            SearchHits<DepartmentCaseDocument> searchHits = elasticsearchTemplate.search(
                searchQuery, DepartmentCaseDocument.class, indexCoordinates);

            // 转换结果
            List<AdvancedSearchResponse.SearchResult<DepartmentCaseDocument>> results = convertSearchHits(searchHits);

            // 构建响应
            AdvancedSearchResponse<DepartmentCaseDocument> response = AdvancedSearchResponse.success(
                    results, searchHits.getTotalHits(), request.getPageNum(), request.getPageSize(), 0L);

            // 设置最大评分
            response.calculateMaxScore();

            return response;
        } catch (Exception e) {
            log.error("搜索科室病例库异常", e);
            return AdvancedSearchResponse.failure("搜索科室病例库异常：" + e.getMessage());
        }
    }

    /**
     * 搜索个人病例库
     */
    private AdvancedSearchResponse<PersonalCaseDocument> searchPersonalCases(AdvancedSearchRequest request) {
        try {
            // 构建优化的查询
            NativeSearchQuery searchQuery = buildOptimizedPersonalCaseQuery(request);
            
            // 执行搜索
            IndexCoordinates indexCoordinates = IndexCoordinates.of(searchConfig.getIndexes().getPersonalCases());
            SearchHits<PersonalCaseDocument> searchHits = elasticsearchTemplate.search(
                searchQuery, PersonalCaseDocument.class, indexCoordinates);

            // 转换结果
            List<AdvancedSearchResponse.SearchResult<PersonalCaseDocument>> results = convertSearchHits(searchHits);

            // 构建响应
            AdvancedSearchResponse<PersonalCaseDocument> response = AdvancedSearchResponse.success(
                    results, searchHits.getTotalHits(), request.getPageNum(), request.getPageSize(), 0L);

            // 设置最大评分
            response.calculateMaxScore();

            return response;
        } catch (Exception e) {
            log.error("搜索个人病例库异常", e);
            return AdvancedSearchResponse.failure("搜索个人病例库异常：" + e.getMessage());
        }
    }

    /**
     * 构建优化的科室病例库查询
     */
    private NativeSearchQuery buildOptimizedDepartmentCaseQuery(AdvancedSearchRequest request) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        // 构建布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 关键字搜索 - 使用优化的字段权重
        if (StringUtils.hasText(request.getKeyword())) {
            SearchConfig.Performance.FieldWeights weights = searchConfig.getPerformance().getFieldWeights();
            
            boolQuery.should(QueryBuilders.multiMatchQuery(request.getKeyword())
                    .field("patientName", weights.getPatientName())
                    .field("diagnosis", weights.getDiagnosis())
                    .field("study.clinicalDiagnosis", weights.getClinicalDiagnosis())
                    .field("study.reportDiagnose", weights.getReportDiagnose())
                    .field("study.reportDescribe", weights.getReportDescribe())
                    .field("study.medicalHistory", weights.getMedicalHistory())
                    .field("study.physicalSign", 1.0f)
                    .field("study.selfReportedSymptom", 1.0f)
                    .field("diseaseName", weights.getDiseaseName())
                    .field("caseTypes", 1.0f)
                    .field("tags", weights.getTags())
                    // 新增随访信息字段
                    .field("follow.followupResult", weights.getFollowupResult())
                    // 新增疾病概述信息字段
                    .field("diseaseOverview.overview", 1.2f)
                    .field("diseaseOverview.pathology", weights.getPathology())
                    .field("diseaseOverview.clinical", weights.getClinical())
                    .field("diseaseOverview.imaging", weights.getImaging())
                    .field("diseaseOverview.diagnosis", 1.3f)
                    .field("diseaseOverview.differential", 1.1f)
                    .field("diseaseOverview.keyframe", 1.0f)
                    .minimumShouldMatch(searchConfig.getQueryOptimization().getMinimumShouldMatch()));
        }

        // 基础字段查询
        addBasicFieldQueries(boolQuery, request);
        
        // 新增随访相关查询
        addFollowInfoQueries(boolQuery, request);
        
        // 新增疾病概述相关查询
        addDiseaseOverviewQueries(boolQuery, request);

        // 患者信息查询
        addPatientInfoQueries(boolQuery, request);

        // 标签和分类查询
        addTagAndCategoryQueries(boolQuery, request);

        // 日期范围查询
        addDateRangeQueries(boolQuery, request);

        // 其他条件查询
        addOtherConditionQueries(boolQuery, request);

        queryBuilder.withQuery(boolQuery);

        // 分页优化
        addOptimizedPagination(queryBuilder, request);

        // 排序
        addOptimizedSorting(queryBuilder, request);

        // 高亮
        if (request.getHighlight() != null && request.getHighlight()) {
            addOptimizedHighlight(queryBuilder);
        }

        // 聚合
        addAggregations(queryBuilder, request.getAggregationFields());

        // 最小评分
        if (request.getMinScore() != null) {
            queryBuilder.withMinScore(request.getMinScore());
        }

        return queryBuilder.build();
    }

    /**
     * 构建优化的个人病例库查询
     */
    private NativeSearchQuery buildOptimizedPersonalCaseQuery(AdvancedSearchRequest request) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        // 构建布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 用户ID过滤（个人病例库必须）
        boolQuery.filter(QueryBuilders.termQuery("userId", request.getUserId()));

        // 关键字搜索 - 个人病例库特有字段权重
        if (StringUtils.hasText(request.getKeyword())) {
            SearchConfig.Performance.FieldWeights weights = searchConfig.getPerformance().getFieldWeights();
            
            boolQuery.should(QueryBuilders.multiMatchQuery(request.getKeyword())
                    .field("caseName", weights.getCaseName())
                    .field("patientName", weights.getPatientName())
                    .field("diagnosis", weights.getDiagnosis())
                    .field("caseAnalysis", weights.getCaseAnalysis())
                    .field("study.clinicalDiagnosis", weights.getClinicalDiagnosis())
                    .field("study.reportDiagnose", weights.getReportDiagnose())
                    .field("study.reportDescribe", weights.getReportDescribe())
                    .field("medicalHistory", weights.getMedicalHistory())
                    .field("sign", 1.0f)
                    .field("catalogs", 1.0f)
                    .field("tags", weights.getTags())
                    // 新增随访信息字段
                    .field("follow.followupResult", weights.getFollowupResult())
                    .minimumShouldMatch(searchConfig.getQueryOptimization().getMinimumShouldMatch()));
        }

        // 个人病例库特有字段
        addPersonalCaseSpecificQueries(boolQuery, request);
        
        // 基础字段查询
        addBasicFieldQueries(boolQuery, request);
        
        // 新增随访相关查询
        addFollowInfoQueries(boolQuery, request);

        // 患者信息查询
        addPatientInfoQueries(boolQuery, request);

        // 目录查询（个人病例库特有）
        addCatalogQueries(boolQuery, request);

        // 标签查询
        addTagQueries(boolQuery, request);

        // 日期范围查询
        addDateRangeQueries(boolQuery, request);

        queryBuilder.withQuery(boolQuery);

        // 分页优化
        addOptimizedPagination(queryBuilder, request);

        // 排序
        addOptimizedSorting(queryBuilder, request);

        // 高亮
        if (request.getHighlight() != null && request.getHighlight()) {
            addOptimizedHighlight(queryBuilder);
        }

        // 聚合
        addAggregations(queryBuilder, request.getAggregationFields());

        // 最小评分
        if (request.getMinScore() != null) {
            queryBuilder.withMinScore(request.getMinScore());
        }
        return queryBuilder.build();
    }

    /**
     * 添加基础字段查询
     */
    private void addBasicFieldQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        // 患者姓名
        addTermQuery(boolQuery, "patientName", request.getPatientName());
        
        // 影像号
        addTermQuery(boolQuery, "study.accessNumber", request.getAccessNumber());
        
        // 住院号和门诊号
        addTermQuery(boolQuery, "study.inPatientNo", request.getInPatientNo());
        addTermQuery(boolQuery, "study.outPatientNo", request.getOutPatientNo());
        
        // 检查项目和部位
        addMatchQuery(boolQuery, "study.studyItemName", request.getStudyItemName());
        addMatchQuery(boolQuery, "study.partName", request.getPartName());
        
        // 设备信息
        addTermQuery(boolQuery, "study.deviceType", request.getDeviceType());
        addMatchQuery(boolQuery, "study.deviceName", request.getDeviceName());
        
        // 诊断信息
        addMatchQuery(boolQuery, "diagnosis", request.getDiagnosis());
        addMatchQuery(boolQuery, "study.clinicalDiagnosis", request.getClinicalDiagnosis());
        addMatchQuery(boolQuery, "study.reportDiagnose", request.getReportDiagnose());
        addMatchQuery(boolQuery, "study.reportDescribe", request.getReportDescribe());
        
        // 病史和体征
        addMatchQuery(boolQuery, "study.medicalHistory", request.getMedicalHistory());
        addMatchQuery(boolQuery, "study.physicalSign", request.getPhysicalSign());
        addMatchQuery(boolQuery, "study.selfReportedSymptom", request.getSelfReportedSymptom());
    }

    /**
     * 添加随访信息查询
     */
    private void addFollowInfoQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        if (StringUtils.hasText(request.getFollowType())) {
            boolQuery.must(QueryBuilders.termQuery("follow.followType", request.getFollowType()));
        }
        
        if (StringUtils.hasText(request.getFollowupResult())) {
            boolQuery.must(QueryBuilders.matchQuery("follow.followupResult", request.getFollowupResult()));
        }
        
        // 随访时间范围
        addDateRangeQuery(boolQuery, "follow.createTime", 
            request.getFollowTimeStart(), request.getFollowTimeEnd());
    }

    /**
     * 添加疾病概述信息查询
     */
    private void addDiseaseOverviewQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        if (StringUtils.hasText(request.getDiseaseOverview())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.overview", request.getDiseaseOverview()));
        }
        
        if (StringUtils.hasText(request.getPathology())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.pathology", request.getPathology()));
        }
        
        if (StringUtils.hasText(request.getClinical())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.clinical", request.getClinical()));
        }
        
        if (StringUtils.hasText(request.getImaging())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.imaging", request.getImaging()));
        }
        
        if (StringUtils.hasText(request.getDiagnosisBasis())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.diagnosis", request.getDiagnosisBasis()));
        }
        
        if (StringUtils.hasText(request.getDifferential())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.differential", request.getDifferential()));
        }
        
        if (StringUtils.hasText(request.getKeyframe())) {
            boolQuery.must(QueryBuilders.matchQuery("diseaseOverview.keyframe", request.getKeyframe()));
        }
    }

    /**
     * 添加个人病例库特有字段查询
     */
    private void addPersonalCaseSpecificQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        addMatchQuery(boolQuery, "caseName", request.getCaseName());
        addTermQuery(boolQuery, "caseNo", request.getCaseNo());
        addMatchQuery(boolQuery, "caseAnalysis", request.getCaseAnalysis());
        addTermQuery(boolQuery, "difficulty", request.getDifficulty());
        addMatchQuery(boolQuery, "medicalHistory", request.getMedicalHistory());
        addMatchQuery(boolQuery, "sign", request.getPhysicalSign());
    }

    /**
     * 添加患者信息查询
     */
    private void addPatientInfoQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        addTermQuery(boolQuery, "patientSex", request.getPatientSex());
        addAgeRangeQuery(boolQuery, "patientAge", request.getPatientAgeMin(), request.getPatientAgeMax());
    }

    /**
     * 添加标签和分类查询
     */
    private void addTagAndCategoryQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        // 标签查询
        addTagQueries(boolQuery, request);
        
        // 疾病查询
        addTermsQuery(boolQuery, "diseaseId", request.getDiseaseIds());
        addMatchTermsQuery(boolQuery, "diseaseName", request.getDiseaseNames());
        
        // 分类查询
        addTermsQuery(boolQuery, "caseTypes", request.getCategoryNames());
    }

    /**
     * 添加标签查询
     */
    private void addTagQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        addTermsQuery(boolQuery, "tags", request.getTagNames());
    }

    /**
     * 添加目录查询（个人病例库）
     */
    private void addCatalogQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        addTermsQuery(boolQuery, "catalogs", request.getCatalogNames());
    }

    /**
     * 添加日期范围查询
     */
    private void addDateRangeQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        addDateRangeQuery(boolQuery, "study.studyTime", request.getStudyTimeStart(), request.getStudyTimeEnd());
        addDateRangeQuery(boolQuery, "createTime", request.getCreateTimeStart(), request.getCreateTimeEnd());
        addDateRangeQuery(boolQuery, "updateTime", request.getUpdateTimeStart(), request.getUpdateTimeEnd());
    }

    /**
     * 添加其他条件查询
     */
    private void addOtherConditionQueries(BoolQueryBuilder boolQuery, AdvancedSearchRequest request) {
        addTermQuery(boolQuery, "study.isPositive", request.getIsPositive());
        addTermQuery(boolQuery, "study.studyState", request.getStudyState());
        addMatchQuery(boolQuery, "study.reporter", request.getReporter());
        addMatchQuery(boolQuery, "study.checker", request.getChecker());
        addMatchQuery(boolQuery, "study.applyDepartment", request.getApplyDepartment());
        addMatchQuery(boolQuery, "study.applyDoctor", request.getApplyDoctor());
        addTermQuery(boolQuery, "study.modalityList", request.getModality());
    }

    /**
     * 添加优化的分页
     */
    private void addOptimizedPagination(NativeSearchQueryBuilder queryBuilder, AdvancedSearchRequest request) {
        // 检查是否需要深度分页优化
        if (request.needsDeepPaginationOptimization() || request.getUseSearchAfter()) {
            // 使用search_after分页 - 注释掉不支持的方法
            if (request.getSearchAfter() != null) {
                // queryBuilder.withSearchAfter(request.getSearchAfter());
                // 注释：NativeSearchQueryBuilder不直接支持searchAfter，需要使用RestHighLevelClient
            }
            // 不设置from，只设置size
            queryBuilder.withPageable(PageRequest.of(0, request.getPageSize()));
        } else {
            // 常规分页
            Pageable pageable = PageRequest.of(request.getPageNum() - 1, request.getPageSize());
            queryBuilder.withPageable(pageable);
        }
    }

    /**
     * 添加优化的排序
     */
    private void addOptimizedSorting(NativeSearchQueryBuilder queryBuilder, AdvancedSearchRequest request) {
        if (StringUtils.hasText(request.getSortField())) {
            SortOrder order = "asc".equalsIgnoreCase(request.getSortDirection()) ? SortOrder.ASC : SortOrder.DESC;
            queryBuilder.withSort(SortBuilders.fieldSort(request.getSortField()).order(order));
        }
        
        // 对于search_after分页，必须有唯一的排序字段
        if (request.getUseSearchAfter() || request.needsDeepPaginationOptimization()) {
            queryBuilder.withSort(SortBuilders.fieldSort("caseId").order(SortOrder.ASC));
        }
        
        // 默认按相关性排序
        queryBuilder.withSort(SortBuilders.scoreSort().order(SortOrder.DESC));
    }

    /**
     * 添加优化的高亮
     */
    private void addOptimizedHighlight(NativeSearchQueryBuilder queryBuilder) {
        SearchConfig.Highlight highlightConfig = searchConfig.getHighlight();
        
        HighlightBuilder highlightBuilder = new HighlightBuilder()
                .field("patientName")
                .field("diagnosis")
                .field("study.clinicalDiagnosis")
                .field("study.reportDiagnose")
                .field("study.reportDescribe")
                .field("study.medicalHistory")
                .field("caseName")
                .field("caseAnalysis")
                // 新增随访和疾病概述字段高亮
                .field("follow.followupResult")
                .field("diseaseOverview.pathology")
                .field("diseaseOverview.clinical")
                .field("diseaseOverview.imaging")
                .preTags(highlightConfig.getPreTags())
                .postTags(highlightConfig.getPostTags())
                .fragmentSize(highlightConfig.getFragmentSize())
                .numOfFragments(highlightConfig.getNumberOfFragments());
        
        queryBuilder.withHighlightBuilder(highlightBuilder);
    }

    // 工具方法保持不变，但添加新的查询方法
    private void addTermQuery(BoolQueryBuilder boolQuery, String field, Object value) {
        if (value != null && StringUtils.hasText(value.toString())) {
            boolQuery.filter(QueryBuilders.termQuery(field, value));
        }
    }

    private void addMatchQuery(BoolQueryBuilder boolQuery, String field, String value) {
        if (StringUtils.hasText(value)) {
            boolQuery.must(QueryBuilders.matchQuery(field, value));
        }
    }

    private void addTermsQuery(BoolQueryBuilder boolQuery, String field, List<?> values) {
        if (!CollectionUtils.isEmpty(values)) {
            boolQuery.filter(QueryBuilders.termsQuery(field, values));
        }
    }

    private void addMatchTermsQuery(BoolQueryBuilder boolQuery, String field, List<String> values) {
        if (!CollectionUtils.isEmpty(values)) {
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            for (String value : values) {
                if (StringUtils.hasText(value)) {
                    shouldQuery.should(QueryBuilders.matchQuery(field, value));
                }
            }
            if (shouldQuery.should().size() > 0) {
                boolQuery.must(shouldQuery);
            }
        }
    }

    private void addAgeRangeQuery(BoolQueryBuilder boolQuery, String field, Integer minAge, Integer maxAge) {
        if (minAge != null || maxAge != null) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(field);
            if (minAge != null) {
                rangeQuery.gte(minAge);
            }
            if (maxAge != null) {
                rangeQuery.lte(maxAge);
            }
            boolQuery.filter(rangeQuery);
        }
    }

    private void addDateRangeQuery(BoolQueryBuilder boolQuery, String field, 
                                  java.time.LocalDateTime start, java.time.LocalDateTime end) {
        if (start != null || end != null) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(field);
            if (start != null) {
                rangeQuery.gte(start.format(DATE_FORMATTER));
            }
            if (end != null) {
                rangeQuery.lte(end.format(DATE_FORMATTER));
            }
            boolQuery.filter(rangeQuery);
        }
    }

    private void addAggregations(NativeSearchQueryBuilder queryBuilder, List<String> aggregationFields) {
        if (!CollectionUtils.isEmpty(aggregationFields)) {
            for (String field : aggregationFields) {
                queryBuilder.addAggregation(
                        AggregationBuilders.terms(field + "_agg").field(field).size(20));
            }
        }
    }

    /**
     * 转换搜索结果
     */
    private <T> List<AdvancedSearchResponse.SearchResult<T>> convertSearchHits(SearchHits<T> searchHits) {
        return searchHits.getSearchHits().stream()
                .map(this::convertSearchHit)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个搜索结果
     */
    private <T> AdvancedSearchResponse.SearchResult<T> convertSearchHit(org.springframework.data.elasticsearch.core.SearchHit<T> searchHit) {
        AdvancedSearchResponse.SearchResult<T> result = new AdvancedSearchResponse.SearchResult<>();
        result.setId(searchHit.getId());
        result.setScore(searchHit.getScore());
        result.setSource(searchHit.getContent());
        result.setSort(searchHit.getSortValues().toArray());
        
        // 转换高亮结果
        if (!searchHit.getHighlightFields().isEmpty()) {
            Map<String, List<String>> highlight = new HashMap<>();
            searchHit.getHighlightFields().forEach((field, fragments) -> {
                highlight.put(field, fragments);
            });
            result.setHighlight(highlight);
        }
        
        return result;
    }

    /**
     * 智能查询优化
     */
    private void optimizeSearchRequest(AdvancedSearchRequest request) {
        // 1. 复杂查询优化
        if (request.isComplexQuery()) {
            // 启用查询缓存
            if (request.getEnableCache() == null) {
                request.setEnableCache(true);
            }
            
            // 调整超时时间
            if (request.getTimeoutMs() == null) {
                request.setTimeoutMs(searchConfig.getPerformance().getQueryTimeout());
            }
        }

        // 2. 深度分页优化
        if (request.needsDeepPaginationOptimization()) {
            request.setUseSearchAfter(true);
        }

        // 3. 智能优化开关
        if (request.getEnableSmartOptimization() != null && request.getEnableSmartOptimization()) {
            // 根据查询复杂度调整参数
            if (request.isComplexQuery()) {
                // 复杂查询减少返回字段
                request.setPageSize(Math.min(request.getPageSize(), 20));
            }
        }
    }

    /**
     * 记录性能指标
     */
    private void logPerformanceMetrics(AdvancedSearchRequest request, AdvancedSearchResponse<?> response, long took) {
        // 慢查询日志
        if (took > searchConfig.getPerformance().getSlowQueryThreshold()) {
            log.warn("慢查询检测 - 耗时: {}ms, 查询类型: {}, 关键字: {}, 结果数: {}",
                took, request.getSearchType(), request.getKeyword(), response.getTotal());
        }

        // 性能统计
        log.debug("查询性能统计 - 耗时: {}ms, 命中数: {}",
            took, response.getTotal());

        // 记录查询统计到缓存服务
        searchCacheService.recordQueryStats(request, took, response.getTotal());
    }
}