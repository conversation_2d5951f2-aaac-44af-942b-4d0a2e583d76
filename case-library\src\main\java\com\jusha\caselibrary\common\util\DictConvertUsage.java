/**
 * 字典转换功能使用说明
 *
 * 1. 字典转换工具类：DictConvertUtil
 *    - 提供各种字典类型的转换方法
 *    - 支持通用转换和特定字段转换
 *
 * 2. 在实体类中添加标签字段：
 *    - 原字段：difficulty (字典值)
 *    - 新字段：difficultyLabel (字典标签)
 *
 * 3. 在Service层调用转换：
 *    - 查询数据后调用 setDictLabels() 方法
 *    - 自动完成所有字典字段的转换
 *
 * 使用示例：
 *
 * // 1. 直接使用工具类转换单个值
 * String difficultyLabel = DictConvertUtil.convertDifficulty("1"); // 返回 "一般"
 *
 * // 2. 通用转换方法
 * String label = DictConvertUtil.convertToLabel("caseDifficulty", "1"); // 返回 "一般"
 *
 * // 3. 在实体对象中自动转换
 * DeptCaseDetailResp resp = new DeptCaseDetailResp();
 * resp.setDifficulty("1");
 * resp.setDictLabels(); // 自动设置 difficultyLabel = "一般"
 *
 * 支持的字典类型：
 * - caseDifficulty: 难度等级 (1-一般, 2-简单, 3-困难)
 * - caseCategory: 是否典型
 * - sourceType: 来源
 * - followStatus: 随访状态
 * - qualityMatch: 定性匹配
 * - positionMatch: 定位匹配
 * - patientType: 就诊类型
 * - studyState: 检查状态
 * - isPositive: 阳性/阴性
 * - isPublic: 是否公有
 * - followType: 随访类型
 */

