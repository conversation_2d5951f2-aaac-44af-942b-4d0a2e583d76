package com.jusha.caselibrary.system.service.impl;

import com.jusha.caselibrary.common.util.LoginUtil;
import com.jusha.caselibrary.mybatisplus.system.entity.DiseaseOverview;
import com.jusha.caselibrary.mybatisplus.system.service.DiseaseOverviewService;
import com.jusha.caselibrary.system.dto.req.DiseaseOverviewSaveOrUpdate;
import com.jusha.caselibrary.system.dto.resp.DiseaseOverviewResp;
import com.jusha.caselibrary.system.service.SysDiseaseOverviewService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 疾病概述管理模块 服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysDiseaseOverviewServiceImpl implements SysDiseaseOverviewService {
    private static final Logger log = LoggerFactory.getLogger(SysDiseaseOverviewServiceImpl.class);

    private final DiseaseOverviewService diseaseOverviewService;

    /**
     * 根据疾病ID查询信息
     * @param diseaseId 疾病ID 疾病概述直接用疾病的ID做主键
     * @return 疾病概述信息
     */
    @Override
    public DiseaseOverviewResp selectDiseaseById(Long diseaseId) {
        DiseaseOverview diseaseOverview = diseaseOverviewService.getById(diseaseId);
        DiseaseOverviewResp diseaseOverviewResp = new DiseaseOverviewResp();
        diseaseOverviewResp.setDiseaseId(diseaseOverview.getDiseaseId()).setOverview(diseaseOverview.getOverview())
                .setPathology(diseaseOverview.getPathology()).setClinical(diseaseOverview.getClinical())
                .setImaging(diseaseOverview.getImaging()).setDiagnosis(diseaseOverview.getDiagnosis())
                .setDifferential(diseaseOverview.getDifferential());
        if(diseaseOverview.getKeyframe()!=null){
            List<String> keyframes = Arrays.asList(diseaseOverview.getKeyframe().split(","));
            diseaseOverviewResp.setKeyframe(keyframes);
        }
        return diseaseOverviewResp;
    }

    /**
     * 新增保存疾病信息
     *
     * @param diseaseOverviewSaveOrUpdate 疾病概述信息
     * @return 结果
     */
    @Override
    public DiseaseOverview editDiseaseOverview(DiseaseOverviewSaveOrUpdate diseaseOverviewSaveOrUpdate) {
        DiseaseOverview diseaseOverview = diseaseOverviewService.getById(diseaseOverviewSaveOrUpdate.getDiseaseId());
        if(diseaseOverview == null){
            //没有，需要新增
            diseaseOverview = new DiseaseOverview();
            diseaseOverview.setDiseaseId(diseaseOverviewSaveOrUpdate.getDiseaseId());
            diseaseOverview.setCreateTime(new Date());
            diseaseOverview.setCreatedBy(LoginUtil.getLoginUserId());
        }else {
            diseaseOverview.setUpdateTime(new Date());
            diseaseOverview.setUpdateBy(LoginUtil.getLoginUserId());
        }
        if(diseaseOverviewSaveOrUpdate.getOverview()!=null){
            diseaseOverview.setOverview(diseaseOverviewSaveOrUpdate.getOverview());
        }
        if(diseaseOverviewSaveOrUpdate.getPathology()!=null){
            diseaseOverview.setPathology(diseaseOverviewSaveOrUpdate.getPathology());
        }
        if(diseaseOverviewSaveOrUpdate.getClinical()!=null){
            diseaseOverview.setClinical(diseaseOverviewSaveOrUpdate.getClinical());
        }
        if(diseaseOverviewSaveOrUpdate.getImaging()!=null){
            diseaseOverview.setImaging(diseaseOverviewSaveOrUpdate.getImaging());
        }
        if(diseaseOverviewSaveOrUpdate.getDiagnosis()!=null){
            diseaseOverview.setDiagnosis(diseaseOverviewSaveOrUpdate.getDiagnosis());
        }
        if(diseaseOverviewSaveOrUpdate.getDifferential()!=null){
            diseaseOverview.setDifferential(diseaseOverviewSaveOrUpdate.getDifferential());
        }
        if(diseaseOverviewSaveOrUpdate.getKeyframe()!=null){
            diseaseOverview.setKeyframe(String.join(",", diseaseOverviewSaveOrUpdate.getKeyframe()));
        }
        diseaseOverviewService.saveOrUpdate(diseaseOverview);
        return diseaseOverviewService.getById(diseaseOverview.getDiseaseId());
    }

    /**
     * 删除疾病信息
     *
     * @param overviewId 疾病概述ID
     * @return 结果
     */
    @Override
    public boolean deleteDiseaseById(Long overviewId) {
        return diseaseOverviewService.removeById(overviewId);
    }
}