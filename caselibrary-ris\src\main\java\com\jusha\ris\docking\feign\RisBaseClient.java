package com.jusha.ris.docking.feign;

import com.jusha.ris.docking.common.resp.ResultBean;
import com.jusha.ris.docking.mock.dto.HospitalInfoResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @title RisBaseClient
 * @description
 * @date 2025/2/27
 */
@Component
@FeignClient(name = "ris-base", path = "/ris-base")
public interface RisBaseClient {

    /**
     * 查询医院列表
     *
     * @return
     */
    @GetMapping("/forbid/hospital/list")
    ResultBean<List<HospitalInfoResp>> searchHospitalListForbid();

}
