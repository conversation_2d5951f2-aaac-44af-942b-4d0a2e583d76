package com.jusha.caselibrary.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 标签表
 * <AUTHOR>
 */
@TableName("t_tag")
@Data
public class Tag {

    @ApiModelProperty(value = "标签ID")
    @TableId("tag_id")
    private Long tagId;    

    @ApiModelProperty(value = "标签名称")
    @TableField("tag_name")
    private String tagName;    

}
