# 个人病例库目录操作API文档

## 概述

本文档描述了个人病例库目录操作的API接口设计，基于"目录下操作"的设计理念和"每个人的目录ID都是唯一的"原则，提供在指定目录下创建和删除个人病例的功能，包括单个操作和批量操作。

## 设计理念

### 核心设计原则

#### 1. "每个人的目录ID都是唯一的"
API设计完全基于catalogId进行管理，移除了对userId的依赖：
- **catalogId唯一性**：每个用户的目录ID在系统中都是唯一的
- **无需userId**：所有API接口只需要catalogId，不再需要传递userId
- **简化参数**：减少了API参数的复杂性，提升了接口的易用性

#### 2. "目录下操作"概念
强调个人病例的创建和删除都是在特定目录下进行的操作：
- **在目录下创建病例**：明确病例创建时的目录归属
- **从目录删除病例**：针对特定目录进行删除操作
- **智能关联管理**：系统自动维护病例与目录的关联关系

#### 3. 批量操作支持
提供高效的批量操作能力，提升用户体验和系统性能：
- **批量创建**：一次性将多个病例添加到同一目录
- **批量删除**：一次性从目录中删除多个病例
- **性能优化**：减少网络请求次数，提升操作效率

### API设计原则

1. **语义清晰**：接口路径和方法名明确表达操作意图
2. **参数简洁**：基于catalogId唯一性，只需要核心参数
3. **响应统一**：使用标准的Result响应格式
4. **异常处理**：完善的错误处理和异常信息返回
5. **批量支持**：提供单个和批量两种操作模式
6. **向后兼容**：保持API设计的一致性和可扩展性

## API接口

### 单个操作接口

#### 1. 在目录下创建病例

#### 接口信息
- **URL**: `/personal/case/create-in-catalog`
- **方法**: `POST`
- **描述**: 在指定目录下创建个人病例关联

#### 请求参数

```json
{
  "caseId": 1001,
  "catalogId": 2001
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| caseId | Long | 是 | 个人病例ID |
| catalogId | Long | 是 | 目录ID |

#### 响应格式

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

**失败响应**:
```json
{
  "code": 400,
  "message": "参数校验失败: 病例ID和目录ID不能为空",
  "data": false
}
```

#### 业务逻辑

1. **参数校验**: 验证caseId和catalogId不能为空
2. **重复检查**: 检查病例与目录的关联是否已存在
3. **创建关联**: 在`user_case_catalog`表中创建新的关联记录
4. **ES同步**: 通过`@ESSync`注解自动触发ES同步，更新catalogIds字段

#### 示例代码

```java
@PostMapping("/create-in-catalog")
public Result<Boolean> createInCatalog(@RequestBody @Valid CatalogOperationRequest request) {
    try {
        boolean success = caseManagementService.createPersonalCaseInCatalog(
            request.getCaseId(), 
            request.getCatalogId()
        );
        return Result.success(success);
    } catch (IllegalArgumentException e) {
        return Result.error(400, e.getMessage());
    } catch (Exception e) {
        log.error("在目录下创建病例失败", e);
        return Result.error(500, "操作失败: " + e.getMessage());
    }
}
```

#### 2. 从目录删除病例

#### 接口信息
- **URL**: `/personal/case/delete-from-catalog`
- **方法**: `DELETE`
- **描述**: 从指定目录删除个人病例关联

#### 请求参数

```json
{
  "caseId": 1001,
  "catalogId": 2001
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| caseId | Long | 是 | 个人病例ID |
| catalogId | Long | 是 | 目录ID |

#### 响应格式

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

**失败响应**:
```json
{
  "code": 400,
  "message": "参数校验失败: 病例ID和目录ID不能为空",
  "data": false
}
```

#### 业务逻辑

1. **参数校验**: 验证caseId和catalogId不能为空
2. **删除关联**: 从`user_case_catalog`表中删除对应的关联记录
3. **ES同步**: 通过`@ESSync`注解自动触发ES同步，智能更新catalogIds字段
4. **智能决策**: 如果病例没有剩余目录关联，则从ES中完全删除该文档

#### 示例代码

```java
@DeleteMapping("/delete-from-catalog")
public Result<Boolean> deleteFromCatalog(@RequestBody @Valid CatalogOperationRequest request) {
    try {
        boolean success = caseManagementService.deletePersonalCaseFromCatalog(
            request.getCaseId(), 
            request.getCatalogId()
        );
        return Result.success(success);
### 批量操作接口

#### 3. 批量在目录下创建病例

##### 接口信息
- **URL**: `/personal/case/batch-create-in-catalog`
- **方法**: `POST`
- **描述**: 批量在指定目录下创建多个个人病例关联

##### 请求参数

```json
{
  "caseIds": [1001, 1002, 1003],
  "catalogId": 2001
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| caseIds | List<Long> | 是 | 个人病例ID列表 |
| catalogId | Long | 是 | 目录ID |

##### 响应格式

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

**失败响应**:
```json
{
  "code": 400,
  "message": "参数校验失败: 病例ID列表不能为空",
  "data": false
}
```

##### 业务逻辑

1. **参数校验**: 验证caseIds列表不能为空，catalogId不能为空
2. **批量处理**: 遍历caseIds列表，为每个病例创建与目录的关联
3. **重复检查**: 检查每个病例与目录的关联是否已存在，避免重复创建
4. **事务处理**: 使用数据库事务确保批量操作的原子性
5. **ES同步**: 通过`@ESSync`注解自动触发批量ES同步

##### 示例代码

```java
@PostMapping("/batch-create-in-catalog")
public Result<Boolean> batchCreateInCatalog(@RequestBody @Valid BatchCatalogOperationRequest request) {
    try {
        boolean success = caseManagementService.batchCreatePersonalCaseInCatalog(
            request.getCaseIds(), 
            request.getCatalogId()
        );
        return Result.success(success);
    } catch (IllegalArgumentException e) {
        return Result.error(400, e.getMessage());
    } catch (Exception e) {
        log.error("批量在目录下创建病例失败", e);
        return Result.error(500, "操作失败: " + e.getMessage());
    }
}
```

#### 4. 批量从目录删除病例

##### 接口信息
- **URL**: `/personal/case/batch-delete-from-catalog`
- **方法**: `DELETE`
- **描述**: 批量从指定目录删除多个个人病例关联

##### 请求参数

```json
{
  "caseIds": [1001, 1002, 1003],
  "catalogId": 2001
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| caseIds | List<Long> | 是 | 个人病例ID列表 |
| catalogId | Long | 是 | 目录ID |

##### 响应格式

**成功响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

**失败响应**:
```json
{
  "code": 400,
  "message": "参数校验失败: 病例ID列表不能为空",
  "data": false
}
```

##### 业务逻辑

1. **参数校验**: 验证caseIds列表不能为空，catalogId不能为空
2. **批量删除**: 遍历caseIds列表，删除每个病例与目录的关联
3. **事务处理**: 使用数据库事务确保批量操作的原子性
4. **ES同步**: 通过`@ESSync`注解自动触发批量ES同步
5. **智能决策**: 对于每个病例，如果没有剩余目录关联，则从ES中完全删除该文档

##### 示例代码

```java
@DeleteMapping("/batch-delete-from-catalog")
public Result<Boolean> batchDeleteFromCatalog(@RequestBody @Valid BatchCatalogOperationRequest request) {
    try {
        boolean success = caseManagementService.batchDeletePersonalCaseFromCatalog(
            request.getCaseIds(), 
            request.getCatalogId()
        );
        return Result.success(success);
    } catch (IllegalArgumentException e) {
        return Result.error(400, e.getMessage());
    } catch (Exception e) {
        log.error("批量从目录删除病例失败", e);
        return Result.error(500, "操作失败: " + e.getMessage());
    }
}
```

## 数据传输对象

### 单个操作请求实体

#### CatalogOperationRequest

```java
@Data
public class CatalogOperationRequest {
    
    @NotNull(message = "病例ID不能为空")
    private Long caseId;
    
    @NotNull(message = "目录ID不能为空")
    private Long catalogId;
    
    // 注意：移除了userId字段，体现"每个人的目录ID都是唯一的"设计理念
}
```

### 批量操作请求实体

#### BatchCatalogOperationRequest

```java
@Data
public class BatchCatalogOperationRequest {
    
    @NotEmpty(message = "病例ID列表不能为空")
    private List<Long> caseIds;
    
    @NotNull(message = "目录ID不能为空")
    private Long catalogId;
    
    // 注意：同样移除了userId字段，基于catalogId唯一性进行操作
}
```

### Result响应格式

```java
@Data
public class Result<T> {
    private Integer code;
    private String message;
    private T data;
    
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("操作成功");
        result.setData(data);
        return result;
    }
    
    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.setCode(500);
        result.setMessage(message);
        return result;
    }
    
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}
```

## ES同步机制

### 自动同步

两个API接口都通过`@ESSync`注解实现自动ES同步：

```java
@Service
public class CaseManagementServiceImpl implements CaseManagementService {
    
    @ESSync(operation = Constant.ES_OPERATION_CREATE)
    @Override
    public boolean createPersonalCaseInCatalog(Long caseId, Long catalogId) {
        // 业务逻辑实现
    }
    
    @ESSync(operation = Constant.ES_OPERATION_DELETE)
    @Override
    public boolean deletePersonalCaseFromCatalog(Long caseId, Long catalogId) {
        // 业务逻辑实现
    }
}
```

### catalogIds智能管理

ES同步服务会智能管理个人病例文档的catalogIds字段：

1. **CREATE操作**: 查询数据库获取该病例的所有目录关联，更新ES文档的catalogIds字段
2. **DELETE操作**: 查询删除后剩余的目录关联，如果为空则删除整个ES文档，否则更新catalogIds字段

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 200 | 操作成功 | - |
| 400 | 参数校验失败 | 检查请求参数是否完整且格式正确 |
| 404 | 资源不存在 | 检查病例ID或目录ID是否存在 |
| 500 | 服务器内部错误 | 检查服务器日志，联系技术支持 |

## 使用示例

### JavaScript/Ajax调用

```javascript
// 在目录下创建病例
function createCaseInCatalog(caseId, catalogId) {
    $.ajax({
        url: '/personal/case/create-in-catalog',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            caseId: caseId,
            catalogId: catalogId
        }),
        success: function(response) {
            if (response.code === 200 && response.data) {
                console.log('病例创建成功');
            } else {
                console.error('病例创建失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
        }
    });
}

// 从目录删除病例
function deleteCaseFromCatalog(caseId, catalogId) {
    $.ajax({
        url: '/personal/case/delete-from-catalog',
        type: 'DELETE',
        contentType: 'application/json',
        data: JSON.stringify({
            caseId: caseId,
            catalogId: catalogId
        }),
        success: function(response) {
            if (response.code === 200 && response.data) {
                console.log('病例删除成功');
            } else {
                console.error('病例删除失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
        }
    });
}
```

### Java客户端调用

```java
@Service
public class PersonalCaseApiClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public boolean createCaseInCatalog(Long caseId, Long catalogId) {
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(caseId);
        request.setCatalogId(catalogId);
        
        try {
            ResponseEntity<Result> response = restTemplate.postForEntity(
                "/personal/case/create-in-catalog", 
                request, 
                Result.class
            );
            
            return response.getBody().getCode() == 200 && 
                   Boolean.TRUE.equals(response.getBody().getData());
        } catch (Exception e) {
            log.error("创建病例失败", e);
            return false;
        }
    }
    
    public boolean deleteCaseFromCatalog(Long caseId, Long catalogId) {
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(caseId);
        request.setCatalogId(catalogId);
        
        try {
            HttpEntity<CatalogOperationRequest> entity = new HttpEntity<>(request);
            ResponseEntity<Result> response = restTemplate.exchange(
                "/personal/case/delete-from-catalog",
                HttpMethod.DELETE,
                entity,
                Result.class
            );
            
            return response.getBody().getCode() == 200 && 
                   Boolean.TRUE.equals(response.getBody().getData());
        } catch (Exception e) {
            log.error("删除病例失败", e);
            return false;
        }
    }
}
```

## 测试用例

### 单元测试

```java
@WebMvcTest(PersonalCaseController.class)
public class PersonalCaseControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private CaseManagementService caseManagementService;
    
    @Test
    public void testCreatePersonalCaseInCatalog() throws Exception {
        // 准备测试数据
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法
        when(caseManagementService.createPersonalCaseInCatalog(anyLong(), anyLong()))
            .thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/personal/case/create-in-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));
    }
    
    @Test
    public void testDeletePersonalCaseFromCatalog() throws Exception {
        // 准备测试数据
        CatalogOperationRequest request = new CatalogOperationRequest();
        request.setCaseId(1L);
        request.setCatalogId(100L);

        // Mock服务方法
        when(caseManagementService.deletePersonalCaseFromCatalog(anyLong(), anyLong()))
            .thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/personal/case/delete-from-catalog")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));
    }
}
```

## 性能考虑

### 数据库优化

1. **索引优化**: 在`user_case_catalog`表的`(case_id, catalog_id)`上建立唯一索引
2. **批量操作**: 支持批量创建和删除操作以提高性能
3. **连接池**: 合理配置数据库连接池参数

### ES同步优化

1. **异步同步**: ES同步操作采用异步方式，不阻塞主业务流程
2. **批量更新**: 对于大量操作，考虑使用ES的批量更新API
3. **错误重试**: 内置重试机制处理ES同步失败的情况

## 监控和日志

### 日志记录

```java
@Slf4j
@RestController
public class PersonalCaseController {
    
    @PostMapping("/create-in-catalog")
    public Result<Boolean> createInCatalog(@RequestBody @Valid CatalogOperationRequest request) {
        log.info("开始在目录下创建病例: caseId={}, catalogId={}", 
                request.getCaseId(), request.getCatalogId());
        
        try {
            boolean success = caseManagementService.createPersonalCaseInCatalog(
                request.getCaseId(), request.getCatalogId());
            
            log.info("在目录下创建病例完成: caseId={}, catalogId={}, success={}", 
                    request.getCaseId(), request.getCatalogId(), success);
            
            return Result.success(success);
        } catch (Exception e) {
            log.error("在目录下创建病例失败: caseId={}, catalogId={}", 
                     request.getCaseId(), request.getCatalogId(), e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
}
```

### 监控指标

建议监控以下关键指标：

1. **API响应时间**: 监控接口的平均响应时间和P99响应时间
2. **成功率**: 监控API调用的成功率
3. **ES同步延迟**: 监控ES同步的延迟时间
4. **数据库连接**: 监控数据库连接池的使用情况

## 注意事项

1. **幂等性**: 创建操作具有幂等性，重复调用不会产生副作用
2. **事务一致性**: 数据库操作和ES同步保持最终一致性
3. **并发控制**: 注意多线程环境下的数据一致性问题
4. **参数校验**: 严格的参数校验确保数据完整性
5. **异常处理**: 完善的异常处理机制提供友好的错误信息

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0.0 | 2025-07-11 | 初始版本，实现基于"目录下操作"理念的API设计 |

## 联系方式

如有问题或建议，请联系开发团队。
#### 批量操作示例

```javascript
// 批量在目录下创建病例
function batchCreateCasesInCatalog(caseIds, catalogId) {
    $.ajax({
        url: '/personal/case/batch-create-in-catalog',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            caseIds: caseIds,
            catalogId: catalogId
            // 注意：批量操作同样不需要userId
        }),
        success: function(response) {
            if (response.code === 200 && response.data) {
                console.log('批量创建病例成功，共处理 ' + caseIds.length + ' 个病例');
            } else {
                console.error('批量创建病例失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('批量请求失败:', error);
        }
    });
}

// 批量从目录删除病例
function batchDeleteCasesFromCatalog(caseIds, catalogId) {
    $.ajax({
        url: '/personal/case/batch-delete-from-catalog',
        type: 'DELETE',
        contentType: 'application/json',
        data: JSON.stringify({
            caseIds: caseIds,
            catalogId: catalogId
        }),
        success: function(response) {
            if (response.code === 200 && response.data) {
                console.log('批量删除病例成功，共处理 ' + caseIds.length + ' 个病例');
            } else {
                console.error('批量删除病例失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('批量请求失败:', error);
        }
    });
}

// 实际使用示例：批量操作多个选中的病例
function handleBatchOperation(selectedCaseIds, targetCatalogId, operation) {
    if (!selectedCaseIds || selectedCaseIds.length === 0) {
        alert('请选择要操作的病例');
        return;
    }
    
    if (operation === 'create') {
        batchCreateCasesInCatalog(selectedCaseIds, targetCatalogId);
    } else if (operation === 'delete') {
        batchDeleteCasesFromCatalog(selectedCaseIds, targetCatalogId);
    }
}
```

#### 批量操作Java客户端

```java
@Service
public class PersonalCaseBatchApiClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public boolean batchCreateCasesInCatalog(List<Long> caseIds, Long catalogId) {
        BatchCatalogOperationRequest request = new BatchCatalogOperationRequest();
        request.setCaseIds(caseIds);
        request.setCatalogId(catalogId);
        // 注意：批量操作同样不需要userId
        
        try {
            ResponseEntity<Result> response = restTemplate.postForEntity(
                "/personal/case/batch-create-in-catalog", 
                request, 
                Result.class
            );
            
            return response.getBody().getCode() == 200 && 
                   Boolean.TRUE.equals(response.getBody().getData());
        } catch (Exception e) {
            log.error("批量创建病例失败", e);
            return false;
        }
    }
    
    public boolean batchDeleteCasesFromCatalog(List<Long> caseIds, Long catalogId) {
        BatchCatalogOperationRequest request = new BatchCatalogOperationRequest();
        request.setCaseIds(caseIds);
        request.setCatalogId(catalogId);
        
        try {
            HttpEntity<BatchCatalogOperationRequest> entity = new HttpEntity<>(request);
            ResponseEntity<Result> response = restTemplate.exchange(
                "/personal/case/batch-delete-from-catalog",
                HttpMethod.DELETE,
                entity,
                Result.class
            );
            
            return response.getBody().getCode() == 200 && 
                   Boolean.TRUE.equals(response.getBody().getData());
        } catch (Exception e) {
            log.error("批量删除病例失败", e);
            return false;
        }
    }
    
    /**
     * 便捷方法：根据操作类型执行批量操作
     */
    public boolean executeBatchOperation(List<Long> caseIds, Long catalogId, String operation) {
        if (caseIds == null || caseIds.isEmpty()) {
            throw new IllegalArgumentException("病例ID列表不能为空");
        }
        
        switch (operation.toLowerCase()) {
            case "create":
                return batchCreateCasesInCatalog(caseIds, catalogId);
            case "delete":
                return batchDeleteCasesFromCatalog(caseIds, catalogId);
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + operation);
        }
    }
}
```

## 性能考虑

### 数据库优化

1. **索引优化**: 在`user_case_catalog`表的`(case_id, catalog_id)`上建立唯一索引
2. **批量操作**: 支持批量创建和删除操作以提高性能
3. **连接池**: 合理配置数据库连接池参数
4. **事务管理**: 批量操作使用事务确保数据一致性

### ES同步优化

1. **异步同步**: ES同步操作采用异步方式，不阻塞主业务流程
2. **批量更新**: 对于大量操作，考虑使用ES的批量更新API
3. **错误重试**: 内置重试机制处理ES同步失败的情况
4. **批量同步**: 新增的批量操作支持批量ES同步，提升性能

### 批量操作优化

1. **批量大小限制**: 建议单次批量操作不超过1000个病例
2. **分页处理**: 对于大量数据，采用分页批量处理
3. **并发控制**: 合理控制批量操作的并发数量
4. **内存管理**: 注意批量操作时的内存使用情况

## 注意事项

1. **幂等性**: 创建操作具有幂等性，重复调用不会产生副作用
2. **事务一致性**: 数据库操作和ES同步保持最终一致性
3. **并发控制**: 注意多线程环境下的数据一致性问题
4. **参数校验**: 严格的参数校验确保数据完整性
5. **异常处理**: 完善的异常处理机制提供友好的错误信息
6. **catalogId唯一性**: 确保catalogId在系统中的唯一性，这是移除userId依赖的基础
7. **批量操作限制**: 考虑设置合理的批量操作大小限制，避免单次操作过大影响系统性能
8. **向后兼容**: 如果有旧的API仍在使用userId，需要提供兼容性支持或迁移方案

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0.0 | 2025-07-11 | 初始版本，实现基于"目录下操作"理念的API设计 |
| 2.0.0 | 2025-07-12 | 重大更新：移除userId依赖，基于"每个人的目录ID都是唯一的"原则重新设计；新增批量操作接口支持；完善测试用例和文档 |

## 迁移指南

### 从userId依赖迁移到catalogId

1. **API更新**：更新所有API接口，移除userId参数
2. **客户端更新**：更新前端和其他客户端代码
3. **测试验证**：确保所有功能在移除userId后正常工作
4. **数据验证**：验证catalogId的唯一性和正确性
5. **性能测试**：验证批量操作的性能表现

### 批量操作集成

1. **接口集成**：集成新的批量操作API
2. **前端适配**：更新前端界面支持批量选择和操作
3. **性能测试**：测试批量操作在不同数据量下的性能
4. **错误处理**：完善批量操作的错误处理和用户反馈

## 联系方式

如有问题或建议，请联系开发团队。