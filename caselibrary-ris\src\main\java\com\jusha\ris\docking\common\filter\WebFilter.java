package com.jusha.ris.docking.common.filter;

import com.jusha.ris.docking.common.constant.Constant;
import com.jusha.ris.docking.common.util.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 过滤器
 */
@Slf4j
@Component
public class WebFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, Filter<PERSON>hain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        //traceId写入MDC
        String traceId = LoginUtil.getRequestHeader(Constant.TRACEID);
        if(StringUtils.isBlank(traceId)){
            traceId = StringUtils.join(RandomStringUtils.randomAlphanumeric(4), RandomStringUtils.randomNumeric(16));
        }
        MDC.put(Constant.TRACEID, traceId);

        filterChain.doFilter(servletRequest, servletResponse);

        //清除LoginUtil中的缓存
        LoginUtil.clearCache();
        //清除MDC
        MDC.remove(Constant.TRACEID);
    }

    @Override
    public void destroy() {
    }

}